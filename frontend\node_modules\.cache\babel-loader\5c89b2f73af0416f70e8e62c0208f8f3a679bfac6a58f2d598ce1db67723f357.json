{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  // Data News Component - Fixed Version\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [themeSettings, setThemeSettings] = useState({\n    primary_color: '#3B82F6',\n    secondary_color: '#10B981',\n    accent_color: '#F59E0B'\n  });\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: 'React News Portal'\n  });\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n    // Fetch kostum data\n    fetchKostumData();\n    // Load theme settings\n    loadThemeSettings();\n  }, [id]);\n\n  // Load theme settings from admin\n  const loadThemeSettings = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const text = await response.text();\n      console.log('Raw theme response:', text);\n      let data;\n      try {\n        data = JSON.parse(text);\n      } catch (e) {\n        console.error('JSON parse error:', e);\n        console.error('Response text:', text);\n        throw new Error('Invalid JSON response');\n      }\n      if (data.success && data.data) {\n        setThemeSettings({\n          primary_color: data.data.primary_color || '#3B82F6',\n          secondary_color: data.data.secondary_color || '#10B981',\n          accent_color: data.data.accent_color || '#F59E0B'\n        });\n        console.log('🎨 Theme settings loaded:', data.data);\n      }\n    } catch (error) {\n      console.error('❌ Error loading theme settings:', error);\n    }\n  };\n  const fetchKostumData = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      const data = await response.json();\n      if (data.success && data.data) {\n        const settings = data.data;\n        setKostum({\n          logo: settings.website_logo || '',\n          title: settings.website_name || 'React News Portal'\n        });\n      }\n    } catch (error) {\n      // Fallback to default values if API fails\n      setKostum({\n        logo: '',\n        title: 'React News Portal'\n      });\n    }\n  };\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // Fetch news detail (views already incremented from landing page)\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n    console.log('data-news.js - Processing image path:', imagePath);\n\n    // Jika sudah URL lengkap, gunakan langsung\n    if (imagePath.startsWith('http')) {\n      console.log('data-news.js - Using full URL:', imagePath);\n      return imagePath;\n    }\n\n    // If it's already the correct path, use it directly\n    if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n      const finalUrl = `http://localhost${imagePath}`;\n      console.log('data-news.js - Using direct path:', finalUrl);\n      return finalUrl;\n    }\n\n    // Extract filename from any path format\n    let filename = '';\n    if (imagePath.startsWith('/react-news/uploads/')) {\n      filename = imagePath.replace('/react-news/uploads/', '');\n    } else if (imagePath.startsWith('/uploads/')) {\n      filename = imagePath.replace('/uploads/', '');\n    } else if (imagePath.startsWith('assets/news/')) {\n      filename = imagePath.replace('assets/news/', '');\n    } else if (!imagePath.includes('/')) {\n      // Just filename\n      filename = imagePath;\n    } else {\n      // Extract filename from any other path\n      filename = imagePath.split('/').pop();\n    }\n\n    // Use consistent frontend/uploads path\n    const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n    console.log('data-news.js - Final URL:', finalUrl);\n    return finalUrl;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\",\n          style: {\n            borderBottomColor: themeSettings.primary_color\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color,\n            ':hover': {\n              backgroundColor: themeSettings.secondary_color\n            }\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'gray.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: {\n            xs: 70,\n            md: 80\n          },\n          px: {\n            xs: 2,\n            md: 6\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: {\n                xs: 22,\n                md: 28\n              }\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/'),\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'white',\n        boxShadow: 1,\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        mt: {\n          xs: '70px',\n          md: '80px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 2,\n          py: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            fontSize: '14px',\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"button\",\n            onClick: handleBackToHome,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main'\n              },\n              transition: 'color 0.2s',\n              border: 'none',\n              background: 'none',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home\",\n              style: {\n                marginRight: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right\",\n            style: {\n              fontSize: '12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: '1200px',\n        mx: 'auto',\n        px: 2,\n        py: 4,\n        pb: {\n          xs: 12,\n          md: 4\n        } // More padding on mobile for bottom nav\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            lg: '2fr 1fr'\n          },\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://picsum.photos/800/400?random=2';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  style: {\n                    backgroundColor: themeSettings.primary_color\n                  },\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                    style: {\n                      backgroundColor: themeSettings.primary_color\n                    },\n                    onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n                    onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                    style: {\n                      backgroundColor: themeSettings.accent_color\n                    },\n                    onMouseEnter: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                    onMouseLeave: e => e.target.style.backgroundColor = themeSettings.accent_color,\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2\",\n                style: {\n                  color: themeSettings.primary_color\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: 'white',\n              borderRadius: 2,\n              boxShadow: 2,\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"button\",\n              onClick: handleBackToHome,\n              sx: {\n                width: '100%',\n                bgcolor: 'primary.main',\n                color: 'white',\n                py: 1.5,\n                px: 2,\n                borderRadius: 2,\n                border: 'none',\n                cursor: 'pointer',\n                fontWeight: 500,\n                transition: 'background-color 0.2s',\n                '&:hover': {\n                  bgcolor: 'primary.dark'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        // Hide on desktop\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"sDau4pGRAX1/GyVsTHECO1kU39M=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "IconButton", "MenuIcon", "useMediaQuery", "useTheme", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "theme", "isDesktop", "breakpoints", "up", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "themeSettings", "setThemeSettings", "primary_color", "secondary_color", "accent_color", "kostum", "setKostum", "logo", "title", "sidebarOpen", "setSidebarOpen", "fetchNewsDetail", "fetchKostumData", "loadThemeSettings", "response", "fetch", "ok", "Error", "status", "text", "console", "log", "data", "JSON", "parse", "e", "success", "json", "settings", "website_logo", "website_name", "newsId", "category_id", "fetchRelatedNews", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "finalUrl", "filename", "replace", "includes", "split", "pop", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "style", "borderBottomColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backgroundColor", "onMouseEnter", "target", "onMouseLeave", "sx", "minHeight", "bgcolor", "width", "overflow", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "xs", "md", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "variant", "fontWeight", "fontSize", "edge", "boxShadow", "mt", "max<PERSON><PERSON><PERSON>", "mx", "py", "gap", "component", "transition", "border", "background", "cursor", "marginRight", "pb", "gridTemplateColumns", "lg", "image", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "length", "map", "borderRadius", "p", "left", "right", "bottom", "borderTop", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\n\nconst DataNews = () => {\n    // Data News Component - Fixed Version\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const theme = useTheme();\n    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [themeSettings, setThemeSettings] = useState({\n        primary_color: '#3B82F6',\n        secondary_color: '#10B981',\n        accent_color: '#F59E0B'\n    });\n    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });\n    const [sidebarOpen, setSidebarOpen] = useState(false);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n        // Fetch kostum data\n        fetchKostumData();\n        // Load theme settings\n        loadThemeSettings();\n    }, [id]);\n\n    // Load theme settings from admin\n    const loadThemeSettings = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const text = await response.text();\n            console.log('Raw theme response:', text);\n\n            let data;\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                console.error('JSON parse error:', e);\n                console.error('Response text:', text);\n                throw new Error('Invalid JSON response');\n            }\n\n            if (data.success && data.data) {\n                setThemeSettings({\n                    primary_color: data.data.primary_color || '#3B82F6',\n                    secondary_color: data.data.secondary_color || '#10B981',\n                    accent_color: data.data.accent_color || '#F59E0B'\n                });\n                console.log('🎨 Theme settings loaded:', data.data);\n            }\n        } catch (error) {\n            console.error('❌ Error loading theme settings:', error);\n        }\n    };\n\n    const fetchKostumData = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                const settings = data.data;\n                setKostum({\n                    logo: settings.website_logo || '',\n                    title: settings.website_name || 'React News Portal'\n                });\n            }\n        } catch (error) {\n            // Fallback to default values if API fails\n            setKostum({\n                logo: '',\n                title: 'React News Portal'\n            });\n        }\n    };\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // Fetch news detail (views already incremented from landing page)\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n\n        console.log('data-news.js - Processing image path:', imagePath);\n\n        // Jika sudah URL lengkap, gunakan langsung\n        if (imagePath.startsWith('http')) {\n            console.log('data-news.js - Using full URL:', imagePath);\n            return imagePath;\n        }\n\n        // If it's already the correct path, use it directly\n        if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n            const finalUrl = `http://localhost${imagePath}`;\n            console.log('data-news.js - Using direct path:', finalUrl);\n            return finalUrl;\n        }\n\n        // Extract filename from any path format\n        let filename = '';\n\n        if (imagePath.startsWith('/react-news/uploads/')) {\n            filename = imagePath.replace('/react-news/uploads/', '');\n        } else if (imagePath.startsWith('/uploads/')) {\n            filename = imagePath.replace('/uploads/', '');\n        } else if (imagePath.startsWith('assets/news/')) {\n            filename = imagePath.replace('assets/news/', '');\n        } else if (!imagePath.includes('/')) {\n            // Just filename\n            filename = imagePath;\n        } else {\n            // Extract filename from any other path\n            filename = imagePath.split('/').pop();\n        }\n\n        // Use consistent frontend/uploads path\n        const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n        console.log('data-news.js - Final URL:', finalUrl);\n        return finalUrl;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div\n                        className=\"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\"\n                        style={{ borderBottomColor: themeSettings.primary_color }}\n                    ></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color,\n                            ':hover': { backgroundColor: themeSettings.secondary_color }\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"mt-4 text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>\n            {/* Responsive Navigation Bar */}\n            <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 48, height: 48, mr: 2 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/')}\n                        sx={{ mr: 1 }}\n                    >\n                        <MenuIcon fontSize=\"large\" />\n                    </IconButton>\n                </Toolbar>\n            </AppBar>\n\n            {/* Header/Breadcrumb */}\n            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>\n                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>\n                        <Box\n                            component=\"button\"\n                            onClick={handleBackToHome}\n                            sx={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'text.secondary',\n                                '&:hover': { color: 'primary.main' },\n                                transition: 'color 0.2s',\n                                border: 'none',\n                                background: 'none',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            <i className=\"fas fa-home\" style={{ marginRight: '4px' }}></i>\n                            Beranda\n                        </Box>\n                        <i className=\"fas fa-chevron-right\" style={{ fontSize: '12px' }}></i>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n                            Detail Berita\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box sx={{\n                maxWidth: '1200px',\n                mx: 'auto',\n                px: 2,\n                py: 4,\n                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav\n            }}>\n                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>\n                    {/* Main Article */}\n                    <Box>\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://picsum.photos/800/400?random=2';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span\n                                        className=\"text-white px-3 py-1 rounded-full text-sm font-medium\"\n                                        style={{ backgroundColor: themeSettings.primary_color }}\n                                    >\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-calendar-alt mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-clock mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-eye mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.primary_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                        >\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.accent_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.accent_color}\n                                        >\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </Box>\n\n                    {/* Sidebar */}\n                    <Box>\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i\n                                        className=\"fas fa-newspaper mr-2\"\n                                        style={{ color: themeSettings.primary_color }}\n                                    ></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>\n                            <Box\n                                component=\"button\"\n                                onClick={handleBackToHome}\n                                sx={{\n                                    width: '100%',\n                                    bgcolor: 'primary.main',\n                                    color: 'white',\n                                    py: 1.5,\n                                    px: 2,\n                                    borderRadius: 2,\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontWeight: 500,\n                                    transition: 'background-color 0.2s',\n                                    '&:hover': {\n                                        bgcolor: 'primary.dark'\n                                    }\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: '8px' }}></i>\n                                Kembali ke Beranda\n                            </Box>\n                        </Box>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Custom Bottom Navigation - Mobile Only */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: { xs: 'block', md: 'none' }, // Hide on desktop\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </Box>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB;EACA,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,KAAK,GAAGP,QAAQ,CAAC,CAAC;EACxB,MAAMQ,SAAS,GAAGT,aAAa,CAACQ,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC;IAC/CiC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC;IAAEsC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAoB,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZ,IAAIgB,EAAE,EAAE;MACJyB,eAAe,CAACzB,EAAE,CAAC;IACvB;IACA;IACA0B,eAAe,CAAC,CAAC;IACjB;IACAC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC3B,EAAE,CAAC,CAAC;;EAER;EACA,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MAEzH,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC;MAExC,IAAIG,IAAI;MACR,IAAI;QACAA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOM,CAAC,EAAE;QACRL,OAAO,CAACtB,KAAK,CAAC,mBAAmB,EAAE2B,CAAC,CAAC;QACrCL,OAAO,CAACtB,KAAK,CAAC,gBAAgB,EAAEqB,IAAI,CAAC;QACrC,MAAM,IAAIF,KAAK,CAAC,uBAAuB,CAAC;MAC5C;MAEA,IAAIK,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3BrB,gBAAgB,CAAC;UACbC,aAAa,EAAEoB,IAAI,CAACA,IAAI,CAACpB,aAAa,IAAI,SAAS;UACnDC,eAAe,EAAEmB,IAAI,CAACA,IAAI,CAACnB,eAAe,IAAI,SAAS;UACvDC,YAAY,EAAEkB,IAAI,CAACA,IAAI,CAAClB,YAAY,IAAI;QAC5C,CAAC,CAAC;QACFgB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,IAAI,CAACA,IAAI,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MACzH,MAAMO,IAAI,GAAG,MAAMR,QAAQ,CAACa,IAAI,CAAC,CAAC;MAElC,IAAIL,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B,MAAMM,QAAQ,GAAGN,IAAI,CAACA,IAAI;QAC1BhB,SAAS,CAAC;UACNC,IAAI,EAAEqB,QAAQ,CAACC,YAAY,IAAI,EAAE;UACjCrB,KAAK,EAAEoB,QAAQ,CAACE,YAAY,IAAI;QACpC,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACZ;MACAQ,SAAS,CAAC;QACNC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMG,eAAe,GAAG,MAAOoB,MAAM,IAAK;IACtC,IAAI;MACAlC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGgB,MAAM,EAAE,CAAC;MAEtI,IAAI,CAACjB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACa,IAAI,CAAC,CAAC;MAElC,IAAIL,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B7B,OAAO,CAAC6B,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACU,WAAW,EAAE;UACvBC,gBAAgB,CAACX,IAAI,CAACA,IAAI,CAACU,WAAW,EAAED,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHhC,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMoC,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMrB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGmB,UAAU,UAAU,CAAC;MAElJ,IAAI,CAACpB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACa,IAAI,CAAC,CAAC;MAElC,IAAIL,IAAI,CAACI,OAAO,IAAIU,KAAK,CAACC,OAAO,CAACf,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMgB,QAAQ,GAAGhB,IAAI,CAACA,IAAI,CACrBiB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACtD,EAAE,KAAKuD,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB/C,cAAc,CAAC2C,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACH3C,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMgD,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,2CAA2C;IAElExB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuB,SAAS,CAAC;;IAE/D;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9BzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuB,SAAS,CAAC;MACxD,OAAOA,SAAS;IACpB;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,+BAA+B,CAAC,EAAE;MACvD,MAAMC,QAAQ,GAAG,mBAAmBF,SAAS,EAAE;MAC/CxB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyB,QAAQ,CAAC;MAC1D,OAAOA,QAAQ;IACnB;;IAEA;IACA,IAAIC,QAAQ,GAAG,EAAE;IAEjB,IAAIH,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;MAC9CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;IAC5D,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;MAC1CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACjD,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;MAC7CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACpD,CAAC,MAAM,IAAI,CAACJ,SAAS,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACAF,QAAQ,GAAGH,SAAS;IACxB,CAAC,MAAM;MACH;MACAG,QAAQ,GAAGH,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;IACzC;;IAEA;IACA,MAAML,QAAQ,GAAG,gDAAgDC,QAAQ,EAAE;IAC3E3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEyB,QAAQ,CAAC;IAClD,OAAOA,QAAQ;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIlC,MAAM,IAAK;IACvC5C,QAAQ,CAAC,cAAc4C,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B/E,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIS,OAAO,EAAE;IACT,oBACIb,OAAA;MAAKoF,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrErF,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBrF,OAAA;UACIoF,SAAS,EAAC,6DAA6D;UACvEE,KAAK,EAAE;YAAEC,iBAAiB,EAAEtE,aAAa,CAACE;UAAc;QAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACP3F,OAAA;UAAGoF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI5E,KAAK,EAAE;IACP,oBACIf,OAAA;MAAKoF,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrErF,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBrF,OAAA;UAAKoF,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpFrF,OAAA;YAAGoF,SAAS,EAAC;UAAkC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnD5E,KAAK;QAAA;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN3F,OAAA;UACI4F,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,mDAAmD;UAC7DE,KAAK,EAAE;YACHO,eAAe,EAAE5E,aAAa,CAACE,aAAa;YAC5C,QAAQ,EAAE;cAAE0E,eAAe,EAAE5E,aAAa,CAACG;YAAgB;UAC/D,CAAE;UACF0E,YAAY,EAAGpD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACG,eAAgB;UACpF4E,YAAY,EAAGtD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACE,aAAc;UAAAkE,QAAA,gBAElFrF,OAAA;YAAGoF,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAClF,IAAI,EAAE;IACP,oBACIT,OAAA;MAAKoF,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrErF,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBrF,OAAA;UAAGoF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvD3F,OAAA;UACI4F,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,wDAAwD;UAClEE,KAAK,EAAE;YACHO,eAAe,EAAE5E,aAAa,CAACE;UACnC,CAAE;UACF2E,YAAY,EAAGpD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACG,eAAgB;UACpF4E,YAAY,EAAGtD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACE,aAAc;UAAAkE,QAAA,gBAElFrF,OAAA;YAAGoF,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI3F,OAAA,CAACV,GAAG;IAAC2G,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAhB,QAAA,gBAEpFrF,OAAA,CAACR,MAAM;MAAC8G,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACP,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAtB,QAAA,eACnIrF,OAAA,CAACP,OAAO;QAACwG,EAAE,EAAE;UAAEC,SAAS,EAAE;YAAEU,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAAEC,EAAE,EAAE;YAAEF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAAxB,QAAA,gBACjErF,OAAA,CAACV,GAAG;UAAC2G,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBAC5DrF,OAAA,CAACN,MAAM;YACHwH,GAAG,EAAE5F,MAAM,CAACE,IAAI,GAAGoC,WAAW,CAACtC,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;YAC7D2F,GAAG,EAAC,MAAM;YACVlB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEgB,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAG5E,CAAC,IAAK;cAAEA,CAAC,CAACqD,MAAM,CAACmB,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACF3F,OAAA,CAACT,UAAU;YAACgI,OAAO,EAAC,IAAI;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEjB,KAAK,EAAE,cAAc;cAAEkB,QAAQ,EAAE;gBAAEb,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAAE,CAAE;YAAAxB,QAAA,EACjG/D,MAAM,CAACG;UAAK;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACN3F,OAAA,CAACL,UAAU;UACP+H,IAAI,EAAC,KAAK;UACVnB,KAAK,EAAC,SAAS;UACfX,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,GAAG,CAAE;UAC7B6F,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,eAEdrF,OAAA,CAACJ,QAAQ;YAAC6H,QAAQ,EAAC;UAAO;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGT3F,OAAA,CAACV,GAAG;MAAC2G,EAAE,EAAE;QAAEE,OAAO,EAAE,OAAO;QAAEwB,SAAS,EAAE,CAAC;QAAElB,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEkB,EAAE,EAAE;UAAEhB,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO;MAAE,CAAE;MAAAxB,QAAA,eAClHrF,OAAA,CAACV,GAAG;QAAC2G,EAAE,EAAE;UAAE4B,QAAQ,EAAE,QAAQ;UAAEC,EAAE,EAAE,MAAM;UAAEhB,EAAE,EAAE,CAAC;UAAEiB,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,eACtDrF,OAAA,CAACV,GAAG;UAAC2G,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgB,GAAG,EAAE,CAAC;YAAEP,QAAQ,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAiB,CAAE;UAAAlB,QAAA,gBAClGrF,OAAA,CAACV,GAAG;YACA2I,SAAS,EAAC,QAAQ;YAClBrC,OAAO,EAAET,gBAAiB;YAC1Bc,EAAE,EAAE;cACAc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBT,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBAAEA,KAAK,EAAE;cAAe,CAAC;cACpC2B,UAAU,EAAE,YAAY;cACxBC,MAAM,EAAE,MAAM;cACdC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE;YACZ,CAAE;YAAAhD,QAAA,gBAEFrF,OAAA;cAAGoF,SAAS,EAAC,aAAa;cAACE,KAAK,EAAE;gBAAEgD,WAAW,EAAE;cAAM;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAElE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3F,OAAA;YAAGoF,SAAS,EAAC,sBAAsB;YAACE,KAAK,EAAE;cAAEmC,QAAQ,EAAE;YAAO;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE3F,OAAA,CAACT,UAAU;YAACgI,OAAO,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEjB,KAAK,EAAE;YAAe,CAAE;YAAAlB,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3F,OAAA,CAACV,GAAG;MAAC2G,EAAE,EAAE;QACL4B,QAAQ,EAAE,QAAQ;QAClBC,EAAE,EAAE,MAAM;QACVhB,EAAE,EAAE,CAAC;QACLiB,EAAE,EAAE,CAAC;QACLQ,EAAE,EAAE;UAAE3B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,CAAC;MAC1B,CAAE;MAAAxB,QAAA,eACErF,OAAA,CAACV,GAAG;QAAC2G,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEyB,mBAAmB,EAAE;YAAE5B,EAAE,EAAE,KAAK;YAAE6B,EAAE,EAAE;UAAU,CAAC;UAAET,GAAG,EAAE;QAAE,CAAE;QAAA3C,QAAA,gBAEpFrF,OAAA,CAACV,GAAG;UAAA+F,QAAA,eACArF,OAAA;YAASoF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9DrF,OAAA;cAAKoF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrBrF,OAAA;gBACIkH,GAAG,EAAEtD,WAAW,CAACnD,IAAI,CAACiI,KAAK,CAAE;gBAC7BvB,GAAG,EAAE1G,IAAI,CAACgB,KAAM;gBAChB2D,SAAS,EAAC,kCAAkC;gBAC5CkC,OAAO,EAAG5E,CAAC,IAAK;kBACZA,CAAC,CAACqD,MAAM,CAACmB,GAAG,GAAG,wCAAwC;gBAC3D;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACF3F,OAAA;gBAAKoF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClCrF,OAAA;kBACIoF,SAAS,EAAC,uDAAuD;kBACjEE,KAAK,EAAE;oBAAEO,eAAe,EAAE5E,aAAa,CAACE;kBAAc,CAAE;kBAAAkE,QAAA,EAEvD5E,IAAI,CAACkI,aAAa,IAAI;gBAAQ;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN3F,OAAA;cAAKoF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBrF,OAAA;gBAAIoF,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1E5E,IAAI,CAACgB;cAAK;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGL3F,OAAA;gBAAKoF,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvFrF,OAAA;kBAAKoF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BrF,OAAA;oBACIoF,SAAS,EAAC,0BAA0B;oBACpCE,KAAK,EAAE;sBAAEiB,KAAK,EAAEtF,aAAa,CAACE;oBAAc;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJtB,UAAU,CAAC5D,IAAI,CAACmI,UAAU,IAAInI,IAAI,CAAC8D,IAAI,CAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN3F,OAAA;kBAAKoF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BrF,OAAA;oBACIoF,SAAS,EAAC,mBAAmB;oBAC7BE,KAAK,EAAE;sBAAEiB,KAAK,EAAEtF,aAAa,CAACE;oBAAc;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJb,UAAU,CAACrE,IAAI,CAACmI,UAAU,IAAInI,IAAI,CAAC8D,IAAI,CAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN3F,OAAA;kBAAKoF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BrF,OAAA;oBACIoF,SAAS,EAAC,iBAAiB;oBAC3BE,KAAK,EAAE;sBAAEiB,KAAK,EAAEtF,aAAa,CAACE;oBAAc;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJlF,IAAI,CAACoI,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN3F,OAAA;gBAAKoF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtCrF,OAAA;kBACIoF,SAAS,EAAC,mDAAmD;kBAC7D0D,uBAAuB,EAAE;oBACrBC,MAAM,EAAEtI,IAAI,CAACuI,OAAO,CAAC/E,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN3F,OAAA;gBAAKoF,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/BrF,OAAA;kBAAIoF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E3F,OAAA;kBAAKoF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BrF,OAAA;oBACIoF,SAAS,EAAC,mDAAmD;oBAC7DE,KAAK,EAAE;sBAAEO,eAAe,EAAE5E,aAAa,CAACE;oBAAc,CAAE;oBACxD2E,YAAY,EAAGpD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACG,eAAgB;oBACpF4E,YAAY,EAAGtD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACE,aAAc;oBAAAkE,QAAA,gBAElFrF,OAAA;sBAAGoF,SAAS,EAAC;oBAAwB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3F,OAAA;oBACIoF,SAAS,EAAC,mDAAmD;oBAC7DE,KAAK,EAAE;sBAAEO,eAAe,EAAE5E,aAAa,CAACI;oBAAa,CAAE;oBACvDyE,YAAY,EAAGpD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACE,aAAc;oBAClF6E,YAAY,EAAGtD,CAAC,IAAKA,CAAC,CAACqD,MAAM,CAACT,KAAK,CAACO,eAAe,GAAG5E,aAAa,CAACI,YAAa;oBAAAgE,QAAA,gBAEjFrF,OAAA;sBAAGoF,SAAS,EAAC;oBAAqB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3F,OAAA;oBAAQoF,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjGrF,OAAA;sBAAGoF,SAAS,EAAC;oBAAsB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGN3F,OAAA,CAACV,GAAG;UAAA+F,QAAA,GAEC1E,WAAW,CAACsI,MAAM,GAAG,CAAC,iBACnBjJ,OAAA;YAAKoF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnDrF,OAAA;cAAIoF,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpDrF,OAAA;gBACIoF,SAAS,EAAC,uBAAuB;gBACjCE,KAAK,EAAE;kBAAEiB,KAAK,EAAEtF,aAAa,CAACE;gBAAc;cAAE;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,kBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3F,OAAA;cAAKoF,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB1E,WAAW,CAACuI,GAAG,CAAEzF,IAAI,iBAClBzD,OAAA;gBAEI4F,OAAO,EAAEA,CAAA,KAAMV,sBAAsB,CAACzB,IAAI,CAACtD,EAAE,CAAE;gBAC/CiF,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjFrF,OAAA;kBACIkH,GAAG,EAAEtD,WAAW,CAACH,IAAI,CAACiF,KAAK,CAAE;kBAC7BvB,GAAG,EAAE1D,IAAI,CAAChC,KAAM;kBAChB2D,SAAS,EAAC,sDAAsD;kBAChEkC,OAAO,EAAG5E,CAAC,IAAK;oBACZA,CAAC,CAACqD,MAAM,CAACmB,GAAG,GAAG,wCAAwC;kBAC3D;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACF3F,OAAA;kBAAKoF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BrF,OAAA;oBAAIoF,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9D5B,IAAI,CAAChC;kBAAK;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL3F,OAAA;oBAAGoF,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACZ,IAAI,CAACmF,UAAU,IAAInF,IAAI,CAACc,IAAI;kBAAC;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBDlC,IAAI,CAACtD,EAAE;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGD3F,OAAA,CAACV,GAAG;YAAC2G,EAAE,EAAE;cAAEE,OAAO,EAAE,OAAO;cAAEgD,YAAY,EAAE,CAAC;cAAExB,SAAS,EAAE,CAAC;cAAEyB,CAAC,EAAE;YAAE,CAAE;YAAA/D,QAAA,eAC/DrF,OAAA,CAACV,GAAG;cACA2I,SAAS,EAAC,QAAQ;cAClBrC,OAAO,EAAET,gBAAiB;cAC1Bc,EAAE,EAAE;gBACAG,KAAK,EAAE,MAAM;gBACbD,OAAO,EAAE,cAAc;gBACvBI,KAAK,EAAE,OAAO;gBACdwB,EAAE,EAAE,GAAG;gBACPjB,EAAE,EAAE,CAAC;gBACLqC,YAAY,EAAE,CAAC;gBACfhB,MAAM,EAAE,MAAM;gBACdE,MAAM,EAAE,SAAS;gBACjBb,UAAU,EAAE,GAAG;gBACfU,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACP/B,OAAO,EAAE;gBACb;cACJ,CAAE;cAAAd,QAAA,gBAEFrF,OAAA;gBAAGoF,SAAS,EAAC,mBAAmB;gBAACE,KAAK,EAAE;kBAAEgD,WAAW,EAAE;gBAAM;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAExE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3F,OAAA,CAACV,GAAG;MAAC2G,EAAE,EAAE;QACLK,QAAQ,EAAE,OAAO;QACjB+C,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT5C,MAAM,EAAE,IAAI;QACZI,OAAO,EAAE;UAAEH,EAAE,EAAE,OAAO;UAAEC,EAAE,EAAE;QAAO,CAAC;QAAE;QACtChB,eAAe,EAAE,OAAO;QACxB2D,SAAS,EAAE,mBAAmB;QAC9B7B,SAAS,EAAE;MACf,CAAE;MAAAtC,QAAA,eACErF,OAAA,CAACV,GAAG;QAAC2G,EAAE,EAAE;UACLc,OAAO,EAAE,MAAM;UACf0C,cAAc,EAAE,cAAc;UAC9BzC,UAAU,EAAE,QAAQ;UACpBI,MAAM,EAAE,EAAE;UACVN,EAAE,EAAE;QACR,CAAE;QAAAzB,QAAA,gBACErF,OAAA,CAACV,GAAG;UACAsG,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,GAAG,CAAE;UAC7BgF,SAAS,EAAC,wBAAwB;UAClCE,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAU,CAAE;UAAAhD,QAAA,gBAE7BrF,OAAA;YAAGoF,SAAS,EAAC;UAA2C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D3F,OAAA,CAACT,UAAU;YAACgI,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAe,CAAE;YAAAlB,QAAA,EAAC;UAE1F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN3F,OAAA,CAACV,GAAG;UACAsG,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,GAAG,CAAE;UAC7BgF,SAAS,EAAC,iBAAiB;UAC3BE,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAU,CAAE;UAAAhD,QAAA,gBAE7BrF,OAAA;YAAGoF,SAAS,EAAC;UAA6C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D3F,OAAA,CAACT,UAAU;YAACgI,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAlB,QAAA,EAAC;UAE5F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN3F,OAAA,CAACV,GAAG;UACAsG,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,QAAQ,CAAE;UAClCgF,SAAS,EAAC,iBAAiB;UAC3BE,KAAK,EAAE;YAAE+C,MAAM,EAAE;UAAU,CAAE;UAAAhD,QAAA,gBAE7BrF,OAAA;YAAGoF,SAAS,EAAC;UAA+C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE3F,OAAA,CAACT,UAAU;YAACgI,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAlB,QAAA,EAAC;UAE5F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzF,EAAA,CAziBID,QAAQ;EAAA,QAEKb,SAAS,EACPC,WAAW,EACdS,QAAQ,EACJD,aAAa;AAAA;AAAA6J,EAAA,GAL7BzJ,QAAQ;AA2iBd,eAAeA,QAAQ;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}