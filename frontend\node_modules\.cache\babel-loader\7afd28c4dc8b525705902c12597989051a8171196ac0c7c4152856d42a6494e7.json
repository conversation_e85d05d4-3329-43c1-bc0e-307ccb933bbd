{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\DetailNews.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { AppBar, Toolbar, Typography, Container, Box, IconButton, Avatar, Button, useMediaQuery, useTheme, Skeleton, Chip, Card, CardContent, CardMedia, Fab } from '@mui/material';\nimport { Home as HomeIcon, Bookmark as BookmarkIcon, VideoLibrary as VideoIcon, Share as ShareIcon, Favorite as FavoriteIcon, Visibility as ViewIcon, ArrowBack as ArrowBackIcon, FavoriteBorder as FavoriteBorderIcon } from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DetailNews = () => {\n  _s();\n  var _user$name;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const {\n    user\n  } = useAuth();\n\n  // States\n  const [newsData, setNewsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isBookmarked, setIsBookmarked] = useState(false);\n  const [likeCount, setLikeCount] = useState(0);\n\n  // Fetch news detail\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail();\n      incrementViews();\n    }\n  }, [id]);\n  const fetchNewsDetail = async () => {\n    try {\n      const response = await fetch(`/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${id}`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNewsData(data.data);\n        setLikeCount(data.data.likes || 0);\n      } else {\n        console.error('News not found');\n        navigate('/');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      navigate('/');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const incrementViews = async () => {\n    try {\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: new URLSearchParams({\n          action: 'increment_views',\n          id: id\n        })\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n  };\n  const handleLike = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: new URLSearchParams({\n          action: 'toggle_like',\n          id: id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setIsLiked(!isLiked);\n        setLikeCount(prev => isLiked ? prev - 1 : prev + 1);\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n  const handleBookmark = async () => {\n    try {\n      const action = isBookmarked ? 'remove_saved_news' : 'add_saved_news';\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: new URLSearchParams({\n          action: action,\n          id: id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setIsBookmarked(!isBookmarked);\n      }\n    } catch (error) {\n      console.error('Error toggling bookmark:', error);\n    }\n  };\n  const handleShare = () => {\n    if (navigator.share && newsData) {\n      navigator.share({\n        title: newsData.title,\n        text: newsData.description,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link berhasil disalin!');\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        minHeight: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AppBar, {\n        position: \"fixed\",\n        children: /*#__PURE__*/_jsxDEV(Toolbar, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: () => navigate('/'),\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              flexGrow: 1\n            },\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        sx: {\n          mt: 10,\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rectangular\",\n          height: 300,\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"text\",\n          height: 40,\n          sx: {\n            mb: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"text\",\n          height: 20,\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"text\",\n          height: 200\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this);\n  }\n  if (!newsData) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        minHeight: '100vh',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        color: \"text.secondary\",\n        children: \"Berita tidak ditemukan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => navigate('/'),\n        sx: {\n          mt: 2\n        },\n        children: \"Kembali ke Beranda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          edge: \"start\",\n          onClick: () => navigate('/'),\n          sx: {\n            mr: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            flexGrow: 1,\n            fontWeight: 'bold'\n          },\n          children: \"Detail Berita\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          onClick: () => navigate('/video'),\n          children: /*#__PURE__*/_jsxDEV(VideoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), user ? /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            width: 32,\n            height: 32,\n            bgcolor: 'secondary.main'\n          },\n          children: (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigate('/auth/login'),\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"md\",\n      sx: {\n        mt: 10,\n        mb: {\n          xs: 8,\n          md: 4\n        },\n        px: {\n          xs: 2,\n          md: 3\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          overflow: 'hidden',\n          boxShadow: 3\n        },\n        children: [newsData.image && /*#__PURE__*/_jsxDEV(CardMedia, {\n          component: \"img\",\n          height: \"400\",\n          image: newsData.image,\n          alt: newsData.title,\n          sx: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: {\n              xs: 2,\n              md: 4\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: newsData.category_name || newsData.category || 'Berita',\n              sx: {\n                backgroundColor: newsData.category_color || '#3B82F6',\n                color: 'white',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 2,\n              fontSize: {\n                xs: '1.5rem',\n                md: '2rem'\n              },\n              lineHeight: 1.3\n            },\n            children: newsData.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 3,\n              flexWrap: 'wrap',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(ViewIcon, {\n                sx: {\n                  fontSize: 16,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [newsData.views || 0, \" views\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(FavoriteIcon, {\n                sx: {\n                  fontSize: 16,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [likeCount, \" likes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: formatDate(newsData.published_at || newsData.date || newsData.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), newsData.description && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3,\n              p: 2,\n              backgroundColor: 'grey.50',\n              borderRadius: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                fontStyle: 'italic',\n                color: 'text.secondary'\n              },\n              children: newsData.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              '& p': {\n                mb: 2,\n                lineHeight: 1.7\n              },\n              '& h1, & h2, & h3': {\n                mt: 3,\n                mb: 2,\n                fontWeight: 'bold'\n              },\n              '& img': {\n                maxWidth: '100%',\n                height: 'auto',\n                borderRadius: 2,\n                my: 2\n              },\n              '& ul, & ol': {\n                pl: 3,\n                mb: 2\n              },\n              '& li': {\n                mb: 1\n              }\n            },\n            dangerouslySetInnerHTML: {\n              __html: newsData.content\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        bottom: {\n          xs: 80,\n          md: 20\n        },\n        right: 20,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Fab, {\n        color: isLiked ? \"error\" : \"default\",\n        size: \"medium\",\n        onClick: handleLike,\n        sx: {\n          boxShadow: 3\n        },\n        children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 22\n        }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Fab, {\n        color: isBookmarked ? \"success\" : \"primary\",\n        size: \"medium\",\n        onClick: handleBookmark,\n        sx: {\n          boxShadow: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Fab, {\n        color: \"secondary\",\n        size: \"medium\",\n        onClick: handleShare,\n        sx: {\n          boxShadow: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\n_s(DetailNews, \"pB+VFW6a9THU1Jh90o+BnvMxka0=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery, useAuth];\n});\n_c = DetailNews;\nexport default DetailNews;\nvar _c;\n$RefreshReg$(_c, \"DetailNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Container", "Box", "IconButton", "Avatar", "<PERSON><PERSON>", "useMediaQuery", "useTheme", "Skeleton", "Chip", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Fab", "Home", "HomeIcon", "Bookmark", "BookmarkIcon", "VideoLibrary", "VideoIcon", "Share", "ShareIcon", "Favorite", "FavoriteIcon", "Visibility", "ViewIcon", "ArrowBack", "ArrowBackIcon", "FavoriteBorder", "FavoriteBorderIcon", "useAuth", "jsxDEV", "_jsxDEV", "DetailNews", "_s", "_user$name", "id", "navigate", "theme", "isMobile", "breakpoints", "down", "user", "newsData", "setNewsData", "loading", "setLoading", "isLiked", "setIsLiked", "isBookmarked", "setIsBookmarked", "likeCount", "setLikeCount", "fetchNewsDetail", "incrementViews", "response", "fetch", "data", "json", "success", "likes", "console", "error", "method", "headers", "body", "URLSearchParams", "action", "log", "handleLike", "prev", "handleBookmark", "handleShare", "navigator", "share", "title", "text", "description", "url", "window", "location", "href", "clipboard", "writeText", "alert", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "sx", "display", "flexDirection", "minHeight", "children", "position", "color", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "flexGrow", "max<PERSON><PERSON><PERSON>", "mt", "mb", "height", "alignItems", "justifyContent", "edge", "mr", "component", "fontWeight", "width", "bgcolor", "name", "char<PERSON>t", "toUpperCase", "xs", "md", "px", "overflow", "boxShadow", "image", "alt", "objectFit", "p", "label", "category_name", "category", "backgroundColor", "category_color", "fontSize", "lineHeight", "flexWrap", "gap", "views", "published_at", "created_at", "borderRadius", "fontStyle", "my", "pl", "dangerouslySetInnerHTML", "__html", "content", "bottom", "right", "size", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/DetailNews.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Typography,\n  Container,\n  Box,\n  IconButton,\n  Avatar,\n  Button,\n  useMediaQuery,\n  useTheme,\n  Skeleton,\n  Chip,\n  Card,\n  CardContent,\n  CardMedia,\n  Fab\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  Bookmark as BookmarkIcon,\n  VideoLibrary as VideoIcon,\n  Share as ShareIcon,\n  Favorite as FavoriteIcon,\n  Visibility as ViewIcon,\n  ArrowBack as ArrowBackIcon,\n  FavoriteBorder as FavoriteBorderIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst DetailNews = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const { user } = useAuth();\n\n  // States\n  const [newsData, setNewsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isBookmarked, setIsBookmarked] = useState(false);\n  const [likeCount, setLikeCount] = useState(0);\n\n  // Fetch news detail\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail();\n      incrementViews();\n    }\n  }, [id]);\n\n  const fetchNewsDetail = async () => {\n    try {\n      const response = await fetch(`/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${id}`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setNewsData(data.data);\n        setLikeCount(data.data.likes || 0);\n      } else {\n        console.error('News not found');\n        navigate('/');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      navigate('/');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const incrementViews = async () => {\n    try {\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: new URLSearchParams({\n          action: 'increment_views',\n          id: id\n        })\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: new URLSearchParams({\n          action: 'toggle_like',\n          id: id\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setIsLiked(!isLiked);\n        setLikeCount(prev => isLiked ? prev - 1 : prev + 1);\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  const handleBookmark = async () => {\n    try {\n      const action = isBookmarked ? 'remove_saved_news' : 'add_saved_news';\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: new URLSearchParams({\n          action: action,\n          id: id\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setIsBookmarked(!isBookmarked);\n      }\n    } catch (error) {\n      console.error('Error toggling bookmark:', error);\n    }\n  };\n\n  const handleShare = () => {\n    if (navigator.share && newsData) {\n      navigator.share({\n        title: newsData.title,\n        text: newsData.description,\n        url: window.location.href,\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link berhasil disalin!');\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', { \n      day: 'numeric', \n      month: 'long', \n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n        <AppBar position=\"fixed\">\n          <Toolbar>\n            <IconButton color=\"inherit\" onClick={() => navigate('/')}>\n              <ArrowBackIcon />\n            </IconButton>\n            <Typography variant=\"h6\" sx={{ flexGrow: 1 }}>\n              Loading...\n            </Typography>\n          </Toolbar>\n        </AppBar>\n        \n        <Container maxWidth=\"md\" sx={{ mt: 10, mb: 4 }}>\n          <Skeleton variant=\"rectangular\" height={300} sx={{ mb: 2 }} />\n          <Skeleton variant=\"text\" height={40} sx={{ mb: 1 }} />\n          <Skeleton variant=\"text\" height={20} sx={{ mb: 2 }} />\n          <Skeleton variant=\"text\" height={200} />\n        </Container>\n      </Box>\n    );\n  }\n\n  if (!newsData) {\n    return (\n      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', alignItems: 'center', justifyContent: 'center' }}>\n        <Typography variant=\"h5\" color=\"text.secondary\">\n          Berita tidak ditemukan\n        </Typography>\n        <Button onClick={() => navigate('/')} sx={{ mt: 2 }}>\n          Kembali ke Beranda\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n      {/* Top Navigation */}\n      <AppBar position=\"fixed\">\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            edge=\"start\"\n            onClick={() => navigate('/')}\n            sx={{ mr: 2 }}\n          >\n            <ArrowBackIcon />\n          </IconButton>\n          \n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1, fontWeight: 'bold' }}>\n            Detail Berita\n          </Typography>\n\n          {/* Video Button */}\n          <IconButton color=\"inherit\" onClick={() => navigate('/video')}>\n            <VideoIcon />\n          </IconButton>\n\n          {/* Profile/Login */}\n          {user ? (\n            <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>\n              {user.name?.charAt(0).toUpperCase()}\n            </Avatar>\n          ) : (\n            <Button color=\"inherit\" onClick={() => navigate('/auth/login')}>\n              Login\n            </Button>\n          )}\n        </Toolbar>\n      </AppBar>\n\n      {/* Main Content */}\n      <Container maxWidth=\"md\" sx={{ mt: 10, mb: { xs: 8, md: 4 }, px: { xs: 2, md: 3 } }}>\n        <Card sx={{ overflow: 'hidden', boxShadow: 3 }}>\n          {/* Featured Image */}\n          {newsData.image && (\n            <CardMedia\n              component=\"img\"\n              height=\"400\"\n              image={newsData.image}\n              alt={newsData.title}\n              sx={{ objectFit: 'cover' }}\n            />\n          )}\n          \n          <CardContent sx={{ p: { xs: 2, md: 4 } }}>\n            {/* Category */}\n            <Box sx={{ mb: 2 }}>\n              <Chip\n                label={newsData.category_name || newsData.category || 'Berita'}\n                sx={{\n                  backgroundColor: newsData.category_color || '#3B82F6',\n                  color: 'white',\n                  fontWeight: 'bold'\n                }}\n              />\n            </Box>\n            \n            {/* Title */}\n            <Typography \n              variant=\"h4\" \n              component=\"h1\" \n              sx={{ \n                fontWeight: 'bold', \n                mb: 2,\n                fontSize: { xs: '1.5rem', md: '2rem' },\n                lineHeight: 1.3\n              }}\n            >\n              {newsData.title}\n            </Typography>\n            \n            {/* Meta Info */}\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, flexWrap: 'wrap', gap: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <ViewIcon sx={{ fontSize: 16, color: 'text.secondary' }} />\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {newsData.views || 0} views\n                </Typography>\n              </Box>\n              \n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <FavoriteIcon sx={{ fontSize: 16, color: 'text.secondary' }} />\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {likeCount} likes\n                </Typography>\n              </Box>\n              \n              <Typography variant=\"caption\" color=\"text.secondary\">\n                {formatDate(newsData.published_at || newsData.date || newsData.created_at)}\n              </Typography>\n            </Box>\n            \n            {/* Description */}\n            {newsData.description && (\n              <Box sx={{ mb: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>\n                <Typography variant=\"body1\" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>\n                  {newsData.description}\n                </Typography>\n              </Box>\n            )}\n            \n            {/* Content */}\n            <Box \n              sx={{ \n                '& p': { mb: 2, lineHeight: 1.7 },\n                '& h1, & h2, & h3': { mt: 3, mb: 2, fontWeight: 'bold' },\n                '& img': { maxWidth: '100%', height: 'auto', borderRadius: 2, my: 2 },\n                '& ul, & ol': { pl: 3, mb: 2 },\n                '& li': { mb: 1 }\n              }}\n              dangerouslySetInnerHTML={{ __html: newsData.content }}\n            />\n          </CardContent>\n        </Card>\n      </Container>\n\n      {/* Floating Action Buttons */}\n      <Box sx={{ position: 'fixed', bottom: { xs: 80, md: 20 }, right: 20, display: 'flex', flexDirection: 'column', gap: 1 }}>\n        <Fab\n          color={isLiked ? \"error\" : \"default\"}\n          size=\"medium\"\n          onClick={handleLike}\n          sx={{ boxShadow: 3 }}\n        >\n          {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n        </Fab>\n        \n        <Fab\n          color={isBookmarked ? \"success\" : \"primary\"}\n          size=\"medium\"\n          onClick={handleBookmark}\n          sx={{ boxShadow: 3 }}\n        >\n          <BookmarkIcon />\n        </Fab>\n        \n        <Fab\n          color=\"secondary\"\n          size=\"medium\"\n          onClick={handleShare}\n          sx={{ boxShadow: 3 }}\n        >\n          <ShareIcon />\n        </Fab>\n      </Box>\n    </Box>\n  );\n};\n\nexport default DetailNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,GAAG,QACE,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,YAAY,IAAIC,SAAS,EACzBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,QAC/B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACvB,MAAM;IAAEC;EAAG,CAAC,GAAGxC,SAAS,CAAC,CAAC;EAC1B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,KAAK,GAAG/B,QAAQ,CAAC,CAAC;EACxB,MAAMgC,QAAQ,GAAGjC,aAAa,CAACgC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyC,EAAE,EAAE;MACNiB,eAAe,CAAC,CAAC;MACjBC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClB,EAAE,CAAC,CAAC;EAER,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,gFAAgFpB,EAAE,EAAE,CAAC;MAClH,MAAMqB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7Bb,WAAW,CAACa,IAAI,CAACA,IAAI,CAAC;QACtBL,YAAY,CAACK,IAAI,CAACA,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;MACpC,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAC;QAC/BzB,QAAQ,CAAC,GAAG,CAAC;MACf;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CzB,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,SAAS;MACRS,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAME,KAAK,CAAC,qEAAqE,EAAE;QACjFO,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;UACxBC,MAAM,EAAE,iBAAiB;UACzB/B,EAAE,EAAEA;QACN,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdD,OAAO,CAACO,GAAG,CAAC,4BAA4B,EAAEN,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAClGO,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;UACxBC,MAAM,EAAE,aAAa;UACrB/B,EAAE,EAAEA;QACN,CAAC;MACH,CAAC,CAAC;MAEF,MAAMqB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBX,UAAU,CAAC,CAACD,OAAO,CAAC;QACpBK,YAAY,CAACkB,IAAI,IAAIvB,OAAO,GAAGuB,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMJ,MAAM,GAAGlB,YAAY,GAAG,mBAAmB,GAAG,gBAAgB;MACpE,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAClGO,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;UACxBC,MAAM,EAAEA,MAAM;UACd/B,EAAE,EAAEA;QACN,CAAC;MACH,CAAC,CAAC;MAEF,MAAMqB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBT,eAAe,CAAC,CAACD,YAAY,CAAC;MAChC;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIC,SAAS,CAACC,KAAK,IAAI/B,QAAQ,EAAE;MAC/B8B,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAEhC,QAAQ,CAACgC,KAAK;QACrBC,IAAI,EAAEjC,QAAQ,CAACkC,WAAW;QAC1BC,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAR,SAAS,CAACS,SAAS,CAACC,SAAS,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACnDG,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAIjD,OAAO,EAAE;IACX,oBACEb,OAAA,CAAC9B,GAAG;MAAC6F,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAC,QAAA,gBACxEnE,OAAA,CAAClC,MAAM;QAACsG,QAAQ,EAAC,OAAO;QAAAD,QAAA,eACtBnE,OAAA,CAACjC,OAAO;UAAAoG,QAAA,gBACNnE,OAAA,CAAC7B,UAAU;YAACkG,KAAK,EAAC,SAAS;YAACC,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,GAAG,CAAE;YAAA8D,QAAA,eACvDnE,OAAA,CAACL,aAAa;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACb1E,OAAA,CAAChC,UAAU;YAAC2G,OAAO,EAAC,IAAI;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAE;YAAE,CAAE;YAAAT,QAAA,EAAC;UAE9C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAET1E,OAAA,CAAC/B,SAAS;QAAC4G,QAAQ,EAAC,IAAI;QAACd,EAAE,EAAE;UAAEe,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBAC7CnE,OAAA,CAACxB,QAAQ;UAACmG,OAAO,EAAC,aAAa;UAACK,MAAM,EAAE,GAAI;UAACjB,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9D1E,OAAA,CAACxB,QAAQ;UAACmG,OAAO,EAAC,MAAM;UAACK,MAAM,EAAE,EAAG;UAACjB,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtD1E,OAAA,CAACxB,QAAQ;UAACmG,OAAO,EAAC,MAAM;UAACK,MAAM,EAAE,EAAG;UAACjB,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtD1E,OAAA,CAACxB,QAAQ;UAACmG,OAAO,EAAC,MAAM;UAACK,MAAM,EAAE;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEV;EAEA,IAAI,CAAC/D,QAAQ,EAAE;IACb,oBACEX,OAAA,CAAC9B,GAAG;MAAC6F,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,SAAS,EAAE,OAAO;QAAEe,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAf,QAAA,gBACxHnE,OAAA,CAAChC,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAACN,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA,CAAC3B,MAAM;QAACiG,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,GAAG,CAAE;QAAC0D,EAAE,EAAE;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1E,OAAA,CAAC9B,GAAG;IAAC6F,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAExEnE,OAAA,CAAClC,MAAM;MAACsG,QAAQ,EAAC,OAAO;MAAAD,QAAA,eACtBnE,OAAA,CAACjC,OAAO;QAAAoG,QAAA,gBACNnE,OAAA,CAAC7B,UAAU;UACTkG,KAAK,EAAC,SAAS;UACfc,IAAI,EAAC,OAAO;UACZb,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,GAAG,CAAE;UAC7B0D,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,eAEdnE,OAAA,CAACL,aAAa;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEb1E,OAAA,CAAChC,UAAU;UAAC2G,OAAO,EAAC,IAAI;UAACU,SAAS,EAAC,KAAK;UAACtB,EAAE,EAAE;YAAEa,QAAQ,EAAE,CAAC;YAAEU,UAAU,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAAC;QAElF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb1E,OAAA,CAAC7B,UAAU;UAACkG,KAAK,EAAC,SAAS;UAACC,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,QAAQ,CAAE;UAAA8D,QAAA,eAC5DnE,OAAA,CAACb,SAAS;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGZhE,IAAI,gBACHV,OAAA,CAAC5B,MAAM;UAAC2F,EAAE,EAAE;YAAEwB,KAAK,EAAE,EAAE;YAAEP,MAAM,EAAE,EAAE;YAAEQ,OAAO,EAAE;UAAiB,CAAE;UAAArB,QAAA,GAAAhE,UAAA,GAC9DO,IAAI,CAAC+E,IAAI,cAAAtF,UAAA,uBAATA,UAAA,CAAWuF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAAC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,gBAET1E,OAAA,CAAC3B,MAAM;UAACgG,KAAK,EAAC,SAAS;UAACC,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,aAAa,CAAE;UAAA8D,QAAA,EAAC;QAEhE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGT1E,OAAA,CAAC/B,SAAS;MAAC4G,QAAQ,EAAC,IAAI;MAACd,EAAE,EAAE;QAAEe,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;UAAEa,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAAEC,EAAE,EAAE;UAAEF,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE;MAAE,CAAE;MAAA1B,QAAA,eAClFnE,OAAA,CAACtB,IAAI;QAACqF,EAAE,EAAE;UAAEgC,QAAQ,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAE,CAAE;QAAA7B,QAAA,GAE5CxD,QAAQ,CAACsF,KAAK,iBACbjG,OAAA,CAACpB,SAAS;UACRyG,SAAS,EAAC,KAAK;UACfL,MAAM,EAAC,KAAK;UACZiB,KAAK,EAAEtF,QAAQ,CAACsF,KAAM;UACtBC,GAAG,EAAEvF,QAAQ,CAACgC,KAAM;UACpBoB,EAAE,EAAE;YAAEoC,SAAS,EAAE;UAAQ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACF,eAED1E,OAAA,CAACrB,WAAW;UAACoF,EAAE,EAAE;YAAEqC,CAAC,EAAE;cAAER,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE;UAAE,CAAE;UAAA1B,QAAA,gBAEvCnE,OAAA,CAAC9B,GAAG;YAAC6F,EAAE,EAAE;cAAEgB,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,eACjBnE,OAAA,CAACvB,IAAI;cACH4H,KAAK,EAAE1F,QAAQ,CAAC2F,aAAa,IAAI3F,QAAQ,CAAC4F,QAAQ,IAAI,QAAS;cAC/DxC,EAAE,EAAE;gBACFyC,eAAe,EAAE7F,QAAQ,CAAC8F,cAAc,IAAI,SAAS;gBACrDpC,KAAK,EAAE,OAAO;gBACdiB,UAAU,EAAE;cACd;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1E,OAAA,CAAChC,UAAU;YACT2G,OAAO,EAAC,IAAI;YACZU,SAAS,EAAC,IAAI;YACdtB,EAAE,EAAE;cACFuB,UAAU,EAAE,MAAM;cAClBP,EAAE,EAAE,CAAC;cACL2B,QAAQ,EAAE;gBAAEd,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACtCc,UAAU,EAAE;YACd,CAAE;YAAAxC,QAAA,EAEDxD,QAAQ,CAACgC;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGb1E,OAAA,CAAC9B,GAAG;YAAC6F,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEF,EAAE,EAAE,CAAC;cAAE6B,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAClFnE,OAAA,CAAC9B,GAAG;cAAC6F,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAE4B,GAAG,EAAE;cAAI,CAAE;cAAA1C,QAAA,gBAC3DnE,OAAA,CAACP,QAAQ;gBAACsE,EAAE,EAAE;kBAAE2C,QAAQ,EAAE,EAAE;kBAAErC,KAAK,EAAE;gBAAiB;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3D1E,OAAA,CAAChC,UAAU;gBAAC2G,OAAO,EAAC,SAAS;gBAACN,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GACjDxD,QAAQ,CAACmG,KAAK,IAAI,CAAC,EAAC,QACvB;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN1E,OAAA,CAAC9B,GAAG;cAAC6F,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAE4B,GAAG,EAAE;cAAI,CAAE;cAAA1C,QAAA,gBAC3DnE,OAAA,CAACT,YAAY;gBAACwE,EAAE,EAAE;kBAAE2C,QAAQ,EAAE,EAAE;kBAAErC,KAAK,EAAE;gBAAiB;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D1E,OAAA,CAAChC,UAAU;gBAAC2G,OAAO,EAAC,SAAS;gBAACN,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GACjDhD,SAAS,EAAC,QACb;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN1E,OAAA,CAAChC,UAAU;cAAC2G,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EACjDd,UAAU,CAAC1C,QAAQ,CAACoG,YAAY,IAAIpG,QAAQ,CAAC4C,IAAI,IAAI5C,QAAQ,CAACqG,UAAU;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAGL/D,QAAQ,CAACkC,WAAW,iBACnB7C,OAAA,CAAC9B,GAAG;YAAC6F,EAAE,EAAE;cAAEgB,EAAE,EAAE,CAAC;cAAEqB,CAAC,EAAE,CAAC;cAAEI,eAAe,EAAE,SAAS;cAAES,YAAY,EAAE;YAAE,CAAE;YAAA9C,QAAA,eACpEnE,OAAA,CAAChC,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACZ,EAAE,EAAE;gBAAEmD,SAAS,EAAE,QAAQ;gBAAE7C,KAAK,EAAE;cAAiB,CAAE;cAAAF,QAAA,EAC9ExD,QAAQ,CAACkC;YAAW;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,eAGD1E,OAAA,CAAC9B,GAAG;YACF6F,EAAE,EAAE;cACF,KAAK,EAAE;gBAAEgB,EAAE,EAAE,CAAC;gBAAE4B,UAAU,EAAE;cAAI,CAAC;cACjC,kBAAkB,EAAE;gBAAE7B,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEO,UAAU,EAAE;cAAO,CAAC;cACxD,OAAO,EAAE;gBAAET,QAAQ,EAAE,MAAM;gBAAEG,MAAM,EAAE,MAAM;gBAAEiC,YAAY,EAAE,CAAC;gBAAEE,EAAE,EAAE;cAAE,CAAC;cACrE,YAAY,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAErC,EAAE,EAAE;cAAE,CAAC;cAC9B,MAAM,EAAE;gBAAEA,EAAE,EAAE;cAAE;YAClB,CAAE;YACFsC,uBAAuB,EAAE;cAAEC,MAAM,EAAE3G,QAAQ,CAAC4G;YAAQ;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGZ1E,OAAA,CAAC9B,GAAG;MAAC6F,EAAE,EAAE;QAAEK,QAAQ,EAAE,OAAO;QAAEoD,MAAM,EAAE;UAAE5B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAAE4B,KAAK,EAAE,EAAE;QAAEzD,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAE4C,GAAG,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACtHnE,OAAA,CAACnB,GAAG;QACFwF,KAAK,EAAEtD,OAAO,GAAG,OAAO,GAAG,SAAU;QACrC2G,IAAI,EAAC,QAAQ;QACbpD,OAAO,EAAEjC,UAAW;QACpB0B,EAAE,EAAE;UAAEiC,SAAS,EAAE;QAAE,CAAE;QAAA7B,QAAA,EAEpBpD,OAAO,gBAAGf,OAAA,CAACT,YAAY;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACH,kBAAkB;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEN1E,OAAA,CAACnB,GAAG;QACFwF,KAAK,EAAEpD,YAAY,GAAG,SAAS,GAAG,SAAU;QAC5CyG,IAAI,EAAC,QAAQ;QACbpD,OAAO,EAAE/B,cAAe;QACxBwB,EAAE,EAAE;UAAEiC,SAAS,EAAE;QAAE,CAAE;QAAA7B,QAAA,eAErBnE,OAAA,CAACf,YAAY;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEN1E,OAAA,CAACnB,GAAG;QACFwF,KAAK,EAAC,WAAW;QACjBqD,IAAI,EAAC,QAAQ;QACbpD,OAAO,EAAE9B,WAAY;QACrBuB,EAAE,EAAE;UAAEiC,SAAS,EAAE;QAAE,CAAE;QAAA7B,QAAA,eAErBnE,OAAA,CAACX,SAAS;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CAhUID,UAAU;EAAA,QACCrC,SAAS,EACPC,WAAW,EACdU,QAAQ,EACLD,aAAa,EACbwB,OAAO;AAAA;AAAA6H,EAAA,GALpB1H,UAAU;AAkUhB,eAAeA,UAAU;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}