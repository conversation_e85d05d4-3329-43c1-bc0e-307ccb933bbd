<?php
// Simple API endpoint for pengaturan
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'react_news';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'get_pengaturan':
            $stmt = $pdo->prepare("
                SELECT p.*, l.logo_data, l.mime_type as logo_mime_type
                FROM pengaturan p
                LEFT JOIN logos l ON p.logo_id = l.id
                WHERE p.id = 1
            ");
            $stmt->execute();
            $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pengaturan) {
                // Create default if not exists - this should not happen if config.php ran properly
                echo json_encode(['success' => false, 'message' => 'Pengaturan not found']);
                break;
            }

            // Add logo_file_path for backward compatibility
            $pengaturan['logo_file_path'] = $pengaturan['logo_data'] ?? 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';

            echo json_encode(['success' => true, 'data' => $pengaturan]);
            break;

        case 'update_pengaturan':
            $nama_website = $_POST['nama_website'] ?? '';
            $deskripsi_website = $_POST['deskripsi_website'] ?? '';
            $warna_sidebar = $_POST['warna_sidebar'] ?? '#2563EB';
            $warna_sidebar_header = $_POST['warna_sidebar_header'] ?? '#1D4ED8';
            $warna_primary = $_POST['warna_primary'] ?? '#3B82F6';
            $warna_secondary = $_POST['warna_secondary'] ?? '#10B981';
            $warna_accent = $_POST['warna_accent'] ?? '#F59E0B';

            $newLogoId = null;

            // Handle logo upload
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['logo'];

                // Validate file - accept all common image formats
                $allowedTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                    'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif', 'image/ico',
                    'image/icon', 'image/x-icon', 'image/vnd.microsoft.icon'
                ];

                // Also check file extension as backup
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'tif', 'ico'];

                if (!in_array($file['type'], $allowedTypes) && !in_array($fileExtension, $allowedExtensions)) {
                    echo json_encode(['success' => false, 'message' => 'Tipe file tidak diizinkan. Gunakan format gambar yang valid (JPG, PNG, GIF, WebP, SVG, BMP, TIFF, ICO)']);
                    exit;
                }

                if ($file['size'] > 5 * 1024 * 1024) {
                    echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                    exit;
                }

                // Read file content and convert to base64
                $fileContent = file_get_contents($file['tmp_name']);
                if ($fileContent === false) {
                    echo json_encode(['success' => false, 'message' => 'Gagal membaca file logo']);
                    exit;
                }

                $base64Data = 'data:' . $file['type'] . ';base64,' . base64_encode($fileContent);

                // Save logo to database
                $stmt = $pdo->prepare("INSERT INTO logos (logo_data, mime_type, file_size) VALUES (?, ?, ?)");
                $result = $stmt->execute([$base64Data, $file['type'], $file['size']]);

                if ($result) {
                    $newLogoId = $pdo->lastInsertId();

                    // Deactivate old logos
                    $stmt = $pdo->prepare("UPDATE logos SET is_active = 0 WHERE id != ?");
                    $stmt->execute([$newLogoId]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menyimpan logo ke database']);
                    exit;
                }
            }

            // Update pengaturan
            $sql = "UPDATE pengaturan SET
                        nama_website = ?,
                        deskripsi_website = ?,
                        warna_sidebar = ?,
                        warna_sidebar_header = ?,
                        warna_primary = ?,
                        warna_secondary = ?,
                        warna_accent = ?";

            $params = [$nama_website, $deskripsi_website, $warna_sidebar, $warna_sidebar_header, $warna_primary, $warna_secondary, $warna_accent];

            if ($newLogoId) {
                $sql .= ", logo_id = ?";
                $params[] = $newLogoId;
            }

            $sql .= " WHERE id = 1";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Pengaturan berhasil disimpan']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Gagal menyimpan pengaturan']);
            }
            break;

        case 'get_categories':
            try {
                // Check if categories table exists
                $stmt = $pdo->prepare("SHOW TABLES LIKE 'categories'");
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $stmt = $pdo->prepare("
                        SELECT
                            c.*,
                            COUNT(p.id) as post_count
                        FROM categories c
                        LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
                        WHERE c.is_active = 1
                        GROUP BY c.id
                        ORDER BY c.name ASC
                    ");
                    $stmt->execute();
                    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } else {
                    // Return default categories if table doesn't exist
                    $categories = [
                        ['id' => 1, 'name' => 'Umum', 'slug' => 'umum', 'color' => '#6B7280', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 2, 'name' => 'Teknologi', 'slug' => 'teknologi', 'color' => '#3B82F6', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 3, 'name' => 'Bisnis', 'slug' => 'bisnis', 'color' => '#10B981', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 4, 'name' => 'Olahraga', 'slug' => 'olahraga', 'color' => '#F59E0B', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 5, 'name' => 'Hiburan', 'slug' => 'hiburan', 'color' => '#EF4444', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 6, 'name' => 'Politik', 'slug' => 'politik', 'color' => '#8B5CF6', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 7, 'name' => 'Kesehatan', 'slug' => 'kesehatan', 'color' => '#06B6D4', 'is_active' => 1, 'post_count' => 0]
                    ];
                }

                echo json_encode(['success' => true, 'data' => $categories]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading categories: ' . $e->getMessage()]);
            }
            break;

        case 'get_news':
            try {
                $limit = $_GET['limit'] ?? null;
                $category = $_GET['category'] ?? null;

                $sql = "
                    SELECT
                        p.*,
                        c.name as category_name,
                        c.color as category_color
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE 1=1
                ";

                $params = [];

                if ($category && $category !== 'all') {
                    $sql .= " AND p.category_id = ?";
                    $params[] = $category;
                }

                $sql .= " ORDER BY p.created_at DESC";

                if ($limit) {
                    $sql .= " LIMIT ?";
                    $params[] = (int)$limit;
                }

                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $news = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $news]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading news: ' . $e->getMessage()]);
            }
            break;

        case 'get_news_by_id':
            $newsId = $_GET['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("
                    SELECT
                        p.*,
                        c.name as category_name,
                        c.color as category_color
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.id = ?
                ");
                $stmt->execute([$newsId]);
                $news = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($news) {
                    echo json_encode(['success' => true, 'data' => $news]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'News not found']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading news: ' . $e->getMessage()]);
            }
            break;

        case 'get_categories':
            try {
                $stmt = $pdo->prepare("
                    SELECT
                        c.*,
                        COUNT(p.id) as post_count
                    FROM categories c
                    LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
                    GROUP BY c.id
                    ORDER BY c.name ASC
                ");
                $stmt->execute();
                $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $categories]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading categories: ' . $e->getMessage()]);
            }
            break;

        case 'get_notifications':
            $limit = $_GET['limit'] ?? 10;

            // Check if notifications table exists
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'notifications'");
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                try {
                    $stmt = $pdo->prepare("SELECT * FROM notifications ORDER BY created_at DESC LIMIT ?");
                    $stmt->execute([(int)$limit]);
                    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    // Try to get unread count, handle if is_read column doesn't exist
                    try {
                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0");
                        $stmt->execute();
                        $unreadCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                    } catch (Exception $e) {
                        // If is_read column doesn't exist, assume all are unread
                        $unreadCount = count($notifications);
                    }
                } catch (Exception $e) {
                    $notifications = [];
                    $unreadCount = 0;
                }
            } else {
                $notifications = [];
                $unreadCount = 0;
            }

            echo json_encode([
                'success' => true,
                'data' => $notifications,
                'unread_count' => $unreadCount
            ]);
            break;

        case 'toggle_like':
            $postId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'Post ID required']);
                break;
            }

            $ipAddress = $_SERVER['REMOTE_ADDR'];

            // Check if already liked
            $stmt = $pdo->prepare("SELECT id FROM post_likes WHERE post_id = ? AND ip_address = ?");
            $stmt->execute([$postId, $ipAddress]);
            $existingLike = $stmt->fetch();

            if ($existingLike) {
                // Remove like
                $stmt = $pdo->prepare("DELETE FROM post_likes WHERE post_id = ? AND ip_address = ?");
                $stmt->execute([$postId, $ipAddress]);

                // Decrease like count
                $stmt = $pdo->prepare("UPDATE posts SET likes = GREATEST(0, likes - 1) WHERE id = ?");
                $stmt->execute([$postId]);

                echo json_encode(['success' => true, 'liked' => false, 'message' => 'Like removed']);
            } else {
                // Add like
                $stmt = $pdo->prepare("INSERT INTO post_likes (post_id, ip_address, user_agent) VALUES (?, ?, ?)");
                $stmt->execute([$postId, $ipAddress, $_SERVER['HTTP_USER_AGENT'] ?? '']);

                // Increase like count
                $stmt = $pdo->prepare("UPDATE posts SET likes = likes + 1 WHERE id = ?");
                $stmt->execute([$postId]);

                echo json_encode(['success' => true, 'liked' => true, 'message' => 'Like added']);
            }
            break;

        case 'get_liked_posts':
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt = $pdo->prepare("SELECT post_id FROM post_likes WHERE ip_address = ?");
            $stmt->execute([$ipAddress]);
            $likedPosts = $stmt->fetchAll(PDO::FETCH_COLUMN);

            echo json_encode([
                'success' => true,
                'data' => $likedPosts,
                'ip_address' => $ipAddress
            ]);
            break;

        case 'add_saved_news':
        case 'remove_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['news_id'] ?? null;

            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            $ipAddress = $_SERVER['REMOTE_ADDR'];

            if ($action === 'add_saved_news') {
                // Add to saved
                $stmt = $pdo->prepare("INSERT IGNORE INTO saved (post_id, ip_address) VALUES (?, ?)");
                $stmt->execute([$postId, $ipAddress]);
                echo json_encode(['success' => true, 'message' => 'News saved']);
            } else {
                // Remove from saved
                $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ? AND ip_address = ?");
                $stmt->execute([$postId, $ipAddress]);
                echo json_encode(['success' => true, 'message' => 'News removed from saved']);
            }
            break;

        case 'get_saved_news':
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt = $pdo->prepare("
                SELECT p.*, c.name as category_name, c.color as category_color
                FROM saved s
                JOIN posts p ON s.post_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE s.ip_address = ?
                ORDER BY s.saved_at DESC
            ");
            $stmt->execute([$ipAddress]);
            $savedNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'data' => $savedNews]);
            break;

        case 'increment_views':
            $postId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'Post ID required']);
                break;
            }

            // Update view count
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $stmt->execute([$postId]);

            echo json_encode(['success' => true, 'message' => 'Views incremented']);
            break;

        case 'update_news':
            $newsId = $_POST['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                $title = $_POST['title'] ?? '';
                $slug = $_POST['slug'] ?? '';
                $description = $_POST['description'] ?? '';
                $content = $_POST['content'] ?? '';
                $categoryId = $_POST['category_id'] ?? null;
                $status = $_POST['status'] ?? 'draft';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $tags = $_POST['tags'] ?? '';

                // Handle image upload
                $imageData = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['image'];

                    // Validate file
                    $allowedTypes = [
                        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                        'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif'
                    ];

                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format gambar tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 5 * 1024 * 1024) {
                        echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                        break;
                    }

                    // Save to uploads directory
                    $uploadsDir = dirname(dirname(dirname(__DIR__))) . '/uploads';
                    if (!is_dir($uploadsDir)) {
                        mkdir($uploadsDir, 0755, true);
                    }

                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'news_' . time() . '_' . uniqid() . '.' . $extension;
                    $filepath = $uploadsDir . '/' . $filename;

                    if (move_uploaded_file($file['tmp_name'], $filepath)) {
                        $imageData = 'uploads/' . $filename;
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Gagal mengupload gambar']);
                        break;
                    }
                }

                // Update news
                $sql = "UPDATE posts SET
                            title = ?,
                            slug = ?,
                            description = ?,
                            content = ?,
                            category_id = ?,
                            status = ?,
                            featured = ?,
                            tags = ?,
                            updated_at = NOW()";

                $params = [$title, $slug, $description, $content, $categoryId, $status, $featured, $tags];

                if ($imageData) {
                    $sql .= ", image = ?";
                    $params[] = $imageData;
                }

                $sql .= " WHERE id = ?";
                $params[] = $newsId;

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Berita berhasil diperbarui']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal memperbarui berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error updating news: ' . $e->getMessage()]);
            }
            break;

        case 'delete_news':
            $newsId = $_POST['id'] ?? $_GET['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                // Get image path before deleting
                $stmt = $pdo->prepare("SELECT image FROM posts WHERE id = ?");
                $stmt->execute([$newsId]);
                $news = $stmt->fetch(PDO::FETCH_ASSOC);

                // Delete the news
                $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
                $result = $stmt->execute([$newsId]);

                if ($result) {
                    // Try to delete the image file if it exists
                    if ($news && $news['image'] && strpos($news['image'], 'uploads/') === 0) {
                        $imagePath = dirname(dirname(dirname(__DIR__))) . '/' . $news['image'];
                        if (file_exists($imagePath)) {
                            unlink($imagePath);
                        }
                    }

                    echo json_encode(['success' => true, 'message' => 'Berita berhasil dihapus']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menghapus berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting news: ' . $e->getMessage()]);
            }
            break;

        case 'add_news':
            try {
                $title = $_POST['title'] ?? '';
                $slug = $_POST['slug'] ?? '';
                $description = $_POST['description'] ?? '';
                $content = $_POST['content'] ?? '';
                $categoryId = $_POST['category_id'] ?? null;
                $status = $_POST['status'] ?? 'draft';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $tags = $_POST['tags'] ?? '';

                // Handle image upload
                $imageData = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['image'];

                    // Validate file
                    $allowedTypes = [
                        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                        'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif'
                    ];

                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format gambar tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 5 * 1024 * 1024) {
                        echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                        break;
                    }

                    // Save to uploads directory
                    $uploadsDir = dirname(dirname(dirname(__DIR__))) . '/uploads';
                    if (!is_dir($uploadsDir)) {
                        mkdir($uploadsDir, 0755, true);
                    }

                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'news_' . time() . '_' . uniqid() . '.' . $extension;
                    $filepath = $uploadsDir . '/' . $filename;

                    if (move_uploaded_file($file['tmp_name'], $filepath)) {
                        $imageData = 'uploads/' . $filename;
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Gagal mengupload gambar']);
                        break;
                    }
                }

                // Insert news
                $stmt = $pdo->prepare("
                    INSERT INTO posts (title, slug, description, content, category_id, status, featured, tags, image, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $result = $stmt->execute([
                    $title, $slug, $description, $content, $categoryId, $status, $featured, $tags, $imageData
                ]);

                if ($result) {
                    $newsId = $pdo->lastInsertId();
                    echo json_encode(['success' => true, 'message' => 'Berita berhasil ditambahkan', 'id' => $newsId]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menambahkan berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error adding news: ' . $e->getMessage()]);
            }
            break;

        case 'mark_notification_read':
            $notificationId = $_POST['id'] ?? null;
            if (!$notificationId) {
                echo json_encode(['success' => false, 'message' => 'Notification ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
                $stmt->execute([$notificationId]);
                echo json_encode(['success' => true, 'message' => 'Notification marked as read']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error marking notification as read']);
            }
            break;

        case 'mark_all_notifications_read':
            try {
                $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE is_read = 0");
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'All notifications marked as read']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error marking all notifications as read']);
            }
            break;

        case 'delete_notification':
            $notificationId = $_POST['id'] ?? null;
            if (!$notificationId) {
                echo json_encode(['success' => false, 'message' => 'Notification ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
                $stmt->execute([$notificationId]);
                echo json_encode(['success' => true, 'message' => 'Notification deleted']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting notification']);
            }
            break;

        case 'clear_all_notifications':
            try {
                $stmt = $pdo->prepare("DELETE FROM notifications");
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'All notifications cleared']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error clearing all notifications']);
            }
            break;

        case 'get_stats':
            try {
                $stats = [];

                // Total users
                try {
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users");
                    $stmt->execute();
                    $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
                } catch (Exception $e) {
                    $stats['total_users'] = 0;
                }

                // Total news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts");
                $stmt->execute();
                $stats['total_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Published news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts WHERE status = 'published'");
                $stmt->execute();
                $stats['published_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Draft news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts WHERE status = 'draft'");
                $stmt->execute();
                $stats['draft_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Total categories
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM categories WHERE is_active = 1");
                $stmt->execute();
                $stats['total_categories'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Total likes
                $stmt = $pdo->prepare("SELECT SUM(likes) as total FROM posts");
                $stmt->execute();
                $stats['total_likes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                // Total page views
                $stmt = $pdo->prepare("SELECT SUM(views) as total FROM posts");
                $stmt->execute();
                $stats['page_views'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                // Saved posts
                try {
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM saved");
                    $stmt->execute();
                    $stats['saved_posts'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
                } catch (Exception $e) {
                    $stats['saved_posts'] = 0;
                }

                echo json_encode(['success' => true, 'data' => $stats]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading stats: ' . $e->getMessage()]);
            }
            break;

        case 'get_recent_news':
            try {
                $limit = $_GET['limit'] ?? 5;
                $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                        FROM posts p
                        LEFT JOIN categories c ON p.category_id = c.id
                        ORDER BY p.created_at DESC
                        LIMIT " . (int)$limit;

                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $recentNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $recentNews]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading recent news: ' . $e->getMessage()]);
            }
            break;

        case 'get_popular_posts':
            try {
                $limit = $_GET['limit'] ?? 5;
                $stmt = $pdo->prepare("
                    SELECT p.*, c.name as category_name, c.color as category_color,
                           (p.views + p.likes * 2 + p.share * 3) as popularity_score
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.status = 'published'
                    ORDER BY popularity_score DESC, p.created_at DESC
                    LIMIT ?
                ");
                $stmt->execute([(int)$limit]);
                $popularPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $popularPosts]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading popular posts: ' . $e->getMessage()]);
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action: ' . $action]);
            break;
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
