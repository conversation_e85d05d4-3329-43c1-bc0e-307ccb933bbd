<?php
// Simple API endpoint for pengaturan
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Helper function to generate slug from title
function generateSlug($title) {
    // Convert to lowercase
    $slug = strtolower($title);

    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);

    // Remove multiple consecutive hyphens
    $slug = preg_replace('/-+/', '-', $slug);

    // Remove leading and trailing hyphens
    $slug = trim($slug, '-');

    // If slug is empty, generate a random one
    if (empty($slug)) {
        $slug = 'post-' . time();
    }

    return $slug;
}

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'react_news';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Helper function to add notifications
    function addNotification($pdo, $title, $message, $action_type, $entity_type, $type = 'info', $entity_id = null) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO notifications (title, message, action_type, entity_type, entity_id, type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$title, $message, $action_type, $entity_type, $entity_id, $type]);
            return true;
        } catch (Exception $e) {
            error_log("Error adding notification: " . $e->getMessage());
            return false;
        }
    }

    // Helper function to calculate time ago
    function timeAgo($datetime) {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'baru saja';
        if ($time < 3600) return floor($time/60) . ' menit yang lalu';
        if ($time < 86400) return floor($time/3600) . ' jam yang lalu';
        if ($time < 2592000) return floor($time/86400) . ' hari yang lalu';
        if ($time < 31536000) return floor($time/2592000) . ' bulan yang lalu';
        return floor($time/31536000) . ' tahun yang lalu';
    }

    // Helper function to generate avatar
    function generateAvatar($name) {
        $colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
        $initial = strtoupper(substr($name, 0, 1));
        $colorIndex = ord($initial) % count($colors);
        $color = $colors[$colorIndex];

        return [
            'initial' => $initial,
            'color' => $color,
            'name' => $name
        ];
    }

    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'get_pengaturan':
            $stmt = $pdo->prepare("
                SELECT p.*, l.logo_data, l.mime_type as logo_mime_type
                FROM pengaturan p
                LEFT JOIN logos l ON p.logo_id = l.id
                WHERE p.id = 1
            ");
            $stmt->execute();
            $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pengaturan) {
                // Create default if not exists - this should not happen if config.php ran properly
                echo json_encode(['success' => false, 'message' => 'Pengaturan not found']);
                break;
            }

            // Add logo_file_path for backward compatibility
            $pengaturan['logo_file_path'] = $pengaturan['logo_data'] ?? 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';

            echo json_encode(['success' => true, 'data' => $pengaturan]);
            break;

        case 'update_pengaturan':
            $nama_website = $_POST['nama_website'] ?? '';
            $deskripsi_website = $_POST['deskripsi_website'] ?? '';
            $warna_sidebar = $_POST['warna_sidebar'] ?? '#2563EB';
            $warna_sidebar_header = $_POST['warna_sidebar_header'] ?? '#1D4ED8';
            $warna_primary = $_POST['warna_primary'] ?? '#3B82F6';
            $warna_secondary = $_POST['warna_secondary'] ?? '#10B981';
            $warna_accent = $_POST['warna_accent'] ?? '#F59E0B';

            $newLogoId = null;

            // Handle logo upload
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['logo'];

                // Validate file - accept all common image formats
                $allowedTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                    'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif', 'image/ico',
                    'image/icon', 'image/x-icon', 'image/vnd.microsoft.icon'
                ];

                // Also check file extension as backup
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'tif', 'ico'];

                if (!in_array($file['type'], $allowedTypes) && !in_array($fileExtension, $allowedExtensions)) {
                    echo json_encode(['success' => false, 'message' => 'Tipe file tidak diizinkan. Gunakan format gambar yang valid (JPG, PNG, GIF, WebP, SVG, BMP, TIFF, ICO)']);
                    exit;
                }

                if ($file['size'] > 5 * 1024 * 1024) {
                    echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                    exit;
                }

                // Read file content and convert to base64
                $fileContent = file_get_contents($file['tmp_name']);
                if ($fileContent === false) {
                    echo json_encode(['success' => false, 'message' => 'Gagal membaca file logo']);
                    exit;
                }

                $base64Data = 'data:' . $file['type'] . ';base64,' . base64_encode($fileContent);

                // Save logo to database
                $stmt = $pdo->prepare("INSERT INTO logos (logo_data, mime_type, file_size) VALUES (?, ?, ?)");
                $result = $stmt->execute([$base64Data, $file['type'], $file['size']]);

                if ($result) {
                    $newLogoId = $pdo->lastInsertId();

                    // Deactivate old logos
                    $stmt = $pdo->prepare("UPDATE logos SET is_active = 0 WHERE id != ?");
                    $stmt->execute([$newLogoId]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menyimpan logo ke database']);
                    exit;
                }
            }

            // Update pengaturan
            $sql = "UPDATE pengaturan SET
                        nama_website = ?,
                        deskripsi_website = ?,
                        warna_sidebar = ?,
                        warna_sidebar_header = ?,
                        warna_primary = ?,
                        warna_secondary = ?,
                        warna_accent = ?";

            $params = [$nama_website, $deskripsi_website, $warna_sidebar, $warna_sidebar_header, $warna_primary, $warna_secondary, $warna_accent];

            if ($newLogoId) {
                $sql .= ", logo_id = ?";
                $params[] = $newLogoId;
            }

            $sql .= " WHERE id = 1";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

            if ($result) {
                // Add notification
                addNotification($pdo,
                    'Pengaturan Diperbarui',
                    'Pengaturan website berhasil diperbarui',
                    'update',
                    'settings',
                    'success'
                );

                echo json_encode(['success' => true, 'message' => 'Pengaturan berhasil disimpan']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Gagal menyimpan pengaturan']);
            }
            break;

        case 'get_categories':
            try {
                // Check if categories table exists
                $stmt = $pdo->prepare("SHOW TABLES LIKE 'categories'");
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $stmt = $pdo->prepare("
                        SELECT
                            c.*,
                            COUNT(p.id) as post_count
                        FROM categories c
                        LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
                        WHERE c.is_active = 1
                        GROUP BY c.id
                        ORDER BY c.name ASC
                    ");
                    $stmt->execute();
                    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } else {
                    // Return default categories if table doesn't exist
                    $categories = [
                        ['id' => 1, 'name' => 'Umum', 'slug' => 'umum', 'color' => '#6B7280', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 2, 'name' => 'Teknologi', 'slug' => 'teknologi', 'color' => '#3B82F6', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 3, 'name' => 'Bisnis', 'slug' => 'bisnis', 'color' => '#10B981', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 4, 'name' => 'Olahraga', 'slug' => 'olahraga', 'color' => '#F59E0B', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 5, 'name' => 'Hiburan', 'slug' => 'hiburan', 'color' => '#EF4444', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 6, 'name' => 'Politik', 'slug' => 'politik', 'color' => '#8B5CF6', 'is_active' => 1, 'post_count' => 0],
                        ['id' => 7, 'name' => 'Kesehatan', 'slug' => 'kesehatan', 'color' => '#06B6D4', 'is_active' => 1, 'post_count' => 0]
                    ];
                }

                echo json_encode(['success' => true, 'data' => $categories]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading categories: ' . $e->getMessage()]);
            }
            break;

        case 'get_news':
            try {
                $limit = $_GET['limit'] ?? null;
                $category = $_GET['category'] ?? null;
                $status = $_GET['status'] ?? 'published';

                $sql = "
                    SELECT
                        p.*,
                        c.name as category_name,
                        c.color as category_color,
                        c.slug as category_slug
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE 1=1
                ";

                $params = [];

                if ($status && $status !== 'all') {
                    $sql .= " AND p.status = ?";
                    $params[] = $status;
                }

                if ($category && $category !== 'all') {
                    // Check if category is ID or slug
                    if (is_numeric($category)) {
                        $sql .= " AND p.category_id = ?";
                        $params[] = $category;
                    } else {
                        $sql .= " AND c.slug = ?";
                        $params[] = $category;
                    }
                }

                $sql .= " ORDER BY p.created_at DESC";

                if ($limit) {
                    $sql .= " LIMIT ?";
                    $params[] = (int)$limit;
                }

                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $news = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $news]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading news: ' . $e->getMessage()]);
            }
            break;

        case 'get_news_by_id':
            $newsId = $_GET['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("
                    SELECT
                        p.*,
                        c.name as category_name,
                        c.color as category_color
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.id = ?
                ");
                $stmt->execute([$newsId]);
                $news = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($news) {
                    echo json_encode(['success' => true, 'data' => $news]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'News not found']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading news: ' . $e->getMessage()]);
            }
            break;

        case 'get_categories':
            try {
                $stmt = $pdo->prepare("
                    SELECT
                        c.*,
                        COUNT(p.id) as post_count
                    FROM categories c
                    LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
                    GROUP BY c.id
                    ORDER BY c.name ASC
                ");
                $stmt->execute();
                $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $categories]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading categories: ' . $e->getMessage()]);
            }
            break;

        case 'get_notifications':
            $limit = $_GET['limit'] ?? 10;

            // Check if notifications table exists
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'notifications'");
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                try {
                    // Use simple query without parameter binding for LIMIT
                    $sql = "SELECT * FROM notifications ORDER BY created_at DESC LIMIT " . (int)$limit;
                    $stmt = $pdo->query($sql);
                    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    // Try to get unread count, handle if is_read column doesn't exist
                    try {
                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0");
                        $stmt->execute();
                        $unreadCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                    } catch (Exception $e) {
                        // If is_read column doesn't exist, assume all are unread
                        $unreadCount = count($notifications);
                    }
                } catch (Exception $e) {
                    $notifications = [];
                    $unreadCount = 0;
                    error_log("Error in get_notifications: " . $e->getMessage());
                }
            } else {
                $notifications = [];
                $unreadCount = 0;
            }

            echo json_encode([
                'success' => true,
                'data' => $notifications,
                'unread_count' => $unreadCount
            ]);
            break;

        case 'toggle_like':
            $postId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'Post ID required']);
                break;
            }

            $ipAddress = $_SERVER['REMOTE_ADDR'];

            // Check if already liked
            $stmt = $pdo->prepare("SELECT id FROM post_likes WHERE post_id = ? AND ip_address = ?");
            $stmt->execute([$postId, $ipAddress]);
            $existingLike = $stmt->fetch();

            if ($existingLike) {
                // Remove like
                $stmt = $pdo->prepare("DELETE FROM post_likes WHERE post_id = ? AND ip_address = ?");
                $stmt->execute([$postId, $ipAddress]);

                // Decrease like count
                $stmt = $pdo->prepare("UPDATE posts SET likes = GREATEST(0, likes - 1) WHERE id = ?");
                $stmt->execute([$postId]);

                echo json_encode(['success' => true, 'liked' => false, 'message' => 'Like removed']);
            } else {
                // Add like
                $stmt = $pdo->prepare("INSERT INTO post_likes (post_id, ip_address, user_agent) VALUES (?, ?, ?)");
                $stmt->execute([$postId, $ipAddress, $_SERVER['HTTP_USER_AGENT'] ?? '']);

                // Increase like count
                $stmt = $pdo->prepare("UPDATE posts SET likes = likes + 1 WHERE id = ?");
                $stmt->execute([$postId]);

                echo json_encode(['success' => true, 'liked' => true, 'message' => 'Like added']);
            }
            break;

        case 'get_liked_posts':
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt = $pdo->prepare("SELECT post_id FROM post_likes WHERE ip_address = ?");
            $stmt->execute([$ipAddress]);
            $likedPosts = $stmt->fetchAll(PDO::FETCH_COLUMN);

            echo json_encode([
                'success' => true,
                'data' => $likedPosts,
                'ip_address' => $ipAddress
            ]);
            break;

        case 'add_saved_news':
        case 'remove_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['news_id'] ?? null;

            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            $ipAddress = $_SERVER['REMOTE_ADDR'];

            if ($action === 'add_saved_news') {
                // Add to saved
                $stmt = $pdo->prepare("INSERT IGNORE INTO saved (post_id, ip_address) VALUES (?, ?)");
                $stmt->execute([$postId, $ipAddress]);
                echo json_encode(['success' => true, 'message' => 'News saved']);
            } else {
                // Remove from saved
                $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ? AND ip_address = ?");
                $stmt->execute([$postId, $ipAddress]);
                echo json_encode(['success' => true, 'message' => 'News removed from saved']);
            }
            break;

        case 'get_saved_news':
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt = $pdo->prepare("
                SELECT p.*, c.name as category_name, c.color as category_color
                FROM saved s
                JOIN posts p ON s.post_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE s.ip_address = ?
                ORDER BY s.saved_at DESC
            ");
            $stmt->execute([$ipAddress]);
            $savedNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'data' => $savedNews]);
            break;

        case 'increment_views':
            $postId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$postId) {
                echo json_encode(['success' => false, 'message' => 'Post ID required']);
                break;
            }

            // Update view count
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $stmt->execute([$postId]);

            echo json_encode(['success' => true, 'message' => 'Views incremented']);
            break;

        case 'increment_video_view':
            $videoId = $_GET['video_id'] ?? $_POST['video_id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                // Update video view count
                $stmt = $pdo->prepare("UPDATE videos SET views = views + 1 WHERE id = ?");
                $stmt->execute([$videoId]);

                // Get updated view count
                $stmt = $pdo->prepare("SELECT views FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                $viewCount = $video ? $video['views'] : 0;

                echo json_encode([
                    'success' => true,
                    'message' => 'Video view incremented',
                    'views' => $viewCount
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error incrementing video view: ' . $e->getMessage()]);
            }
            break;

        case 'toggle_video_like':
            $videoId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

                // Check if user already liked this video
                $stmt = $pdo->prepare("SELECT id FROM video_likes WHERE video_id = ? AND ip_address = ?");
                $stmt->execute([$videoId, $ipAddress]);
                $existingLike = $stmt->fetch();

                if ($existingLike) {
                    // Unlike - remove like
                    $stmt = $pdo->prepare("DELETE FROM video_likes WHERE video_id = ? AND ip_address = ?");
                    $stmt->execute([$videoId, $ipAddress]);

                    // Decrease like count
                    $stmt = $pdo->prepare("UPDATE videos SET likes = GREATEST(0, likes - 1) WHERE id = ?");
                    $stmt->execute([$videoId]);

                    $action = 'unliked';
                    $message = 'Video unliked';
                } else {
                    // Like - add like
                    $stmt = $pdo->prepare("INSERT INTO video_likes (video_id, ip_address, user_agent) VALUES (?, ?, ?)");
                    $stmt->execute([$videoId, $ipAddress, $userAgent]);

                    // Increase like count
                    $stmt = $pdo->prepare("UPDATE videos SET likes = likes + 1 WHERE id = ?");
                    $stmt->execute([$videoId]);

                    $action = 'liked';
                    $message = 'Video liked';
                }

                // Get updated like count
                $stmt = $pdo->prepare("SELECT likes FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                $likeCount = $video ? $video['likes'] : 0;

                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'likes' => $likeCount,
                    'action' => $action,
                    'is_liked' => ($action === 'liked')
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error toggling video like: ' . $e->getMessage()]);
            }
            break;

        case 'increment_video_share':
            $videoId = $_GET['id'] ?? $_POST['id'] ?? null;
            $platform = $_GET['platform'] ?? $_POST['platform'] ?? 'direct';

            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

                // Track share in video_shares table
                $stmt = $pdo->prepare("INSERT INTO video_shares (video_id, ip_address, user_agent) VALUES (?, ?, ?)");
                $stmt->execute([$videoId, $ipAddress, $userAgent]);

                // Increment share count in videos table
                $stmt = $pdo->prepare("UPDATE videos SET shares = shares + 1 WHERE id = ?");
                $stmt->execute([$videoId]);

                // Get updated share count
                $stmt = $pdo->prepare("SELECT shares FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                $shareCount = $video ? $video['shares'] : 0;

                echo json_encode([
                    'success' => true,
                    'message' => 'Video shared successfully',
                    'shares' => $shareCount,
                    'platform' => $platform
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error sharing video: ' . $e->getMessage()]);
            }
            break;

        case 'get_video_comments':
            $videoId = $_GET['video_id'] ?? $_POST['video_id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                // Get comments for video from video_comment table
                $stmt = $pdo->prepare("
                    SELECT
                        id,
                        video_id,
                        user_name,
                        comment_text as comment,
                        likes,
                        created_at,
                        DATE_FORMAT(created_at, '%d %M %Y %H:%i') as formatted_date
                    FROM video_comment
                    WHERE video_id = ?
                    ORDER BY created_at DESC
                ");
                $stmt->execute([$videoId]);
                $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Format comments for display
                foreach ($comments as &$comment) {
                    $comment['time_ago'] = timeAgo($comment['created_at']);
                    $comment['user_avatar'] = generateAvatar($comment['user_name']);
                }

                echo json_encode([
                    'success' => true,
                    'comments' => $comments,
                    'count' => count($comments)
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Error loading comments: ' . $e->getMessage(),
                    'comments' => [],
                    'count' => 0
                ]);
            }
            break;

        case 'add_video_comment':
            $videoId = $_POST['video_id'] ?? null;
            $comment = trim($_POST['comment'] ?? '');
            $userName = trim($_POST['user_name'] ?? 'Anonymous');

            if (!$videoId || empty($comment)) {
                echo json_encode(['success' => false, 'message' => 'Video ID and comment required']);
                break;
            }

            // Validate comment length
            if (strlen($comment) < 3) {
                echo json_encode(['success' => false, 'message' => 'Comment must be at least 3 characters long']);
                break;
            }

            if (strlen($comment) > 1000) {
                echo json_encode(['success' => false, 'message' => 'Comment must be less than 1000 characters']);
                break;
            }

            try {
                // Check if video exists
                $stmt = $pdo->prepare("SELECT id FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                if (!$stmt->fetch()) {
                    echo json_encode(['success' => false, 'message' => 'Video not found']);
                    break;
                }

                // Get user IP and user agent
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

                // Add comment to video_comment table
                $stmt = $pdo->prepare("
                    INSERT INTO video_comment (video_id, user_name, comment_text, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$videoId, $userName, $comment, $ipAddress, $userAgent]);
                $commentId = $pdo->lastInsertId();

                // Update comments count in videos table
                $stmt = $pdo->prepare("UPDATE videos SET comments_count = comments_count + 1 WHERE id = ?");
                $stmt->execute([$videoId]);

                // Get the new comment data
                $stmt = $pdo->prepare("
                    SELECT
                        id,
                        video_id,
                        user_name,
                        comment_text as comment,
                        likes,
                        created_at,
                        DATE_FORMAT(created_at, '%d %M %Y %H:%i') as formatted_date
                    FROM video_comment
                    WHERE id = ?
                ");
                $stmt->execute([$commentId]);
                $newComment = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($newComment) {
                    $newComment['time_ago'] = timeAgo($newComment['created_at']);
                    $newComment['user_avatar'] = generateAvatar($newComment['user_name']);
                }

                // Add notification
                addNotification($pdo,
                    'Komentar Video Baru',
                    "Komentar baru ditambahkan pada video",
                    'create',
                    'video',
                    'info',
                    $videoId
                );

                echo json_encode([
                    'success' => true,
                    'message' => 'Comment added successfully',
                    'comment' => $newComment
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error adding comment: ' . $e->getMessage()]);
            }
            break;

        case 'like_video_comment':
            $commentId = $_POST['comment_id'] ?? null;
            if (!$commentId) {
                echo json_encode(['success' => false, 'message' => 'Comment ID required']);
                break;
            }

            try {
                // Increment comment likes
                $stmt = $pdo->prepare("UPDATE video_comment SET likes = likes + 1 WHERE id = ?");
                $stmt->execute([$commentId]);

                // Get updated like count
                $stmt = $pdo->prepare("SELECT likes FROM video_comment WHERE id = ?");
                $stmt->execute([$commentId]);
                $comment = $stmt->fetch(PDO::FETCH_ASSOC);

                $likeCount = $comment ? $comment['likes'] : 0;

                echo json_encode([
                    'success' => true,
                    'message' => 'Comment liked',
                    'likes' => $likeCount
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error liking comment: ' . $e->getMessage()]);
            }
            break;

        case 'delete_video_comment':
            $commentId = $_POST['comment_id'] ?? $_GET['comment_id'] ?? null;
            if (!$commentId) {
                echo json_encode(['success' => false, 'message' => 'Comment ID required']);
                break;
            }

            try {
                // Get comment info before deleting
                $stmt = $pdo->prepare("SELECT video_id FROM video_comment WHERE id = ?");
                $stmt->execute([$commentId]);
                $comment = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$comment) {
                    echo json_encode(['success' => false, 'message' => 'Comment not found']);
                    break;
                }

                // Delete comment
                $stmt = $pdo->prepare("DELETE FROM video_comment WHERE id = ?");
                $stmt->execute([$commentId]);

                // Update comments count in videos table
                $stmt = $pdo->prepare("UPDATE videos SET comments_count = GREATEST(0, comments_count - 1) WHERE id = ?");
                $stmt->execute([$comment['video_id']]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Comment deleted successfully'
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting comment: ' . $e->getMessage()]);
            }
            break;

        case 'toggle_video_save':
            $videoId = $_GET['id'] ?? $_POST['id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

                // Check if user already saved this video
                $stmt = $pdo->prepare("SELECT id FROM video_saves WHERE video_id = ? AND ip_address = ?");
                $stmt->execute([$videoId, $ipAddress]);
                $existingSave = $stmt->fetch();

                if ($existingSave) {
                    // Unsave - remove save
                    $stmt = $pdo->prepare("DELETE FROM video_saves WHERE video_id = ? AND ip_address = ?");
                    $stmt->execute([$videoId, $ipAddress]);

                    $action = 'unsaved';
                    $message = 'Video dihapus dari simpanan';
                } else {
                    // Save - add save with all required columns
                    $stmt = $pdo->prepare("INSERT INTO video_saves (video_id, ip_address, user_agent, saved_at) VALUES (?, ?, ?, NOW())");
                    $stmt->execute([$videoId, $ipAddress, $userAgent]);

                    $action = 'saved';
                    $message = 'Video berhasil disimpan';
                }

                // Get save count
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM video_saves WHERE video_id = ?");
                $stmt->execute([$videoId]);
                $saveCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'saves' => $saveCount,
                    'action' => $action,
                    'is_saved' => ($action === 'saved')
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error toggling video save: ' . $e->getMessage()]);
            }
            break;

        case 'check_video_save_status':
            $videoId = $_GET['video_id'] ?? $_POST['video_id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

                // Check if user already saved this video
                $stmt = $pdo->prepare("SELECT id FROM video_saves WHERE video_id = ? AND ip_address = ?");
                $stmt->execute([$videoId, $ipAddress]);
                $existingSave = $stmt->fetch();

                // Get total save count
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM video_saves WHERE video_id = ?");
                $stmt->execute([$videoId]);
                $saveCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

                echo json_encode([
                    'success' => true,
                    'is_saved' => !empty($existingSave),
                    'saves' => $saveCount
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error checking save status: ' . $e->getMessage()]);
            }
            break;

        case 'check_video_like_status':
            $videoId = $_GET['video_id'] ?? $_POST['video_id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

                // Check if user already liked this video
                $stmt = $pdo->prepare("SELECT id FROM video_likes WHERE video_id = ? AND ip_address = ?");
                $stmt->execute([$videoId, $ipAddress]);
                $existingLike = $stmt->fetch();

                // Get total like count
                $stmt = $pdo->prepare("SELECT likes FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);
                $likeCount = $video['likes'] ?? 0;

                echo json_encode([
                    'success' => true,
                    'is_liked' => !empty($existingLike),
                    'likes' => $likeCount
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error checking like status: ' . $e->getMessage()]);
            }
            break;

        case 'get_video_share_count':
            $videoId = $_GET['video_id'] ?? $_POST['video_id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                // Get share count from videos table
                $stmt = $pdo->prepare("SELECT shares FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);
                $shareCount = $video['shares'] ?? 0;

                echo json_encode([
                    'success' => true,
                    'shares' => $shareCount
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error getting share count: ' . $e->getMessage()]);
            }
            break;

        case 'get_saved_videos':
            try {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

                // Get saved videos for this IP
                $stmt = $pdo->prepare("
                    SELECT v.*, vs.saved_at
                    FROM videos v
                    INNER JOIN video_saves vs ON v.id = vs.video_id
                    WHERE vs.ip_address = ?
                    ORDER BY vs.saved_at DESC
                ");
                $stmt->execute([$ipAddress]);
                $savedVideos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'videos' => $savedVideos,
                    'count' => count($savedVideos)
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error getting saved videos: ' . $e->getMessage()]);
            }
            break;

        case 'update_news':
            $newsId = $_POST['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                $title = $_POST['title'] ?? '';
                $slug = $_POST['slug'] ?? '';
                $description = $_POST['description'] ?? '';
                $content = $_POST['content'] ?? '';
                $categoryId = $_POST['category_id'] ?? null;
                $status = $_POST['status'] ?? 'draft';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $tags = $_POST['tags'] ?? '';

                // Validate required fields
                if (empty($title)) {
                    echo json_encode(['success' => false, 'message' => 'Judul harus diisi']);
                    break;
                }

                // Generate slug if empty
                if (empty($slug)) {
                    $slug = generateSlug($title);
                }

                // Ensure slug is unique (exclude current news)
                $originalSlug = $slug;
                $counter = 1;
                while (true) {
                    $stmt = $pdo->prepare("SELECT id FROM posts WHERE slug = ? AND id != ?");
                    $stmt->execute([$slug, $newsId]);
                    if (!$stmt->fetch()) {
                        break; // Slug is unique
                    }
                    $slug = $originalSlug . '-' . $counter;
                    $counter++;
                }

                // Handle image upload
                $imageData = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['image'];

                    // Validate file
                    $allowedTypes = [
                        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                        'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif'
                    ];

                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format gambar tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 5 * 1024 * 1024) {
                        echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                        break;
                    }

                    // Save to uploads directory
                    $uploadsDir = dirname(dirname(dirname(__DIR__))) . '/uploads';
                    if (!is_dir($uploadsDir)) {
                        mkdir($uploadsDir, 0755, true);
                    }

                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'news_' . time() . '_' . uniqid() . '.' . $extension;
                    $filepath = $uploadsDir . '/' . $filename;

                    if (move_uploaded_file($file['tmp_name'], $filepath)) {
                        $imageData = '/react-news/frontend/uploads/' . $filename;
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Gagal mengupload gambar']);
                        break;
                    }
                }

                // Update news
                $sql = "UPDATE posts SET
                            title = ?,
                            slug = ?,
                            description = ?,
                            content = ?,
                            category_id = ?,
                            status = ?,
                            featured = ?,
                            tags = ?,
                            updated_at = NOW()";

                $params = [$title, $slug, $description, $content, $categoryId, $status, $featured, $tags];

                if ($imageData) {
                    $sql .= ", image = ?";
                    $params[] = $imageData;
                }

                $sql .= " WHERE id = ?";
                $params[] = $newsId;

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);

                if ($result) {
                    // Add notification
                    addNotification($pdo,
                        'Berita Diperbarui',
                        "Berita '$title' berhasil diperbarui",
                        'update',
                        'news',
                        'success',
                        $newsId
                    );

                    echo json_encode(['success' => true, 'message' => 'Berita berhasil diperbarui']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal memperbarui berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error updating news: ' . $e->getMessage()]);
            }
            break;

        case 'delete_news':
            $newsId = $_POST['id'] ?? $_GET['id'] ?? null;
            if (!$newsId) {
                echo json_encode(['success' => false, 'message' => 'News ID required']);
                break;
            }

            try {
                // Get image path before deleting
                $stmt = $pdo->prepare("SELECT image FROM posts WHERE id = ?");
                $stmt->execute([$newsId]);
                $news = $stmt->fetch(PDO::FETCH_ASSOC);

                // Delete the news
                $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
                $result = $stmt->execute([$newsId]);

                if ($result) {
                    // Try to delete the image file if it exists
                    if ($news && $news['image']) {
                        if (strpos($news['image'], '/react-news/frontend/uploads/') === 0) {
                            $filename = str_replace('/react-news/frontend/uploads/', '', $news['image']);
                            $imagePath = dirname(dirname(dirname(__DIR__))) . '/uploads/' . $filename;
                            if (file_exists($imagePath)) {
                                unlink($imagePath);
                            }
                        }
                    }

                    // Add notification
                    $newsTitle = $news['title'] ?? 'Berita';
                    addNotification($pdo,
                        'Berita Dihapus',
                        "Berita berhasil dihapus",
                        'delete',
                        'news',
                        'warning',
                        $newsId
                    );

                    echo json_encode(['success' => true, 'message' => 'Berita berhasil dihapus']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menghapus berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting news: ' . $e->getMessage()]);
            }
            break;

        case 'add_news':
            try {
                $title = $_POST['title'] ?? '';
                $slug = $_POST['slug'] ?? '';
                $description = $_POST['description'] ?? '';
                $content = $_POST['content'] ?? '';
                $categoryId = $_POST['category_id'] ?? null;
                $status = $_POST['status'] ?? 'draft';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $tags = $_POST['tags'] ?? '';

                // Validate required fields
                if (empty($title)) {
                    echo json_encode(['success' => false, 'message' => 'Judul harus diisi']);
                    break;
                }

                // Generate slug if empty
                if (empty($slug)) {
                    $slug = generateSlug($title);
                }

                // Ensure slug is unique
                $originalSlug = $slug;
                $counter = 1;
                while (true) {
                    $stmt = $pdo->prepare("SELECT id FROM posts WHERE slug = ?");
                    $stmt->execute([$slug]);
                    if (!$stmt->fetch()) {
                        break; // Slug is unique
                    }
                    $slug = $originalSlug . '-' . $counter;
                    $counter++;
                }

                // Handle image upload
                $imageData = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['image'];

                    // Validate file
                    $allowedTypes = [
                        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                        'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif'
                    ];

                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format gambar tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 5 * 1024 * 1024) {
                        echo json_encode(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)']);
                        break;
                    }

                    // Save to uploads directory
                    $uploadsDir = dirname(dirname(dirname(__DIR__))) . '/uploads';
                    if (!is_dir($uploadsDir)) {
                        mkdir($uploadsDir, 0755, true);
                    }

                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'news_' . time() . '_' . uniqid() . '.' . $extension;
                    $filepath = $uploadsDir . '/' . $filename;

                    if (move_uploaded_file($file['tmp_name'], $filepath)) {
                        $imageData = '/react-news/frontend/uploads/' . $filename;
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Gagal mengupload gambar']);
                        break;
                    }
                }

                // Insert news
                $stmt = $pdo->prepare("
                    INSERT INTO posts (title, slug, description, content, category_id, status, featured, tags, image, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $result = $stmt->execute([
                    $title, $slug, $description, $content, $categoryId, $status, $featured, $tags, $imageData
                ]);

                if ($result) {
                    $newsId = $pdo->lastInsertId();

                    // Add notification
                    addNotification($pdo,
                        'Berita Baru Ditambahkan',
                        "Berita '$title' berhasil ditambahkan",
                        'create',
                        'news',
                        'success',
                        $newsId
                    );

                    echo json_encode(['success' => true, 'message' => 'Berita berhasil ditambahkan', 'id' => $newsId]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menambahkan berita']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error adding news: ' . $e->getMessage()]);
            }
            break;

        case 'get_videos':
            try {
                $limit = $_GET['limit'] ?? null;
                $status = $_GET['status'] ?? null;

                // Build query step by step
                $sql = "SELECT id, title, description, video_type, youtube_url, status, tags, created_at, updated_at FROM videos";
                $whereConditions = [];
                $params = [];

                if ($status && $status !== 'all') {
                    $whereConditions[] = "status = ?";
                    $params[] = $status;
                }

                if (!empty($whereConditions)) {
                    $sql .= " WHERE " . implode(" AND ", $whereConditions);
                }

                $sql .= " ORDER BY created_at DESC";

                if ($limit) {
                    $sql .= " LIMIT " . (int)$limit;
                }

                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $videos]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading videos: ' . $e->getMessage()]);
            }
            break;

        case 'add_user':
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $role = $_POST['role'] ?? 'user';
            $status = $_POST['status'] ?? 'active';

            if (empty($name) || empty($email) || empty($password)) {
                echo json_encode(['success' => false, 'message' => 'Nama, email, dan password harus diisi']);
                break;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                echo json_encode(['success' => false, 'message' => 'Format email tidak valid']);
                break;
            }

            try {
                // Create table if not exists
                $pdo->exec("CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    role ENUM('admin', 'user') DEFAULT 'user',
                    email_verified_at TIMESTAMP NULL,
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    last_login_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");

                // Check email exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    echo json_encode(['success' => false, 'message' => 'Email sudah terdaftar']);
                    break;
                }

                // Insert user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)");
                $result = $stmt->execute([$name, $email, $hashedPassword, $role, $status]);

                if ($result) {
                    addNotification($pdo, 'User Baru', "User '$name' berhasil ditambahkan", 'create', 'user', 'success', $pdo->lastInsertId());
                    echo json_encode(['success' => true, 'message' => 'User berhasil ditambahkan', 'user_id' => $pdo->lastInsertId()]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menambahkan user']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'get_user_stats':
            try {
                $stats = [];

                // Total users
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total'] = $result['total'] ?? 0;

                // Active users
                $stmt = $pdo->prepare("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['active'] = $result['active'] ?? 0;

                // Admin users
                $stmt = $pdo->prepare("SELECT COUNT(*) as admin FROM users WHERE role = 'admin'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['admin'] = $result['admin'] ?? 0;

                // Users with tokens (placeholder)
                $stats['with_token'] = 0;

                echo json_encode(['success' => true, 'stats' => $stats]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'get_video':
            $videoId = $_GET['id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("SELECT * FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($video) {
                    echo json_encode(['success' => true, 'data' => $video]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Video not found']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading video: ' . $e->getMessage()]);
            }
            break;

        case 'update_video':
            $videoId = $_POST['id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                $title = $_POST['title'] ?? '';
                $description = $_POST['description'] ?? '';
                $videoType = $_POST['video_type'] ?? 'youtube';
                $youtubeUrl = $_POST['youtube_url'] ?? '';
                $status = $_POST['status'] ?? 'draft';
                $tags = $_POST['tags'] ?? '';

                // Handle video upload for base64 type
                $videoData = null;
                $videoSize = null;
                $videoFormat = null;

                if ($videoType === 'upload' && isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['video_file'];

                    // Validate video file
                    $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format video tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 100 * 1024 * 1024) { // 100MB limit
                        echo json_encode(['success' => false, 'message' => 'Ukuran video terlalu besar (maksimal 100MB)']);
                        break;
                    }

                    // Convert to base64
                    $fileContent = file_get_contents($file['tmp_name']);
                    $videoData = base64_encode($fileContent);
                    $videoSize = $file['size'];
                    $videoFormat = pathinfo($file['name'], PATHINFO_EXTENSION);
                }

                // Update video
                $sql = "UPDATE videos SET
                            title = ?,
                            description = ?,
                            video_type = ?,
                            status = ?,
                            tags = ?,
                            updated_at = NOW()";

                $params = [$title, $description, $videoType, $status, $tags];

                if ($videoType === 'youtube') {
                    $sql .= ", youtube_url = ?";
                    $params[] = $youtubeUrl;
                } elseif ($videoData) {
                    $sql .= ", video_base64 = ?, video_size = ?, video_format = ?";
                    $params = array_merge($params, [$videoData, $videoSize, $videoFormat]);
                }

                $sql .= " WHERE id = ?";
                $params[] = $videoId;

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);

                if ($result) {
                    // Add notification
                    addNotification($pdo,
                        'Video Diperbarui',
                        "Video '$title' berhasil diperbarui",
                        'update',
                        'video',
                        'success',
                        $videoId
                    );

                    echo json_encode(['success' => true, 'message' => 'Video berhasil diperbarui']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal memperbarui video']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error updating video: ' . $e->getMessage()]);
            }
            break;

        case 'delete_video':
            $videoId = $_POST['id'] ?? $_GET['id'] ?? null;
            if (!$videoId) {
                echo json_encode(['success' => false, 'message' => 'Video ID required']);
                break;
            }

            try {
                // Get video title before deleting
                $stmt = $pdo->prepare("SELECT title FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);
                $videoTitle = $video ? $video['title'] : 'Video';

                // Delete the video
                $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
                $result = $stmt->execute([$videoId]);

                if ($result) {
                    // Add notification
                    addNotification($pdo,
                        'Video Dihapus',
                        "Video '$videoTitle' berhasil dihapus",
                        'delete',
                        'video',
                        'warning',
                        $videoId
                    );

                    echo json_encode(['success' => true, 'message' => 'Video berhasil dihapus']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menghapus video']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting video: ' . $e->getMessage()]);
            }
            break;

        case 'add_video':
            try {
                $title = $_POST['title'] ?? '';
                $description = $_POST['description'] ?? '';
                $videoType = $_POST['video_type'] ?? 'youtube';
                $youtubeUrl = $_POST['youtube_url'] ?? '';
                $status = $_POST['status'] ?? 'draft';
                $tags = $_POST['tags'] ?? '';

                // Handle video upload for base64 type
                $videoData = null;
                $videoSize = null;
                $videoFormat = null;

                if ($videoType === 'upload' && isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['video_file'];

                    // Validate video file
                    $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
                    if (!in_array($file['type'], $allowedTypes)) {
                        echo json_encode(['success' => false, 'message' => 'Format video tidak didukung']);
                        break;
                    }

                    if ($file['size'] > 100 * 1024 * 1024) { // 100MB limit
                        echo json_encode(['success' => false, 'message' => 'Ukuran video terlalu besar (maksimal 100MB)']);
                        break;
                    }

                    // Convert to base64
                    $fileContent = file_get_contents($file['tmp_name']);
                    $videoData = base64_encode($fileContent);
                    $videoSize = $file['size'];
                    $videoFormat = pathinfo($file['name'], PATHINFO_EXTENSION);
                }

                // Insert video
                if ($videoType === 'youtube') {
                    $stmt = $pdo->prepare("
                        INSERT INTO videos (title, description, video_type, youtube_url, status, tags, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");
                    $result = $stmt->execute([$title, $description, $videoType, $youtubeUrl, $status, $tags]);
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO videos (title, description, video_type, video_base64, video_size, video_format, status, tags, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");
                    $result = $stmt->execute([$title, $description, $videoType, $videoData, $videoSize, $videoFormat, $status, $tags]);
                }

                if ($result) {
                    $videoId = $pdo->lastInsertId();

                    // Add notification
                    addNotification($pdo,
                        'Video Baru Ditambahkan',
                        "Video '$title' berhasil ditambahkan",
                        'create',
                        'video',
                        'success',
                        $videoId
                    );

                    echo json_encode(['success' => true, 'message' => 'Video berhasil ditambahkan', 'id' => $videoId]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal menambahkan video']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error adding video: ' . $e->getMessage()]);
            }
            break;

        case 'admin_logout':
            try {
                // Start session if not already started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                // Destroy all session data
                $_SESSION = array();

                // Delete session cookie if it exists
                if (ini_get("session.use_cookies")) {
                    $params = session_get_cookie_params();
                    setcookie(session_name(), '', time() - 42000,
                        $params["path"], $params["domain"],
                        $params["secure"], $params["httponly"]
                    );
                }

                // Destroy the session
                session_destroy();

                // Clear any authentication cookies
                setcookie('admin_token', '', time() - 3600, '/');
                setcookie('admin_user', '', time() - 3600, '/');
                setcookie('user_id', '', time() - 3600, '/');
                setcookie('username', '', time() - 3600, '/');

                echo json_encode([
                    'success' => true,
                    'message' => 'Logout berhasil',
                    'redirect' => '/react-news/frontend/src/pages/admin/auth/login.php'
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error during logout: ' . $e->getMessage()]);
            }
            break;

        case 'mark_notification_read':
            $notificationId = $_POST['id'] ?? null;
            if (!$notificationId) {
                echo json_encode(['success' => false, 'message' => 'Notification ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
                $stmt->execute([$notificationId]);
                echo json_encode(['success' => true, 'message' => 'Notification marked as read']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error marking notification as read']);
            }
            break;

        case 'mark_all_notifications_read':
            try {
                $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE is_read = 0");
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'All notifications marked as read']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error marking all notifications as read']);
            }
            break;

        case 'delete_notification':
            $notificationId = $_POST['id'] ?? null;
            if (!$notificationId) {
                echo json_encode(['success' => false, 'message' => 'Notification ID required']);
                break;
            }

            try {
                $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
                $stmt->execute([$notificationId]);
                echo json_encode(['success' => true, 'message' => 'Notification deleted']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting notification']);
            }
            break;

        case 'clear_all_notifications':
            try {
                $stmt = $pdo->prepare("DELETE FROM notifications");
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'All notifications cleared']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error clearing all notifications']);
            }
            break;

        case 'get_stats':
            try {
                $stats = [];

                // Total users
                try {
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users");
                    $stmt->execute();
                    $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
                } catch (Exception $e) {
                    $stats['total_users'] = 0;
                }

                // Total news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts");
                $stmt->execute();
                $stats['total_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Published news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts WHERE status = 'published'");
                $stmt->execute();
                $stats['published_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Draft news
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM posts WHERE status = 'draft'");
                $stmt->execute();
                $stats['draft_news'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Total categories
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM categories WHERE is_active = 1");
                $stmt->execute();
                $stats['total_categories'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

                // Total likes
                $stmt = $pdo->prepare("SELECT SUM(likes) as total FROM posts");
                $stmt->execute();
                $stats['total_likes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                // Total page views
                $stmt = $pdo->prepare("SELECT SUM(views) as total FROM posts");
                $stmt->execute();
                $stats['page_views'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                // Saved posts
                try {
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM saved");
                    $stmt->execute();
                    $stats['saved_posts'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
                } catch (Exception $e) {
                    $stats['saved_posts'] = 0;
                }

                echo json_encode(['success' => true, 'data' => $stats]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading stats: ' . $e->getMessage()]);
            }
            break;

        case 'get_recent_news':
            try {
                $limit = $_GET['limit'] ?? 5;
                $sql = "SELECT p.*, c.name as category_name, c.color as category_color
                        FROM posts p
                        LEFT JOIN categories c ON p.category_id = c.id
                        ORDER BY p.created_at DESC
                        LIMIT " . (int)$limit;

                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $recentNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $recentNews]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading recent news: ' . $e->getMessage()]);
            }
            break;

        case 'get_popular_posts':
            try {
                $limit = $_GET['limit'] ?? 5;
                $stmt = $pdo->prepare("
                    SELECT p.*, c.name as category_name, c.color as category_color,
                           (p.views + p.likes * 2 + p.share * 3) as popularity_score
                    FROM posts p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.status = 'published'
                    ORDER BY popularity_score DESC, p.created_at DESC
                    LIMIT ?
                ");
                $stmt->execute([(int)$limit]);
                $popularPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'data' => $popularPosts]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error loading popular posts: ' . $e->getMessage()]);
            }
            break;

        case 'login':
            $input = json_decode(file_get_contents('php://input'), true);
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($email) || empty($password)) {
                echo json_encode(['success' => false, 'message' => 'Email dan password harus diisi']);
                break;
            }

            try {
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
                $stmt->execute([$email]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user && password_verify($password, $user['password'])) {
                    // Update last login
                    $stmt = $pdo->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
                    $stmt->execute([$user['id']]);

                    // Generate simple token
                    $token = base64_encode($user['id'] . ':' . time());

                    echo json_encode([
                        'success' => true,
                        'message' => 'Login berhasil',
                        'token' => $token,
                        'user' => [
                            'id' => $user['id'],
                            'name' => $user['name'],
                            'email' => $user['email'],
                            'role' => $user['role']
                        ]
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Email atau password salah']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'register':
            $input = json_decode(file_get_contents('php://input'), true);
            $name = $input['name'] ?? '';
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($name) || empty($email) || empty($password)) {
                echo json_encode(['success' => false, 'message' => 'Semua field harus diisi']);
                break;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                echo json_encode(['success' => false, 'message' => 'Format email tidak valid']);
                break;
            }

            try {
                // Check if email exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    echo json_encode(['success' => false, 'message' => 'Email sudah terdaftar']);
                    break;
                }

                // Insert user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, 'user', 'active')");
                $result = $stmt->execute([$name, $email, $hashedPassword]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Registrasi berhasil']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Gagal mendaftar']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'verify_token':
            $input = json_decode(file_get_contents('php://input'), true);
            $token = $input['token'] ?? '';

            if (empty($token)) {
                echo json_encode(['success' => false, 'message' => 'Token tidak valid']);
                break;
            }

            try {
                $decoded = base64_decode($token);
                $parts = explode(':', $decoded);

                if (count($parts) !== 2) {
                    echo json_encode(['success' => false, 'message' => 'Token tidak valid']);
                    break;
                }

                $userId = $parts[0];
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND status = 'active'");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user) {
                    echo json_encode([
                        'success' => true,
                        'user' => [
                            'id' => $user['id'],
                            'name' => $user['name'],
                            'email' => $user['email'],
                            'role' => $user['role']
                        ]
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Token tidak valid']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Token tidak valid']);
            }
            break;

        case 'forgot_password':
            $input = json_decode(file_get_contents('php://input'), true);
            $email = $input['email'] ?? '';

            if (empty($email)) {
                echo json_encode(['success' => false, 'message' => 'Email harus diisi']);
                break;
            }

            try {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user) {
                    // Generate reset token
                    $resetToken = bin2hex(random_bytes(32));

                    // For simplicity, just return the token (in real app, send via email)
                    echo json_encode([
                        'success' => true,
                        'message' => 'Token reset password telah dibuat',
                        'reset_token' => $resetToken
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Email tidak ditemukan']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'reset_password':
            $input = json_decode(file_get_contents('php://input'), true);
            $email = $input['email'] ?? '';
            $token = $input['token'] ?? '';
            $newPassword = $input['newPassword'] ?? '';

            if (empty($email) || empty($token) || empty($newPassword)) {
                echo json_encode(['success' => false, 'message' => 'Semua field harus diisi']);
                break;
            }

            try {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user) {
                    // Update password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
                    $result = $stmt->execute([$hashedPassword, $email]);

                    if ($result) {
                        echo json_encode(['success' => true, 'message' => 'Password berhasil direset']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Gagal mereset password']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => 'Email tidak ditemukan']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action: ' . $action]);
            break;
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
