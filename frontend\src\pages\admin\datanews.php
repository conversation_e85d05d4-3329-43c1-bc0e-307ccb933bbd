<?php
// Start session
session_start();

// Check if admin is logged in (simplified check)
// You can add more robust authentication here

// Include database connection
require_once 'connect.php';

// Get news ID from URL parameter
$newsId = $_GET['id'] ?? null;
$news = null;

if ($newsId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name, c.color as category_color,
                   u.name as author_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN users u ON p.user_id = u.id
            WHERE p.id = ?
        ");
        $stmt->execute([$newsId]);
        $news = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $error = "Error loading news: " . $e->getMessage();
    }
}

if (!$news) {
    header('Location: dashboard.php?page=news');
    exit();
}

// Format date
function formatDate($dateString) {
    return date('d M Y H:i', strtotime($dateString));
}

// Get website settings
try {
    $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $settings = [
        'nama_website' => 'React News Admin',
        'warna_primary' => '#3B82F6'
    ];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Berita - <?php echo htmlspecialchars($news['title']); ?> | <?php echo $settings['nama_website']; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: <?php echo $settings['warna_primary'] ?? '#3B82F6'; ?>;
        }
        
        .primary-bg { background-color: var(--primary-color); }
        .primary-text { color: var(--primary-color); }
        .primary-border { border-color: var(--primary-color); }
        
        .content-area {
            line-height: 1.8;
        }
        
        .content-area h1, .content-area h2, .content-area h3 {
            margin: 1.5rem 0 1rem 0;
            font-weight: bold;
        }
        
        .content-area h1 { font-size: 2rem; }
        .content-area h2 { font-size: 1.5rem; }
        .content-area h3 { font-size: 1.25rem; }
        
        .content-area p {
            margin-bottom: 1rem;
        }
        
        .content-area img {
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
            border-radius: 8px;
        }
        
        .content-area ul, .content-area ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        .content-area li {
            margin-bottom: 0.5rem;
        }
        
        .stats-card {
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="primary-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="history.back()" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <div>
                        <h1 class="text-2xl font-bold">Detail Berita</h1>
                        <p class="text-blue-100">Kelola dan lihat detail berita</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="dashboard.php?page=edit-news&id=<?php echo $news['id']; ?>" 
                       class="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Berita
                    </a>
                    <a href="dashboard.php?page=news" 
                       class="bg-blue-700 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                        <i class="fas fa-list mr-2"></i>Semua Berita
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
                <a href="dashboard.php" class="hover:text-blue-600 transition-colors">
                    <i class="fas fa-home mr-1"></i>Dashboard
                </a>
                <i class="fas fa-chevron-right text-xs"></i>
                <a href="dashboard.php?page=news" class="hover:text-blue-600 transition-colors">
                    Kelola Berita
                </a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-gray-900 font-medium">Detail Berita</span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- News Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-eye text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Views</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($news['views'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-heart text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Likes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($news['likes'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-share text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Shares</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($news['share'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-comments text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Comments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($news['comments_count'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- News Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <article class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Featured Image -->
                    <?php if ($news['image']): ?>
                    <div class="aspect-video bg-gray-200">
                        <img src="<?php echo htmlspecialchars($news['image']); ?>" 
                             alt="<?php echo htmlspecialchars($news['title']); ?>"
                             class="w-full h-full object-cover">
                    </div>
                    <?php endif; ?>
                    
                    <!-- Article Header -->
                    <div class="p-8">
                        <div class="flex items-center space-x-4 mb-6">
                            <span class="px-3 py-1 text-sm font-medium text-white rounded-full"
                                  style="background-color: <?php echo $news['category_color'] ?? '#3B82F6'; ?>">
                                <?php echo htmlspecialchars($news['category_name'] ?? 'Uncategorized'); ?>
                            </span>
                            
                            <span class="px-3 py-1 text-sm font-medium rounded-full
                                <?php echo $news['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                <?php echo ucfirst($news['status']); ?>
                            </span>
                        </div>
                        
                        <h1 class="text-3xl font-bold text-gray-900 mb-4 leading-tight">
                            <?php echo htmlspecialchars($news['title']); ?>
                        </h1>
                        
                        <div class="flex items-center text-gray-600 text-sm mb-6">
                            <i class="fas fa-user mr-2"></i>
                            <span class="mr-4"><?php echo htmlspecialchars($news['author_name'] ?? 'Admin'); ?></span>
                            
                            <i class="fas fa-calendar mr-2"></i>
                            <span class="mr-4"><?php echo formatDate($news['created_at']); ?></span>
                            
                            <?php if ($news['updated_at'] && $news['updated_at'] !== $news['created_at']): ?>
                            <i class="fas fa-edit mr-2"></i>
                            <span>Updated: <?php echo formatDate($news['updated_at']); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Description -->
                        <?php if ($news['description']): ?>
                        <div class="bg-gray-50 rounded-lg p-4 mb-6">
                            <p class="text-gray-700 text-lg leading-relaxed">
                                <?php echo htmlspecialchars($news['description']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Content -->
                        <div class="content-area prose prose-lg max-w-none">
                            <?php echo $news['content']; ?>
                        </div>
                    </div>
                </article>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- News Info -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Informasi Berita</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-600">ID Berita</label>
                            <p class="text-gray-900">#<?php echo $news['id']; ?></p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">Slug</label>
                            <p class="text-gray-900 break-all"><?php echo htmlspecialchars($news['slug']); ?></p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">Kategori</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($news['category_name'] ?? 'Uncategorized'); ?></p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">Status</label>
                            <p class="text-gray-900"><?php echo ucfirst($news['status']); ?></p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">Dibuat</label>
                            <p class="text-gray-900"><?php echo formatDate($news['created_at']); ?></p>
                        </div>
                        
                        <?php if ($news['published_at']): ?>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Dipublikasi</label>
                            <p class="text-gray-900"><?php echo formatDate($news['published_at']); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Aksi Cepat</h3>
                    
                    <div class="space-y-3">
                        <a href="dashboard.php?page=edit-news&id=<?php echo $news['id']; ?>" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i>Edit Berita
                        </a>
                        
                        <button onclick="duplicateNews(<?php echo $news['id']; ?>)" 
                                class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-copy mr-2"></i>Duplikat
                        </button>
                        
                        <button onclick="toggleStatus(<?php echo $news['id']; ?>, '<?php echo $news['status']; ?>')" 
                                class="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-toggle-on mr-2"></i>
                            <?php echo $news['status'] === 'published' ? 'Unpublish' : 'Publish'; ?>
                        </button>
                        
                        <button onclick="deleteNews(<?php echo $news['id']; ?>)" 
                                class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-trash mr-2"></i>Hapus
                        </button>
                        
                        <a href="<?php echo $news['slug'] ? '/news/' . $news['slug'] : '/news/' . $news['id']; ?>" 
                           target="_blank"
                           class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-external-link-alt mr-2"></i>Lihat di Frontend
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script>
        function toggleStatus(newsId, currentStatus) {
            const newStatus = currentStatus === 'published' ? 'draft' : 'published';
            const action = newStatus === 'published' ? 'publish' : 'unpublish';
            
            if (confirm(`Apakah Anda yakin ingin ${action} berita ini?`)) {
                // Implementation for status toggle
                fetch('simple_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'update_news',
                        id: newsId,
                        status: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
        
        function duplicateNews(newsId) {
            if (confirm('Apakah Anda yakin ingin menduplikat berita ini?')) {
                // Implementation for news duplication
                alert('Fitur duplikat akan segera tersedia');
            }
        }
        
        function deleteNews(newsId) {
            if (confirm('Apakah Anda yakin ingin menghapus berita ini? Tindakan ini tidak dapat dibatalkan.')) {
                fetch('simple_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'delete_news',
                        id: newsId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Berita berhasil dihapus');
                        window.location.href = 'dashboard.php?page=news';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
