{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport LandingPage from './pages/user/LandingPage';\nimport VideoPage from './pages/user/VideoPage';\nimport VideoPlay from './pages/user/VideoPlay';\nimport DataNews from './pages/user/DataNews';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\n\n// User Auth Components\nimport UserLogin from './pages/user/auth/Login';\nimport UserRegister from './pages/user/auth/Register';\nimport ForgotPassword from './pages/user/auth/ForgotPassword';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/home\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/video\",\n          element: /*#__PURE__*/_jsxDEV(VideoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/video-play\",\n          element: /*#__PURE__*/_jsxDEV(VideoPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/data-news\",\n          element: /*#__PURE__*/_jsxDEV(DataNews, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/news\",\n          element: /*#__PURE__*/_jsxDEV(DataNews, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/saved\",\n          element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/bookmark\",\n          element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/login\",\n          element: /*#__PURE__*/_jsxDEV(UserLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/register\",\n          element: /*#__PURE__*/_jsxDEV(UserRegister, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/forgot-password\",\n          element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/login\",\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "LandingPage", "VideoPage", "VideoPlay", "DataNews", "Saved", "UserLogin", "UserRegister", "ForgotPassword", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "AdminDashboardRedirect", "AdminLogin", "Register", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>rowser<PERSON>outer as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport LandingPage from './pages/user/LandingPage';\nimport VideoPage from './pages/user/VideoPage';\nimport VideoPlay from './pages/user/VideoPlay';\nimport DataNews from './pages/user/DataNews';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\n\n// User Auth Components\nimport UserLogin from './pages/user/auth/Login';\nimport UserRegister from './pages/user/auth/Register';\nimport ForgotPassword from './pages/user/auth/ForgotPassword';\n\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n        {/* User Routes */}\n        <Route path=\"/\" element={<LandingPage />} />\n        <Route path=\"/home\" element={<LandingPage />} />\n        <Route path=\"/video\" element={<VideoPage />} />\n        <Route path=\"/video-play\" element={<VideoPlay />} />\n        <Route path=\"/data-news\" element={<DataNews />} />\n        <Route path=\"/news\" element={<DataNews />} />\n        <Route path=\"/saved\" element={<Saved />} />\n        <Route path=\"/bookmark\" element={<Saved />} />\n\n        {/* User Auth Routes */}\n        <Route path=\"/auth/login\" element={<UserLogin />} />\n        <Route path=\"/auth/register\" element={<UserRegister />} />\n        <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\n\n        {/* Admin Routes */}\n        <Route path=\"/admin\" element={<AdminDashboardRedirect />} />\n        <Route path=\"/admin/login\" element={<AdminLogin />} />\n        <Route path=\"/admin/register\" element={<Register />} />\n\n        {/* Dashboard Routes - Clean URLs without .php */}\n        <Route path=\"/dashboard\" element={<AdminDashboardRedirect />} />\n        <Route path=\"/admin/dashboard\" element={<AdminDashboardRedirect />} />\n\n        {/* Fallback Route */}\n        <Route path=\"*\" element={<LandingPage />} />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAO,WAAW;;AAElB;AACA,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACb,MAAM;MAAAe,QAAA,eACLF,OAAA,CAACZ,MAAM;QAAAc,QAAA,gBAEPF,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACR,SAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEJ,OAAA,CAACP,SAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACN,QAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEJ,OAAA,CAACN,QAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACL,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACL,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG9CR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEJ,OAAA,CAACJ,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEJ,OAAA,CAACH,YAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEJ,OAAA,CAACF,cAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGnER,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEJ,OAAA,CAACU,UAAU;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAEJ,OAAA,CAACW,QAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGvDR,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChER,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtER,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACI,EAAA,GAnCQX,GAAG;AAqCZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}