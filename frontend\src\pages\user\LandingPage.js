import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Drawer from '@mui/material/Drawer';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import MenuIcon from '@mui/icons-material/Menu';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme, createTheme, ThemeProvider } from '@mui/material/styles';
import ShareIcon from '@mui/icons-material/Share';
import BookmarkAddIcon from '@mui/icons-material/BookmarkAdd';
import BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import CloseIcon from '@mui/icons-material/Close';
import TextField from '@mui/material/TextField';
import SearchIcon from '@mui/icons-material/Search';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

// Helper function to get correct image URL - Use standardized uploads folder
const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return 'https://source.unsplash.com/300x200/?news'; // Fallback to Unsplash
  }

  console.log('LandingPage - Processing image path:', imagePath);

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    console.log('LandingPage - Using full URL:', imagePath);
    return imagePath;
  }

  // Extract filename from any path format (untuk gambar berita, bukan logo)
  let filename = '';

  if (imagePath.startsWith('/react-news/uploads/')) {
    filename = imagePath.replace('/react-news/uploads/', '');
  } else if (imagePath.startsWith('/react-news/frontend/uploads/')) {
    filename = imagePath.replace('/react-news/frontend/uploads/', '');
  } else if (imagePath.startsWith('/uploads/')) {
    filename = imagePath.replace('/uploads/', '');
  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {
    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');
  } else if (imagePath.startsWith('uploads/')) {
    filename = imagePath.replace('uploads/', '');
  } else if (imagePath.startsWith('assets/news/')) {
    filename = imagePath.replace('assets/news/', '');
  } else if (!imagePath.includes('/')) {
    // Just filename
    filename = imagePath;
  } else {
    // Extract filename from any other path
    filename = imagePath.split('/').pop();
  }

  // Use consistent path with admin dashboard - frontend/uploads folder
  const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;
  console.log('LandingPage - Using frontend/uploads path:', finalUrl);
  return finalUrl;
};

// Popular news will be fetched from database

const categories = [
  'Semua', 'Umum', 'Teknologi', 'Bisnis', 'Olahraga', 'Hiburan', 'Politik', 'Kesehatan'
];

function Toast({ message, isVisible, onClose }) {
  if (!isVisible) return null;
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: { xs: 80, md: 100 },
        right: { xs: 16, md: 24 },
        zIndex: 9999,
        bgcolor: '#10b981',
        color: 'white',
        px: 3,
        py: 2,
        borderRadius: 2,
        boxShadow: 4,
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        transform: 'translateX(0)',
        transition: 'transform 0.3s ease-in-out',
        maxWidth: { xs: 'calc(100vw - 32px)', md: 400 },
        minWidth: { xs: 280, md: 320 }
      }}
    >
      <CheckCircleIcon sx={{ fontSize: 20 }} />
      <Typography variant="body2" sx={{ fontWeight: 600, fontSize: { xs: 13, md: 14 } }}>
        {message}
      </Typography>
    </Box>
  );
}

function PopularTags({ selectedCategory, setSelectedCategory, categoriesData }) {
  const popularTags = categoriesData.length > 0 ? categoriesData.map(cat => ({
    name: cat.name,
    count: cat.post_count || 0,
    color: cat.color || '#3B82F6'
  })) : [
    { name: 'Umum', count: 0, color: '#6B7280' },
    { name: 'Teknologi', count: 0, color: '#3B82F6' },
    { name: 'Bisnis', count: 0, color: '#10B981' },
    { name: 'Olahraga', count: 0, color: '#F59E0B' },
    { name: 'Hiburan', count: 0, color: '#EF4444' },
    { name: 'Politik', count: 0, color: '#8B5CF6' },
    { name: 'Kesehatan', count: 0, color: '#06B6D4' }
  ];

  return (
    <Box sx={{ 
      bgcolor: '#fff', 
      borderRadius: 3, 
      p: 3, 
      mb: 3, 
      boxShadow: 2,
      border: '1px solid',
      borderColor: 'grey.200'
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ 
          width: 4, 
          height: 20, 
          bgcolor: 'primary.main', 
          borderRadius: 2, 
          mr: 2 
        }} />
        <Typography variant="h6" sx={{ 
          fontWeight: 700, 
          color: 'grey.800',
          fontSize: { xs: 16, md: 18 }
        }}>
          Tag Terpopuler
        </Typography>
      </Box>
      
      <Box sx={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        gap: 1.5,
        justifyContent: { xs: 'center', md: 'flex-start' }
      }}>
        {popularTags.map((tag, index) => (
          <Chip
            key={tag.name}
            label={`${tag.name} (${tag.count})`}
            onClick={() => setSelectedCategory(tag.name)}
            sx={{
              bgcolor: selectedCategory === tag.name ? tag.color : 'grey.100',
              color: selectedCategory === tag.name ? 'white' : 'grey.700',
              fontWeight: 600,
              fontSize: { xs: 12, md: 13 },
              px: 2,
              py: 1,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: selectedCategory === tag.name ? tag.color : 'grey.200',
                transform: 'translateY(-2px)',
                boxShadow: 2
              },
              border: selectedCategory === tag.name ? 'none' : '1px solid',
              borderColor: 'grey.300'
            }}
          />
        ))}
      </Box>
    </Box>
  );
}

function LatestPosts({ newsData }) {
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });
  };

  // Get latest 4 posts
  const latestPosts = newsData.slice(0, 4);

  return (
    <Box sx={{ 
      bgcolor: '#fff', 
      borderRadius: 3, 
      p: 3, 
      mb: 3, 
      boxShadow: 2,
      border: '1px solid',
      borderColor: 'grey.200'
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Box sx={{ 
          width: 4, 
          height: 20, 
          bgcolor: 'primary.main', 
          borderRadius: 2, 
          mr: 2 
        }} />
        <Typography variant="h6" sx={{ 
          fontWeight: 700, 
          color: 'grey.800',
          fontSize: { xs: 16, md: 18 }
        }}>
          Latest Posts
        </Typography>
      </Box>
      
      <Stack spacing={2}>
        {latestPosts.map((post, index) => (
          <Box key={post.id || index} sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 2,
            p: 2,
            borderRadius: 2,
            transition: 'all 0.3s ease',
            cursor: 'pointer',
            '&:hover': {
              bgcolor: 'grey.50',
              transform: 'translateX(4px)'
            }
          }}>
            {/* Text Content - Left Side */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="subtitle2" sx={{ 
                fontWeight: 600, 
                color: 'grey.900', 
                lineHeight: 1.3,
                mb: 0.5,
                fontSize: { xs: 13, md: 14 },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical'
              }}>
                {post.title}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Typography variant="caption" sx={{
                  color: 'primary.main',
                  fontWeight: 600,
                  fontSize: { xs: 10, md: 11 }
                }}>
                  {post.category_name || post.category || 'Umum'}
                </Typography>
                <Typography variant="caption" sx={{ color: 'grey.500', fontSize: { xs: 10, md: 11 } }}>
                  •
                </Typography>
                <Typography variant="caption" sx={{
                  color: 'grey.600',
                  fontSize: { xs: 10, md: 11 }
                }}>
                  {formatTime(post.created_at || post.date)}
                </Typography>
              </Box>
            </Box>
            
            {/* Image - Right Side */}
            <Box sx={{ 
              width: { xs: 60, md: 80 }, 
              height: { xs: 60, md: 80 }, 
              borderRadius: 2, 
              overflow: 'hidden',
              flexShrink: 0
            }}>
              <Box
                component="img"
                src={getImageUrl(post.image)}
                alt={post.title}
                sx={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
                onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }}
              />
            </Box>
          </Box>
        ))}
      </Stack>
    </Box>
  );
}

function BreakingNewsSlider({ newsData, kostum }) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [autoPlay, setAutoPlay] = useState(true);

  // Add CSS animation for pulse effect
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  // Get latest 5 news for slider
  const sliderNews = newsData.slice(0, 5);

  React.useEffect(() => {
    if (!autoPlay || sliderNews.length <= 1) return;
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderNews.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [autoPlay, sliderNews.length]);

  const handleNext = () => {
    setCurrentSlide((prev) => (prev + 1) % sliderNews.length);
    setAutoPlay(false);
  };

  const handlePrev = () => {
    setCurrentSlide((prev) => (prev - 1 + sliderNews.length) % sliderNews.length);
    setAutoPlay(false);
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });
  };

  if (sliderNews.length === 0) {
    return (
      <Box sx={{ 
        position: 'relative', 
        width: '100%', 
        height: { xs: 200, md: 400 }, 
        borderRadius: 3, 
        overflow: 'hidden',
        mb: 2,
        boxShadow: 3,
        bgcolor: 'grey.100',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Box sx={{ textAlign: 'center', color: 'grey.600' }}>
          <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
            Belum ada berita
          </Typography>
          <Typography variant="body2" sx={{ color: 'grey.500' }}>
            Berita akan muncul di sini
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      position: 'relative', 
      width: '100%', 
      height: { xs: 200, md: 400 }, // 1920x1080 aspect ratio for desktop
      borderRadius: 3, 
      overflow: 'hidden',
      mb: 2,
      boxShadow: 3
    }}>
      {/* Breaking News Badge */}
      <Box sx={{
        position: 'absolute',
        top: 16,
        left: 16,
        zIndex: 3,
        bgcolor: 'error.main',
        color: 'white',
        px: 2,
        py: 0.5,
        borderRadius: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <Box sx={{ 
          width: 8, 
          height: 8, 
          borderRadius: '50%', 
          bgcolor: 'white',
          animation: 'pulse 1.5s infinite'
        }} />
        <Typography variant="caption" sx={{ fontWeight: 700, fontSize: 12 }}>
          BREAKING NEWS
        </Typography>
      </Box>

      {/* Slides */}
      <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
        {sliderNews.map((news, index) => (
          <Box
            key={news.id || index}
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              opacity: index === currentSlide ? 1 : 0,
              transition: 'opacity 0.5s ease-in-out',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <Box sx={{ 
              position: 'relative', 
              width: '100%', 
              height: '100%',
              background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${getImageUrl(news.image)})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              display: 'flex',
              alignItems: 'flex-end',
              p: 3
            }}>
              <Box sx={{ color: 'white', width: '100%' }}>
                <Typography variant="h6" sx={{ 
                  fontWeight: 700, 
                  mb: 1,
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                  fontSize: { xs: 16, sm: 18, md: 24 }
                }}>
                  {news.title}
                </Typography>
                <Typography variant="caption" sx={{ 
                  color: 'grey.300',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                  fontSize: { xs: 12, md: 14 }
                }}>
                  {formatTime(news.date)} • {news.category}
                </Typography>
              </Box>
            </Box>
          </Box>
        ))}
      </Box>

      {/* Navigation Arrows */}
      {sliderNews.length > 1 && (
        <>
          <IconButton
            onClick={handlePrev}
            sx={{
              position: 'absolute',
              left: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },
              zIndex: 2
            }}
          >
            <NavigateBeforeIcon />
          </IconButton>
          <IconButton
            onClick={handleNext}
            sx={{
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },
              zIndex: 2
            }}
          >
            <NavigateNextIcon />
          </IconButton>
        </>
      )}

      {/* Dots Indicator */}
      {sliderNews.length > 1 && (
        <Box sx={{
          position: 'absolute',
          bottom: 16,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: 1,
          zIndex: 2
        }}>
          {sliderNews.map((_, index) => (
            <Box
              key={index}
              onClick={() => {
                setCurrentSlide(index);
                setAutoPlay(false);
              }}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </Box>
      )}
    </Box>
  );
}

function NewsCard({ news, expanded, onExpand, formatDate, truncateText, variant, onShare, onBookmark, isBookmarked, onNewsClick, isAuthenticated, onLike, isLiked }) {
  const aspectRatio = variant === 'desktop' ? '56.25%' : '100%';

  const handleNewsClick = (e) => {
    // Prevent navigation if clicking on buttons/icons
    if (e.target.closest('button') || e.target.closest('[role="button"]')) {
      return;
    }
    onNewsClick && onNewsClick(news.id);
  };

  return (
    <Card
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        position: 'relative',
        minWidth: { xs: '90vw', sm: 320, md: 340 },
        maxWidth: { xs: '100vw', sm: 400, md: 420 },
        mx: 'auto',
        borderRadius: 4,
        boxShadow: 3,
        p: 1,
        bgcolor: '#fff',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 6,
        },
      }}
    >
      {/* Clickable Image Area */}
      <Box
        onClick={handleNewsClick}
        sx={{
          position: 'relative',
          width: '100%',
          paddingTop: aspectRatio,
          borderRadius: 3,
          overflow: 'hidden',
          cursor: 'pointer'
        }}
      >
        <CardMedia
          component="img"
          sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }}
          image={getImageUrl(news.image)}
          alt={news.title}
          onError={(e) => { e.target.src = variant === 'desktop' ? 'https://source.unsplash.com/1920x1080/?news' : 'https://source.unsplash.com/900x900/?news'; }}
        />
      </Box>
      {/* Like, Bookmark and Share icons kanan bawah */}
      <Box sx={{ position: 'absolute', right: 20, bottom: 20, zIndex: 2, mt: 2, display: 'flex', gap: 1 }}>
        <Tooltip title={isLiked ? "Hapus like" : "Suka berita ini"}>
          <IconButton
            color={isLiked ? "error" : "default"}
            size="medium"
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click
              onLike(news);
            }}
            sx={{
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                color: '#e91e63'
              },
              '&:active': {
                transform: 'scale(0.95)',
              },
              ...(isLiked && {
                animation: 'heartBeat 0.6s ease-in-out',
                '@keyframes heartBeat': {
                  '0%': { transform: 'scale(1)' },
                  '14%': { transform: 'scale(1.3)' },
                  '28%': { transform: 'scale(1)' },
                  '42%': { transform: 'scale(1.3)' },
                  '70%': { transform: 'scale(1)' }
                }
              })
            }}
          >
            {isLiked ? (
              <FavoriteIcon fontSize="medium" sx={{ color: '#e91e63' }} />
            ) : (
              <FavoriteBorderIcon fontSize="medium" />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title={
          !isAuthenticated ? "Login untuk menyimpan berita" :
          isBookmarked ? "Hapus dari bookmark" : "Tambah ke bookmark"
        }>
          <IconButton
            color={isBookmarked ? "success" : "primary"}
            size="medium"
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click
              onBookmark(news);
            }}
            sx={{
              transition: 'all 0.3s ease',
              '&:hover': { transform: 'scale(1.1)' }
            }}
          >
            {isBookmarked ? (
              <BookmarkAddedIcon fontSize="medium" />
            ) : (
              <BookmarkAddIcon fontSize="medium" />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title="Bagikan">
          <IconButton
            color="primary"
            size="medium"
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click
              onShare(news);
            }}
          >
            <ShareIcon fontSize="medium" />
          </IconButton>
        </Tooltip>
      </Box>
      {/* Clickable Content Area */}
      <CardContent
        onClick={handleNewsClick}
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          pb: 2,
          pt: 2,
          cursor: 'pointer'
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5, fontSize: { xs: 18, md: 22 } }}>{news.title}</Typography>
        <Typography variant="caption" sx={{ color: 'grey.600', mb: 1, fontSize: { xs: 13, md: 15 } }}>
          Kategori: <Typography component="span" sx={{ fontWeight: 600, color: 'secondary.main' }}>{news.category_name || news.category || 'Umum'}</Typography> &bull; {formatDate(news.created_at || news.date)}
        </Typography>
        <Box sx={{ mb: 2, mr: 12 }}> {/* Add right margin to avoid icon overlap */}
          <Typography
            variant="body2"
            sx={{
              color: 'grey.800',
              fontSize: { xs: 15, md: 16 },
              lineHeight: 1.5,
              display: '-webkit-box',
              WebkitLineClamp: expanded ? 'none' : 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              mb: 1
            }}
          >
            {news.description}
          </Typography>
          {news.description.length > 100 && (
            <Button
              size="small"
              color="primary"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click
                onExpand();
              }}
              sx={{
                alignSelf: 'flex-start',
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.8rem',
                p: 0.5,
                minWidth: 'auto'
              }}
            >
              {expanded ? 'Baca Lebih Sedikit' : 'Baca Selengkapnya'}
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
}

function MobileNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, bottomNav, setBottomNav, handleSidebar, sidebarOpen, handleSidebarClose, handleShare, onBookmark, bookmarkedNews, onSearchClick, handleBottomNavChange, onNewsClick, openSearch, navigate, user, isAuthenticated, handleLogout, onLike, likedNews }) {
  const truncateText = (text, maxLength = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Function to check if news is new (published within last 24 hours)
  const isNewNews = (dateString) => {
    const newsDate = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - newsDate) / (1000 * 60 * 60);
    return diffInHours <= 24;
  };
  const filteredNews = newsData.filter(news =>
    selectedCategory === 'Semua' ||
    news.category === selectedCategory ||
    news.category_name === selectedCategory
  ).sort((a, b) => {
    // Sort by date (newest first)
    const dateA = new Date(a.published_at || a.date || a.created_at);
    const dateB = new Date(b.published_at || b.date || b.created_at);
    return dateB - dateA;
  }).slice(0, 6);
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', width: '100vw', overflow: 'hidden' }}>
      {/* Navbar (fixed) */}
      <AppBar position="fixed" color="inherit" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>
        <Toolbar sx={{ minHeight: 70, px: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Avatar
              src={kostum.logo || '/logo192.png'}
              alt="Logo"
              sx={{
                width: 48,
                height: 48,
                mr: 2,
                cursor: 'pointer'
              }}
              onError={(e) => {
                console.log('Logo error, using fallback');
                e.target.src = '/logo192.png';
              }}
              onClick={() => navigate('/')}
            />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: 'primary.main',
                fontSize: 22,
                cursor: 'pointer'
              }}
              onClick={() => navigate('/')}
            >
              {kostum.title || 'React News Portal'}
            </Typography>
          </Box>
          <IconButton edge="end" color="primary" onClick={() => window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-play.php'} sx={{ mr: 1 }} title="Video">
            <i className="fas fa-play-circle" style={{ fontSize: 24 }}></i>
          </IconButton>
          <IconButton edge="end" color="primary" onClick={onSearchClick} sx={{ mr: 1 }}>
            <SearchIcon fontSize="large" />
          </IconButton>
          <IconButton edge="end" color="primary" onClick={handleSidebar}>
            <MenuIcon fontSize="large" />
          </IconButton>
        </Toolbar>
      </AppBar>
      {/* Sidebar Drawer (Mobile) */}
      <Drawer
        anchor="right"
        open={sidebarOpen}
        onClose={handleSidebarClose}
        ModalProps={{ keepMounted: true }}
        sx={{ zIndex: 2000 }}
      >
        <Box sx={{ width: 260, p: 3, position: 'relative' }}>
          <IconButton
            onClick={handleSidebarClose}
            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}
            aria-label="Tutup"
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={kostum.logo} alt="Logo" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>
          </Box>
          <Divider sx={{ mb: 2 }} />

          {/* Quick Actions */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{
              fontWeight: 600,
              color: 'text.secondary',
              mb: 1.5,
              fontSize: 12,
              textTransform: 'uppercase',
              letterSpacing: 0.5
            }}>
              Quick Actions
            </Typography>
            <Stack spacing={1.5}>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  if (isAuthenticated) {
                    window.location.href = '/saved';
                  } else {
                    navigate('/auth/login');
                  }
                }}
              >
                <i className="fas fa-bookmark" style={{ marginRight: 8, fontSize: 14 }}></i>
                Berita Tersimpan
              </Button>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  handleSidebarClose();
                  openSearch();
                }}
              >
                <i className="fas fa-search" style={{ marginRight: 8, fontSize: 14 }}></i>
                Cari Berita
              </Button>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  handleSidebarClose();
                  window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-play.php';
                }}
              >
                <i className="fas fa-play-circle" style={{ marginRight: 8, fontSize: 14 }}></i>
                Video
              </Button>
            </Stack>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Authentication */}
          <Box>
            <Typography variant="subtitle2" sx={{
              fontWeight: 600,
              color: 'text.secondary',
              mb: 1.5,
              fontSize: 12,
              textTransform: 'uppercase',
              letterSpacing: 0.5
            }}>
              Account
            </Typography>
            <Stack spacing={2}>
              {isAuthenticated ? (
                // Logged in user
                <>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}>
                    <AccountCircleIcon sx={{ color: 'primary.main', fontSize: 32 }} />
                    <Box>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {user?.name || 'User'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user?.email || '<EMAIL>'}
                      </Typography>
                    </Box>
                  </Box>

                  <Button
                    variant="outlined"
                    color="error"
                    fullWidth
                    startIcon={<LogoutIcon />}
                    sx={{
                      textTransform: 'none',
                      borderColor: 'error.main',
                      color: 'error.main',
                      '&:hover': {
                        borderColor: 'error.main',
                        backgroundColor: 'error.main',
                        color: 'white'
                      }
                    }}
                    onClick={handleLogout}
                  >
                    Logout
                  </Button>
                </>
              ) : (
                // Not logged in
                <>
                  <Button
                    variant="outlined"
                    color="primary"
                    fullWidth
                    startIcon={<LoginIcon />}
                    sx={{
                      textTransform: 'none',
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: 'primary.main',
                        color: 'white'
                      }
                    }}
                    onClick={() => navigate('/auth/login')}
                  >
                    Login
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    startIcon={<PersonAddIcon />}
                    sx={{
                      textTransform: 'none',
                      backgroundColor: 'primary.main',
                      '&:hover': {
                        backgroundColor: 'primary.dark'
                      }
                    }}
                    onClick={() => navigate('/auth/register')}
                  >
                    Register
                  </Button>
                </>
              )}
            </Stack>
          </Box>
        </Box>
      </Drawer>
      {/* Category Slider (fixed) */}
      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 60, left: 0, right: 0, zIndex: 1301 }}>
        <Stack direction="row" spacing={2} sx={{ overflowX: 'auto', px: 2, '&::-webkit-scrollbar': { display: 'none' }, scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
          {categories.map((cat) => (
            <Chip
              key={cat}
              label={cat}
              color={selectedCategory === cat ? 'primary' : 'default'}
              onClick={() => setSelectedCategory(cat)}
              sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}
            />
          ))}
        </Stack>
      </Box>
      {/* Main Content */}
      <Box sx={{ position: 'absolute', top: 112, left: 0, right: 0, bottom: 70, px: 1, maxWidth: '100%', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'column', minHeight: 0, zIndex: 1, pt: 2, pb: 2 }}>
        <Box
          component="main"
          sx={{
            flex: 1,
            height: 'calc(100vh - 112px - 70px)', // 112px = navbar+kategori, 70px = bottom nav
            overflowY: 'auto',
            minHeight: 0,
            pt: 0,
            pb: 0,
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          {/* Breaking News Slider */}
          <BreakingNewsSlider newsData={newsData} kostum={kostum} />
          
          {/* Popular Tags */}
          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />

          {/* Latest Posts */}
          <LatestPosts newsData={newsData} />
          
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              overflowY: 'auto',
              pb: { xs: 10, md: 0 }, // padding bawah ekstra di mobile
              '&::-webkit-scrollbar': { display: 'none' },
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              px: 1,
              pr: 2, // Add right padding to hide scrollbar
            }}
          >
            {loading ? (
              Array.from({ length: 6 }).map((_, idx) => (
                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }}>
                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />
                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>
                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />
                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />
                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />
                  </CardContent>
                </Card>
              ))
            ) : filteredNews.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>
                <Typography variant="h6">Belum ada berita</Typography>
                <Typography variant="body2">Berita akan muncul di sini</Typography>
              </Box>
            ) : (
              filteredNews.map((news, idx) => (
                <Box key={news.id || idx} sx={{ width: '100%' }}>
                  <NewsCard
                    news={news}
                    expanded={!!expandedCards[news.id]}
                    onExpand={() => handleCardExpand(news.id)}
                    formatDate={formatDate}
                    truncateText={truncateText}
                    variant="mobile"
                    onShare={handleShare}
                    onBookmark={onBookmark}
                    isBookmarked={bookmarkedNews.has(news.id)}
                    onNewsClick={onNewsClick}
                    isAuthenticated={isAuthenticated}
                    onLike={onLike}
                    isLiked={likedNews.has(news.id)}
                  />
                </Box>
              ))
            )}
          </Box>
        </Box>
      </Box>
      {/* Custom Bottom Navigation */}
      <Box sx={{
        position: 'fixed',
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1300,
        display: 'block',
        backgroundColor: 'white',
        borderTop: '1px solid #e0e0e0',
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          height: 64,
          px: 1
        }}>
          <Box
            onClick={() => handleBottomNavChange(0)}
            className={`bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`}
          >
            <i className="fas fa-home bottom-nav-icon" style={{ color: bottomNav === 0 ? kostum.primary_color : '#6b7280' }}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomNav === 0 ? 'primary.main' : 'text.secondary' }}>
              Home
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(1)}
            className={`bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`}
          >
            <i className="fas fa-play-circle bottom-nav-icon" style={{ color: bottomNav === 1 ? kostum.primary_color : '#6b7280' }}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomNav === 1 ? 'primary.main' : 'text.secondary' }}>
              Video
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(2)}
            className={`bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`}
          >
            <i className="fas fa-search bottom-nav-icon" style={{ color: bottomNav === 2 ? kostum.primary_color : '#6b7280' }}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomNav === 2 ? 'primary.main' : 'text.secondary' }}>
              Cari
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(3)}
            className={`bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`}
          >
            <i className="fas fa-bookmark bottom-nav-icon" style={{ color: bottomNav === 3 ? kostum.primary_color : '#6b7280' }}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomNav === 3 ? 'primary.main' : 'text.secondary' }}>
              Simpan
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

function DesktopNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, handleShare, onBookmark, bookmarkedNews, onSearchClick, sidebarOpen, handleSidebar, handleSidebarClose, onNewsClick, openSearch, navigate, user, isAuthenticated, handleLogout, onLike, likedNews }) {
  const truncateText = (text, maxLength = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Function to check if news is new (published within last 24 hours)
  const isNewNews = (dateString) => {
    const newsDate = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - newsDate) / (1000 * 60 * 60);
    return diffInHours <= 24;
  };
  const filteredNews = newsData.filter(news =>
    selectedCategory === 'Semua' ||
    news.category === selectedCategory ||
    news.category_name === selectedCategory
  ).sort((a, b) => {
    // Sort by date (newest first)
    const dateA = new Date(a.published_at || a.date || a.created_at);
    const dateB = new Date(b.published_at || b.date || b.created_at);
    return dateB - dateA;
  }).slice(0, 6);
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', width: '100vw', overflow: 'hidden' }}>
      <AppBar position="fixed" color="inherit" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>
        <Toolbar sx={{ minHeight: 80, px: 6 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Avatar src={kostum.logo} alt="Logo" sx={{ width: 48, height: 48, mr: 2 }} onError={(e) => { e.target.src = '/logo192.png'; }} />
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>{kostum.title}</Typography>
          </Box>
          <IconButton edge="end" color="primary" onClick={() => navigate('/video')} sx={{ mr: 1 }} title="Video">
            <i className="fas fa-play-circle" style={{ fontSize: 24 }}></i>
          </IconButton>
          <IconButton edge="end" color="primary" onClick={onSearchClick} sx={{ mr: 1 }}>
            <SearchIcon fontSize="large" />
          </IconButton>
          <IconButton edge="end" color="primary" onClick={handleSidebar} sx={{ ml: 1 }}>
            <MenuIcon fontSize="large" />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Drawer
        anchor="right"
        open={sidebarOpen}
        onClose={handleSidebarClose}
        ModalProps={{ keepMounted: true }}
        sx={{ zIndex: 2000 }}
      >
        <Box sx={{ width: 260, p: 3, position: 'relative' }}>
          <IconButton
            onClick={handleSidebarClose}
            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}
            aria-label="Tutup"
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={kostum.logo} alt="Logo" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>
          </Box>
          <Divider sx={{ mb: 2 }} />

          {/* Quick Actions */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{
              fontWeight: 600,
              color: 'text.secondary',
              mb: 1.5,
              fontSize: 12,
              textTransform: 'uppercase',
              letterSpacing: 0.5
            }}>
              Quick Actions
            </Typography>
            <Stack spacing={1.5}>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  if (isAuthenticated) {
                    window.location.href = '/saved';
                  } else {
                    navigate('/auth/login');
                  }
                }}
              >
                <i className="fas fa-bookmark" style={{ marginRight: 8, fontSize: 14 }}></i>
                Berita Tersimpan
              </Button>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  handleSidebarClose();
                  openSearch();
                }}
              >
                <i className="fas fa-search" style={{ marginRight: 8, fontSize: 14 }}></i>
                Cari Berita
              </Button>
              <Button
                variant="text"
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                onClick={() => {
                  handleSidebarClose();
                  navigate('/video');
                }}
              >
                <i className="fas fa-play-circle" style={{ marginRight: 8, fontSize: 14 }}></i>
                Video
              </Button>
            </Stack>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Authentication */}
          <Box>
            <Typography variant="subtitle2" sx={{
              fontWeight: 600,
              color: 'text.secondary',
              mb: 1.5,
              fontSize: 12,
              textTransform: 'uppercase',
              letterSpacing: 0.5
            }}>
              Account
            </Typography>
            <Stack spacing={2}>
              {isAuthenticated ? (
                // Logged in user
                <>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}>
                    <AccountCircleIcon sx={{ color: 'primary.main', fontSize: 32 }} />
                    <Box>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {user?.name || 'User'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user?.email || '<EMAIL>'}
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="outlined"
                    color="error"
                    fullWidth
                    startIcon={<LogoutIcon />}
                    sx={{
                      textTransform: 'none',
                      borderColor: 'error.main',
                      color: 'error.main',
                      '&:hover': {
                        borderColor: 'error.main',
                        backgroundColor: 'error.main',
                        color: 'white'
                      }
                    }}
                    onClick={handleLogout}
                  >
                    Logout
                  </Button>
                </>
              ) : (
                // Not logged in
                <>
                  <Button
                    variant="outlined"
                    color="primary"
                    fullWidth
                    startIcon={<LoginIcon />}
                    sx={{
                      textTransform: 'none',
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: 'primary.main',
                        color: 'white'
                      }
                    }}
                    onClick={() => navigate('/auth/login')}
                  >
                    Login
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    startIcon={<PersonAddIcon />}
                    sx={{
                      textTransform: 'none',
                      backgroundColor: 'primary.main',
                      '&:hover': {
                        backgroundColor: 'primary.dark'
                      }
                    }}
                    onClick={() => navigate('/auth/register')}
                  >
                    Register
                  </Button>
                </>
              )}
            </Stack>
          </Box>
        </Box>
      </Drawer>
      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 70, left: 0, right: 0, zIndex: 1301 }}>
        <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 4 }}>
          <Stack direction="row" spacing={2} sx={{ 
            overflowX: 'auto', 
            justifyContent: 'center',
            '&::-webkit-scrollbar': { display: 'none' }, 
            scrollbarWidth: 'none', 
            msOverflowStyle: 'none' 
          }}>
            {categories.map((cat) => (
              <Chip
                key={cat}
                label={cat}
                color={selectedCategory === cat ? 'primary' : 'default'}
                onClick={() => setSelectedCategory(cat)}
                sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}
              />
            ))}
          </Stack>
        </Box>
      </Box>
      <Box sx={{ position: 'absolute', top: 128, left: 0, right: 0, bottom: 0, px: 4, maxWidth: '1200px', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'row', gap: 4, minHeight: 0, zIndex: 1, pt: 3, pb: 3 }}>
        <Box
          component="main"
          sx={{
            flex: 1,
            height: 'calc(100vh - 128px)', // 128px = navbar+kategori desktop
            overflowY: 'auto',
            minHeight: 0,
            pt: 0,
            pb: 0,
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          {/* Breaking News Slider */}
          <BreakingNewsSlider newsData={newsData} kostum={kostum} />
          
          {/* Popular Tags */}
          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />

          {/* Latest Posts */}
          <LatestPosts newsData={newsData} />
          
          <Box sx={{ 
            display: 'flex',
            flexDirection: 'row',
            gap: 4,
            width: '100%',
            maxWidth: '100%',
            overflowX: 'auto',
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            px: 1,
            mt: 2,
            pb: 2, // Add padding bottom to hide scrollbar
          }}>
            {loading ? (
              Array.from({ length: 6 }).map((_, idx) => (
                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', minWidth: 320, flexShrink: 0 }}>
                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />
                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>
                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />
                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />
                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />
                  </CardContent>
                </Card>
              ))
            ) : filteredNews.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>
                <Typography variant="h6">Belum ada berita</Typography>
                <Typography variant="body2">Berita akan muncul di sini</Typography>
              </Box>
            ) : (
              filteredNews.map((news, idx) => (
                <Box key={news.id || idx} sx={{ minWidth: 320, flexShrink: 0 }}>
                                  <NewsCard
                  news={news}
                  expanded={!!expandedCards[news.id]}
                  onExpand={() => handleCardExpand(news.id)}
                  formatDate={formatDate}
                  truncateText={truncateText}
                  variant="desktop"
                  onShare={handleShare}
                  onBookmark={onBookmark}
                  isBookmarked={bookmarkedNews.has(news.id)}
                  onNewsClick={onNewsClick}
                  isAuthenticated={isAuthenticated}
                  onLike={onLike}
                  isLiked={likedNews.has(news.id)}
                />
                </Box>
              ))
            )}
          </Box>
        </Box>
        {/* Aside: Popular News */}
        <Box component="aside" sx={{ width: 320, mt: 0, display: 'block', mb: 0 }}>
          <Box sx={{ bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 3, mb: 6, mt: 0 }}>
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>Berita Populer</Typography>
            <Divider sx={{ mb: 2 }} />
            <Stack spacing={2}>
              {popularNews.length > 0 ? (
                popularNews.map((item, idx) => (
                  <Box key={item.id || idx} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar src={getImageUrl(item.image)} alt={item.title} sx={{ width: 48, height: 48, mr: 1 }} onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }} />
                    <Box>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'grey.900', lineHeight: 1.2 }}>
                        {item.title.length > 50 ? item.title.substring(0, 50) + '...' : item.title}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'grey.600' }}>
                        {formatDate(item.created_at || item.date)} • {item.views || 0} views
                      </Typography>
                    </Box>
                  </Box>
                ))
              ) : (
                <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center' }}>
                  Belum ada berita populer
                </Typography>
              )}
            </Stack>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

// Create custom theme based on kostum colors
const createCustomTheme = (kostum) => {
  return createTheme({
    palette: {
      primary: {
        main: kostum.primary_color || '#1976d2',
      },
      secondary: {
        main: kostum.secondary_color || '#dc004e',
      },
      background: {
        default: kostum.background_color || '#f5f5f5',
      },
      text: {
        primary: kostum.text_color || '#333333',
      },
    },
  });
};

export default function LandingPage() {
  const navigate = useNavigate();
  const { user, logout, isAuthenticated } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [bottomNav, setBottomNav] = useState(0);
  const [kostum, setKostum] = useState({
    logo: '',
    title: '',
    primary_color: '#1976d2',
    secondary_color: '#dc004e',
    accent_color: '#ff9800',
    background_color: '#f5f5f5',
    text_color: '#333333'
  });
  const [newsData, setNewsData] = useState([]);
  const [popularNews, setPopularNews] = useState([]);
  const [categoriesData, setCategoriesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedCards, setExpandedCards] = useState({});
  // Removed searchModalOpen - using searchActive overlay instead for consistency
  const [searchActive, setSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [bookmarkedNews, setBookmarkedNews] = useState(new Set());
  const [likedNews, setLikedNews] = useState(new Set());
  const [toast, setToast] = useState({ visible: false, message: '' });

  // Check URL parameters on component mount
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const kategori = urlParams.get('kategori');
    const beritaId = urlParams.get('berita');
    
    if (kategori && kategori !== 'Semua') {
      setSelectedCategory(kategori);
    }
    
    // If there's a specific news ID, we could highlight it later
    if (beritaId) {
      // You can add logic here to highlight or scroll to the specific news
      console.log('Shared news ID:', beritaId);
    }
  }, []);
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));


  const handleSidebar = () => setSidebarOpen(true);
  const handleSidebarClose = () => setSidebarOpen(false);

  const handleShare = async (news) => {
    try {
      // Update share count in database
      fetch('http://localhost:5000/api/posts/' + news.id + '/share', {
        method: 'POST'
      }).catch(err => console.error('Error updating share count:', err));

      // Generate URL-friendly title for the link
      const urlTitle = news.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim();

      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(link);

      // Update local state to reflect share count increase
      setNewsData(prevData =>
        prevData.map(item =>
          item.id === news.id
            ? { ...item, share: (item.share || 0) + 1 }
            : item
        )
      );

      // Show success toast
      setToast({ visible: true, message: 'Link berita berhasil disalin ke clipboard!' });

      // Auto-hide toast after 3 seconds
      setTimeout(() => {
        setToast({ visible: false, message: '' });
      }, 3000);

    } catch (error) {
      console.error('Failed to copy link:', error);
      setToast({ visible: true, message: 'Gagal menyalin link berita' });
      setTimeout(() => {
        setToast({ visible: false, message: '' });
      }, 3000);
    }
  };

  const handleLike = async (news) => {
    const isCurrentlyLiked = likedNews.has(news.id);

    try {
      // Optimistic update
      const newLikedNews = new Set(likedNews);
      if (isCurrentlyLiked) {
        newLikedNews.delete(news.id);
      } else {
        newLikedNews.add(news.id);
      }
      setLikedNews(newLikedNews);

      // Update like status in database
      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=toggle_like&id=${news.id}`, {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        // Update liked state based on server response
        const finalLikedNews = new Set(likedNews);
        if (data.liked) {
          finalLikedNews.add(news.id);
        } else {
          finalLikedNews.delete(news.id);
        }
        setLikedNews(finalLikedNews);

        // Update news data with new likes count
        setNewsData(prevNews =>
          prevNews.map(item =>
            item.id === news.id
              ? { ...item, likes: data.likes }
              : item
          )
        );

        setPopularNews(prevNews =>
          prevNews.map(item =>
            item.id === news.id
              ? { ...item, likes: data.likes }
              : item
          )
        );

        setToast({
          visible: true,
          message: data.liked ? 'Berita disukai!' : 'Like dihapus'
        });
      } else {
        // Revert optimistic update on failure
        setLikedNews(likedNews);
        setToast({ visible: true, message: 'Gagal memperbarui like' });
      }
    } catch (error) {
      // Revert optimistic update on error
      setLikedNews(likedNews);
      console.error('Failed to toggle like:', error);
      setToast({ visible: true, message: 'Gagal memperbarui like' });
    }

    // Hide toast after 3 seconds
    setTimeout(() => {
      setToast({ visible: false, message: '' });
    }, 3000);
  };

  const handleBookmark = async (news) => {
    // Check authentication first
    if (!isAuthenticated) {
      navigate('/auth/login');
      return;
    }

    const isCurrentlyBookmarked = bookmarkedNews.has(news.id);

    try {
      // Update bookmark status in database
      const action = isCurrentlyBookmarked ? 'remove_saved_news' : 'add_saved_news';

      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: action,
          id: news.id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update local state
        setBookmarkedNews(prev => {
          const newSet = new Set(prev);
          if (isCurrentlyBookmarked) {
            newSet.delete(news.id);
          } else {
            newSet.add(news.id);
          }
          return newSet;
        });

        // Show toast notification
        const message = isCurrentlyBookmarked
          ? 'Berita dihapus dari bookmark'
          : 'Berita ditambahkan ke bookmark';
        setToast({ visible: true, message });

        setTimeout(() => {
          setToast({ visible: false, message: '' });
        }, 3000);
      } else {
        setToast({ visible: true, message: 'Gagal mengupdate bookmark: ' + (result.message || 'Unknown error') });
        setTimeout(() => {
          setToast({ visible: false, message: '' });
        }, 3000);
      }
    } catch (error) {
      console.error('Error updating bookmark:', error);
      setToast({ visible: true, message: 'Error: ' + error.message });
      setTimeout(() => {
        setToast({ visible: false, message: '' });
      }, 3000);
    }
  };

  // Handler untuk buka search overlay
  const openSearch = () => {
    setSearchActive(true);
    setSearchQuery('');
  };
  // Handler untuk tutup search overlay
  const closeSearch = () => {
    setSearchActive(false);
    setSearchQuery('');
  };
  // Filter berita sesuai query
  const filteredSearch = searchQuery.trim() ? newsData.filter(
    n =>
      n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      n.description.toLowerCase().includes(searchQuery.toLowerCase())
  ).slice(0, 8) : [];
  // Handler keyboard ESC
  React.useEffect(() => {
    if (!searchActive) return;
    const handleKey = (e) => {
      if (e.key === 'Escape') closeSearch();
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [searchActive]);

  // Load website settings from database
  React.useEffect(() => {
    // Get settings from database via simple API (pengaturan table)
    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')
      .then(res => res.json())
      .then(response => {
        if (response.success && response.data) {
          const data = response.data;

          // Logo is now stored as base64 data in database
          let logoPath = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';

          if (data.logo_file_path && data.logo_file_path.startsWith('data:')) {
            // Logo is base64 data from database
            logoPath = data.logo_file_path;
          }

          setKostum({
            logo: logoPath,
            title: data.nama_website || 'React News Portal',
            description: data.deskripsi_website || 'Portal berita terkini dan terpercaya',
            primary_color: data.warna_primary || '#1976d2',
            secondary_color: data.warna_secondary || '#dc004e',
            accent_color: data.warna_accent || '#ff9800',
            background_color: '#f5f5f5',
            text_color: '#333333',
            footer_text: '© 2024 React News Portal. All rights reserved.',
            contact_email: '<EMAIL>',
            social_facebook: '',
            social_twitter: '',
            social_instagram: '',
            social_youtube: '',
            meta_keywords: 'berita, news, portal',
            meta_description: data.website_description || 'Portal berita terkini'
          });

          // Update document title and meta tags
          document.title = data.nama_website || 'React News Portal';

          // Update meta description
          const metaDescription = document.querySelector('meta[name="description"]');
          if (metaDescription) {
            metaDescription.setAttribute('content', data.website_description || 'Portal berita terkini');
          }

          // Update meta keywords
          const metaKeywords = document.querySelector('meta[name="keywords"]');
          if (metaKeywords) {
            metaKeywords.setAttribute('content', 'berita, news, portal');
          }

          // Update favicon (khusus untuk logo)
          if (data.logo_file_path && data.logo_file_path.startsWith('data:')) {
            let faviconUrl = data.logo_file_path;

            // Update existing favicon or create new one
            let favicon = document.querySelector('link[rel="icon"]') || document.querySelector('link[rel="shortcut icon"]');
            if (!favicon) {
              favicon = document.createElement('link');
              favicon.rel = 'icon';
              document.head.appendChild(favicon);
            }
            favicon.href = faviconUrl;

            // Also update apple-touch-icon if exists
            const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]');
            if (appleTouchIcon) {
              appleTouchIcon.href = faviconUrl;
            }

            console.log('LandingPage - Favicon updated:', faviconUrl);
          }
        } else {
          throw new Error('Invalid API response');
        }
      })
      .catch(err => {
        console.log('Database settings not available, using defaults:', err);
        // Use default settings matching database.sql structure
        setKostum({
          logo: 'data:image/svg+xml;base64,' + btoa(`
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="#3B82F6">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          `),
          title: 'React News Portal',
          description: 'Portal berita terkini dan terpercaya',
          primary_color: '#3B82F6',
          secondary_color: '#10B981',
          accent_color: '#F59E0B',
          background_color: '#f5f5f5',
          text_color: '#333333',
          footer_text: '© 2024 React News Portal. All rights reserved.',
          contact_email: '<EMAIL>',
          social_facebook: '',
          social_twitter: '',
          social_instagram: '',
          social_youtube: '',
          meta_keywords: 'berita, news, portal, react',
          meta_description: 'Portal berita terkini dengan teknologi React'
        });

        // Set default document title
        document.title = 'React News Portal';
      });
  }, []);

  // Function to refresh settings (can be called when admin updates settings)
  const refreshSettings = React.useCallback(() => {
    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan&t=' + Date.now())
      .then(res => res.json())
      .then(response => {
        if (response.success && response.data) {
          const data = response.data;

          // Logo is now stored as base64 data in database
          let logoPath = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';

          if (data.logo_file_path && data.logo_file_path.startsWith('data:')) {
            logoPath = data.logo_file_path;

            // Update favicon in refreshSettings
            let favicon = document.querySelector('link[rel="icon"]') || document.querySelector('link[rel="shortcut icon"]');
            if (!favicon) {
              favicon = document.createElement('link');
              favicon.rel = 'icon';
              document.head.appendChild(favicon);
            }
            favicon.href = logoPath;

            // Also update apple-touch-icon if exists
            const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]');
            if (appleTouchIcon) {
              appleTouchIcon.href = logoPath;
            }
          }

          setKostum(prev => ({
            ...prev,
            logo: logoPath,
            title: data.nama_website || 'React News Portal',
            description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'
          }));

          document.title = data.nama_website || 'React News Portal';
        }
      })
      .catch(err => console.log('Failed to refresh settings:', err));
  }, []);

  // Listen for settings updates (optional - for real-time updates)
  React.useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'settings_updated') {
        console.log('Settings updated, refreshing...');
        refreshSettings();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [refreshSettings]);

  // Load categories from database
  React.useEffect(() => {
    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_categories')
      .then(res => res.json())
      .then(data => {
        if (data.success && Array.isArray(data.data)) {
          // Map database structure to frontend format
          const mappedCategories = data.data.map(cat => ({
            id: cat.id,
            name: cat.name,
            slug: cat.slug,
            description: cat.description,
            color: cat.color || '#6B7280',
            is_active: cat.is_active,
            post_count: cat.post_count || 0,
            created_at: cat.created_at,
            updated_at: cat.updated_at
          }));

          // Filter only active categories
          const activeCategories = mappedCategories.filter(cat => cat.is_active !== false);
          setCategoriesData(activeCategories);
        }
      })
      .catch(err => {
        // Fallback categories matching database.sql structure
        setCategoriesData([
          {
            id: 1,
            name: 'Umum',
            slug: 'umum',
            description: 'Berita umum dan informasi terkini',
            color: '#6B7280',
            is_active: true,
            post_count: 0
          },
          {
            id: 2,
            name: 'Teknologi',
            slug: 'teknologi',
            description: 'Berita teknologi dan inovasi digital',
            color: '#3B82F6',
            is_active: true,
            post_count: 0
          },
          {
            id: 3,
            name: 'Bisnis',
            slug: 'bisnis',
            description: 'Berita bisnis dan ekonomi',
            color: '#10B981',
            is_active: true,
            post_count: 0
          },
          {
            id: 4,
            name: 'Olahraga',
            slug: 'olahraga',
            description: 'Berita olahraga dan kompetisi',
            color: '#F59E0B',
            is_active: true,
            post_count: 0
          },
          {
            id: 5,
            name: 'Hiburan',
            slug: 'hiburan',
            description: 'Berita hiburan dan selebriti',
            color: '#EF4444',
            is_active: true,
            post_count: 4
          },
          {
            id: 6,
            name: 'Politik',
            slug: 'politik',
            description: 'Berita politik dan pemerintahan',
            color: '#8B5CF6',
            is_active: true,
            post_count: 2
          },
          {
            id: 7,
            name: 'Kesehatan',
            slug: 'kesehatan',
            description: 'Berita kesehatan dan medis',
            color: '#06B6D4',
            is_active: true,
            post_count: 7
          }
        ]);
      });
  }, []);

  // Load news data from database
  React.useEffect(() => {
    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news')
      .then(res => res.json())
      .then(data => {
        if (data.success && Array.isArray(data.data)) {
          // Map database structure to frontend format
          const mappedData = data.data.map(item => ({
            id: item.id,
            title: item.title,
            slug: item.slug,
            description: item.description || item.excerpt || item.content?.substring(0, 200) + '...',
            content: item.content,
            image: item.image ? (item.image.startsWith('http') ? item.image : `http://localhost/react-news/uploads/${item.image}`) : 'https://via.placeholder.com/400x200?text=No+Image',
            image_alt: item.image_alt || item.title,
            category: item.category_name || 'Umum',
            category_name: item.category_name || 'Umum',
            category_id: item.category_id || 1,
            category_color: item.category_color || '#6B7280',
            status: item.status || 'published',
            featured: item.featured || false,
            tags: item.tags ? item.tags.split(',') : [],
            views: item.views || 0,
            share: item.share || 0,
            likes: item.likes || 0,
            comments_count: item.comments_count || 0,
            reading_time: item.reading_time || 5,
            date: item.date || item.created_at,
            published_at: item.published_at,
            created_at: item.created_at,
            updated_at: item.updated_at,
            author: item.author_name || item.full_name || 'Admin',
            user_id: item.user_id
          }));

          // Filter only published posts
          const publishedPosts = mappedData.filter(item => item.status === 'published');
          setNewsData(publishedPosts);

          // Sort by engagement score for popular news
          const popular = publishedPosts
            .sort((a, b) => {
              const scoreA = (a.views * 1) + (a.share * 3) + (a.likes * 2) + (a.comments_count * 4);
              const scoreB = (b.views * 1) + (b.share * 3) + (b.likes * 2) + (b.comments_count * 4);
              return scoreB - scoreA;
            })
            .slice(0, 5);
          setPopularNews(popular);
        } else {
          setNewsData([]);
          setPopularNews([]);
        }
        setLoading(false);
      })
      .catch(() => {
        // Fallback data matching database.sql structure
        const dummyNews = [
          {
            id: 1,
            title: 'Selamat Datang di React News Portal',
            slug: 'selamat-datang-di-react-news-portal',
            description: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna.',
            content: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna. Dengan desain responsif dan fitur-fitur canggih.',
            image: '/react-news/frontend/uploads/default-tech.jpg',
            image_alt: 'React News Portal',
            category: 'Teknologi',
            category_name: 'Teknologi',
            category_id: 2,
            category_color: '#3B82F6',
            status: 'published',
            featured: true,
            tags: ['teknologi', 'react', 'portal', 'berita'],
            views: 150,
            share: 25,
            likes: 45,
            comments_count: 8,
            reading_time: 3,
            date: new Date().toISOString(),
            published_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            author: 'Admin',
            user_id: 1
          },
          {
            id: 2,
            title: 'Teknologi AI Terbaru Mengubah Dunia Digital',
            slug: 'teknologi-ai-terbaru-mengubah-dunia-digital',
            description: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari.',
            content: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari. AI kini menjadi bagian integral dari transformasi digital.',
            image: '/react-news/frontend/uploads/default-ai.jpg',
            image_alt: 'Teknologi AI',
            category: 'Teknologi',
            category_name: 'Teknologi',
            category_id: 2,
            category_color: '#3B82F6',
            status: 'published',
            featured: false,
            tags: ['ai', 'teknologi', 'digital', 'inovasi'],
            views: 89,
            share: 12,
            likes: 23,
            comments_count: 5,
            reading_time: 4,
            date: new Date(Date.now() - 86400000).toISOString(),
            published_at: new Date(Date.now() - 86400000).toISOString(),
            created_at: new Date(Date.now() - 86400000).toISOString(),
            updated_at: new Date(Date.now() - 86400000).toISOString(),
            author: 'Admin',
            user_id: 1
          },
          {
            id: 3,
            title: 'Tips Investasi untuk Pemula di Era Digital',
            slug: 'tips-investasi-untuk-pemula-di-era-digital',
            description: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya.',
            content: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya. Pelajari strategi investasi yang tepat untuk pemula.',
            image: '/react-news/frontend/uploads/default-business.jpg',
            image_alt: 'Investasi Digital',
            category: 'Bisnis',
            category_name: 'Bisnis',
            category_id: 3,
            category_color: '#10B981',
            status: 'published',
            featured: false,
            tags: ['investasi', 'bisnis', 'digital', 'keuangan'],
            views: 67,
            share: 8,
            likes: 15,
            comments_count: 3,
            reading_time: 6,
            date: new Date(Date.now() - 172800000).toISOString(),
            published_at: new Date(Date.now() - 172800000).toISOString(),
            created_at: new Date(Date.now() - 172800000).toISOString(),
            updated_at: new Date(Date.now() - 172800000).toISOString(),
            author: 'Admin',
            user_id: 1
          }
        ];
        setNewsData(dummyNews);
        setPopularNews(dummyNews);
        setLoading(false);
      });
  }, []);

  // Load bookmarked news from database (only if authenticated)
  React.useEffect(() => {
    if (!isAuthenticated) {
      setBookmarkedNews(new Set()); // Clear bookmarks if not authenticated
      return;
    }

    const loadBookmarkedNews = async () => {
      try {
        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news');
        const data = await response.json();

        if (data.success && Array.isArray(data.data)) {
          // Extract news IDs that are bookmarked
          const bookmarkedIds = new Set(data.data.map(news => news.id));
          setBookmarkedNews(bookmarkedIds);
        }
      } catch (error) {
        console.log('Could not load bookmarked news:', error);
      }
    };

    loadBookmarkedNews();
  }, [isAuthenticated]);

  // Load liked posts from database (based on IP address)
  React.useEffect(() => {
    const loadLikedPosts = async () => {
      try {
        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_liked_posts');
        const data = await response.json();

        if (data.success && Array.isArray(data.data)) {
          // Extract post IDs that are liked by this IP
          const likedIds = new Set(data.data.map(id => parseInt(id)));
          setLikedNews(likedIds);
          console.log('Loaded liked posts for IP:', data.ip_address, 'Posts:', data.data);
        }
      } catch (error) {
        console.log('Could not load liked posts:', error);
      }
    };

    loadLikedPosts();
  }, []); // Load once on component mount

  // Update category post counts when both newsData and categoriesData are loaded
  React.useEffect(() => {
    if (newsData.length > 0 && categoriesData.length > 0) {
      // Check if categories already have correct post_count from API
      const hasApiCounts = categoriesData.some(cat => cat.post_count > 0);

      if (!hasApiCounts) {
        // Count posts per category from newsData
        const postCounts = {};
        newsData.forEach(news => {
          const categoryId = news.category_id;
          postCounts[categoryId] = (postCounts[categoryId] || 0) + 1;
        });

        // Update categories with actual post counts
        const updatedCategories = categoriesData.map(cat => ({
          ...cat,
          post_count: postCounts[cat.id] || 0
        }));

        setCategoriesData(updatedCategories);
      }
    }
  }, [newsData, categoriesData]);

  const handleCardExpand = (cardId) => {
    setExpandedCards(prev => ({ ...prev, [cardId]: !prev[cardId] }));
  };

  const handleNewsClick = async (newsId) => {
    // Increment views when clicking news card
    try {
      await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=increment_views&id=${newsId}`, {
        method: 'POST'
      });
    } catch (error) {
      console.log('Could not increment views:', error);
    }

    // Navigate to news detail
    navigate(`/news/${newsId}`);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const handleSearchClick = () => {
    // Use the same search function as bottom nav for consistency
    openSearch();
    setSidebarOpen(false); // Close sidebar when search opens
  };

  // Handler untuk klik bottom nav
  const handleBottomNavChange = (newValue) => {
    if (newValue === 0) {
      setBottomNav(0);
      // Already on home page, just update state
    } else if (newValue === 1) {
      setBottomNav(1);
      // Video - Navigate to new video player page
      setBottomNav(1);
      console.log('Navigating to video player page...');
      // Direct navigation to new video player page
      window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-play.php';
    } else if (newValue === 2) {
      setBottomNav(2);
      openSearch(); // Search
    } else if (newValue === 3) {
      // Simpan - Check authentication first
      if (isAuthenticated) {
        window.location.href = '/saved'; // Navigate to saved page
      } else {
        // Redirect to login if not authenticated
        navigate('/auth/login');
      }
    } else {
      setBottomNav(newValue);
    }
  };

  const customTheme = createCustomTheme(kostum);

  if (isDesktop) {
    return (
      <ThemeProvider theme={customTheme}>
        <DesktopNewsLayout
          kostum={kostum}
          newsData={newsData}
          popularNews={popularNews}
          categoriesData={categoriesData}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          expandedCards={expandedCards}
          handleCardExpand={handleCardExpand}
          loading={loading}
          handleShare={handleShare}
          onBookmark={handleBookmark}
          bookmarkedNews={bookmarkedNews}
          onSearchClick={openSearch}
          sidebarOpen={sidebarOpen}
          handleSidebar={handleSidebar}
          handleSidebarClose={handleSidebarClose}
          onNewsClick={handleNewsClick}
          openSearch={openSearch}
          navigate={navigate}
          user={user}
          isAuthenticated={isAuthenticated}
          handleLogout={handleLogout}
          onLike={handleLike}
          likedNews={likedNews}
        />
        
        {/* Toast Notification */}
        <Toast 
          message={toast.message} 
          isVisible={toast.visible} 
          onClose={() => setToast({ visible: false, message: '' })} 
        />
        {/* Search Overlay for Desktop */}
        {searchActive && (
          <>
            {/* Backdrop */}
            <Box
              onClick={closeSearch}
              sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 2100,
                bgcolor: 'rgba(0,0,0,0.3)',
                animation: 'fadeIn 0.3s ease-out',
                '@keyframes fadeIn': {
                  '0%': { opacity: 0 },
                  '100%': { opacity: 1 },
                },
              }}
            />
            {/* Search Content */}
            <Box sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 2101,
              bgcolor: 'rgba(255,255,255,0.98)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              px: 2,
              pt: 10, // Space for navbar
              pb: 3,
              animation: 'slideDown 0.3s ease-out',
              '@keyframes slideDown': {
                '0%': {
                  transform: 'translateY(-100%)',
                  opacity: 0,
                },
                '100%': {
                  transform: 'translateY(0)',
                  opacity: 1,
                },
              },
            }}>
            <Box sx={{ maxWidth: 600, mx: 'auto', position: 'relative' }}>
              <TextField
                autoFocus
                fullWidth
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder="Cari berita di sini..."
                variant="outlined"
                sx={{
                  fontSize: 18,
                  bgcolor: '#fff',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    fontSize: 16,
                    '&:hover fieldset': {
                      borderColor: 'primary.main',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                    },
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ color: 'grey.400', mr: 1 }} />
                  ),
                }}
              />
              <IconButton
                onClick={closeSearch}
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  zIndex: 10,
                  bgcolor: 'rgba(0,0,0,0.05)',
                  '&:hover': {
                    bgcolor: 'rgba(0,0,0,0.1)',
                  },
                }}
                aria-label="Tutup"
              >
                <CloseIcon />
              </IconButton>
            </Box>
            {/* Hasil search */}
            {searchQuery.trim() && (
              <Box sx={{
                maxWidth: 600,
                mx: 'auto',
                mt: 2,
                bgcolor: '#fff',
                borderRadius: 3,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                p: 2,
                maxHeight: '60vh',
                overflowY: 'auto',
              }}>
                {filteredSearch.length === 0 ? (
                  <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center', py: 3 }}>
                    Tidak ada berita ditemukan untuk "{searchQuery}"
                  </Typography>
                ) : (
                  <>
                    <Typography variant="body2" sx={{ color: 'grey.600', mb: 2, fontWeight: 500 }}>
                      Ditemukan {filteredSearch.length} berita
                    </Typography>
                    {filteredSearch.map((item) => (
                      <Box
                        key={item.id}
                        onClick={() => {
                          closeSearch();
                          handleNewsClick(item.id);
                        }}
                        sx={{
                          py: 2,
                          px: 2,
                          borderBottom: '1px solid #f0f0f0',
                          cursor: 'pointer',
                          borderRadius: 2,
                          '&:last-child': { borderBottom: 0 },
                          '&:hover': {
                            bgcolor: 'rgba(25, 118, 210, 0.04)',
                            transform: 'translateY(-1px)',
                            transition: 'all 0.2s ease',
                          },
                        }}
                      >
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'primary.main', mb: 0.5 }}>
                          {item.title}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'grey.600', display: 'block', mb: 1 }}>
                          {item.category} • {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'grey.700', lineHeight: 1.4 }}>
                          {item.description.length > 100 ? item.description.substring(0, 100) + '...' : item.description}
                        </Typography>
                      </Box>
                    ))}
                  </>
                )}
              </Box>
            )}
          </Box>
          </>
        )}
      </ThemeProvider>
    );
  }
  return (
    <ThemeProvider theme={customTheme}>
      <MobileNewsLayout
        kostum={kostum}
        newsData={newsData}
        popularNews={popularNews}
        categoriesData={categoriesData}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        expandedCards={expandedCards}
        handleCardExpand={handleCardExpand}
        loading={loading}
        bottomNav={bottomNav}
        setBottomNav={setBottomNav}
        handleSidebar={handleSidebar}
        sidebarOpen={sidebarOpen}
        handleSidebarClose={handleSidebarClose}
        handleShare={handleShare}
        onBookmark={handleBookmark}
        bookmarkedNews={bookmarkedNews}
        onSearchClick={handleSearchClick}
        handleBottomNavChange={handleBottomNavChange}
        onNewsClick={handleNewsClick}
        openSearch={openSearch}
        navigate={navigate}
        user={user}
        isAuthenticated={isAuthenticated}
        handleLogout={handleLogout}
        onLike={handleLike}
        likedNews={likedNews}
      />
      
      {/* Toast Notification */}
      <Toast 
        message={toast.message} 
        isVisible={toast.visible} 
        onClose={() => setToast({ visible: false, message: '' })} 
      />
      {/* Overlay input search */}
      {searchActive && (
        <Box sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 2101,
          bgcolor: 'rgba(255,255,255,0.98)',
          boxShadow: 3,
          px: { xs: 2, md: 0 },
          pt: { xs: 2, md: 0 }, // desktop: pt: 0 agar overlay tepat di atas
          pb: 2,
        }}>
          <Box sx={{ maxWidth: 500, mx: 'auto', position: 'relative' }}>
            <TextField
              autoFocus
              fullWidth
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder="Cari berita di sini..."
              variant="outlined"
              sx={{ fontSize: 18, bgcolor: '#fff' }}
            />
            <IconButton
              onClick={closeSearch}
              sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}
              aria-label="Tutup"
            >
              <CloseIcon />
            </IconButton>
          </Box>
          {/* Hasil search */}
          {searchQuery.trim() && (
            <Box sx={{ maxWidth: 500, mx: 'auto', mt: 1, bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 1 }}>
              {filteredSearch.length === 0 ? (
                <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center', py: 2 }}>
                  Tidak ada berita ditemukan
                </Typography>
              ) : (
                filteredSearch.map((item) => (
                  <Box
                    key={item.id}
                    onClick={() => {
                      closeSearch();
                      handleNewsClick(item.id);
                    }}
                    sx={{
                      py: 1,
                      px: 1,
                      borderBottom: '1px solid #eee',
                      cursor: 'pointer',
                      '&:last-child': { borderBottom: 0 },
                      '&:hover': {
                        backgroundColor: 'grey.50'
                      }
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>{item.title}</Typography>
                    <Typography variant="caption" sx={{ color: 'grey.600' }}>{item.category} &bull; {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</Typography>
                    <Typography variant="body2" sx={{ color: 'grey.800' }}>
                      {item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description}
                    </Typography>
                  </Box>
                ))
              )}
            </Box>
          )}
        </Box>
      )}
      {/* Search modal removed - using consistent search overlay instead */}
    </ThemeProvider>
  );
}
