import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Avatar,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Lock,
  ArrowBack,
  Vpn<PERSON>ey,
  CheckCircle
} from '@mui/icons-material';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(0); // 0: paste token, 1: new password, 2: success
  const [formData, setFormData] = useState({
    jwtToken: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const steps = ['Paste JWT Token', 'Reset Password', 'Complete'];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleTokenSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:5000/api/auth/verify-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: formData.jwtToken
        })
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('JWT Token berhasil diverifikasi. Silakan masukkan password baru Anda.');
        setStep(1);
      } else {
        setError(data.message || 'JWT Token tidak valid atau sudah kadaluarsa');
      }
    } catch (error) {
      console.error('Token verification error:', error);
      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');
    } finally {
      setLoading(false);
    }
  };



  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    if (formData.newPassword !== formData.confirmPassword) {
      setError('Password tidak cocok');
      return;
    }

    if (formData.newPassword.length < 6) {
      setError('Password minimal 6 karakter');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:5000/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: formData.jwtToken,
          newPassword: formData.newPassword
        })
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Password berhasil direset! Anda akan diarahkan ke halaman login.');
        setStep(2);
        setTimeout(() => {
          navigate('/auth/login');
        }, 3000);
      } else {
        setError(data.message || 'Gagal mereset password');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 0:
        return (
          <form onSubmit={handleTokenSubmit}>
            <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
              <Typography variant="body2">
                Masukkan JWT token yang Anda terima saat registrasi. Token ini diperlukan untuk mereset password Anda.
              </Typography>
            </Alert>

            <TextField
              fullWidth
              name="jwtToken"
              label="JWT Token"
              placeholder="Paste JWT token yang Anda terima saat registrasi di sini..."
              value={formData.jwtToken}
              onChange={handleChange}
              required
              multiline
              rows={4}
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}

              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <VpnKey color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{
                mb: 3,
                py: { xs: 1.5, md: 2 },
                borderRadius: 2,
                textTransform: 'none',
                fontSize: { xs: 16, md: 18 },
                fontWeight: 600,
                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',
                  transform: 'translateY(-2px)'
                },
                '&:disabled': {
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {loading ? 'Verifying...' : 'Verify JWT Token'}
            </Button>
          </form>
        );

      case 1:
        return (
          <form onSubmit={handlePasswordSubmit}>
            <TextField
              fullWidth
              name="newPassword"
              type="password"
              label="New Password"
              placeholder="Masukkan password baru Anda (min. 6 karakter)"
              value={formData.newPassword}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />
            <TextField
              fullWidth
              name="confirmPassword"
              type="password"
              label="Confirm New Password"
              placeholder="Konfirmasi password baru Anda"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{
                mb: 3,
                py: { xs: 1.5, md: 2 },
                borderRadius: 2,
                textTransform: 'none',
                fontSize: { xs: 16, md: 18 },
                fontWeight: 600,
                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',
                  transform: 'translateY(-2px)'
                },
                '&:disabled': {
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {loading ? 'Resetting...' : 'Reset Password'}
            </Button>
          </form>
        );

      case 2:
        return (
          <Box sx={{ textAlign: 'center' }}>
            <CheckCircle sx={{ color: 'success.main', fontSize: 64, mb: 2 }} />
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Password Reset Successful!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Your password has been reset successfully. You will be redirected to the login page shortly.
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/auth/login')}
              sx={{
                py: 1.5,
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontSize: 16,
                fontWeight: 600
              }}
            >
              Go to Login
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      p: { xs: 2, sm: 3, md: 4 },
      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'
    }}>
      <Card sx={{
        maxWidth: { xs: '100%', sm: 450, md: 480 },
        width: '100%',
        borderRadius: { xs: 2, md: 4 },
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
        border: '1px solid rgba(0, 0, 0, 0.08)',
        backgroundColor: '#ffffff'
      }}>
        <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 3, md: 4 } }}>
            <IconButton
              onClick={() => navigate('/auth/login')}
              sx={{
                position: 'absolute',
                top: { xs: 12, md: 16 },
                left: { xs: 12, md: 16 },
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.08)'
                }
              }}
            >
              <ArrowBack />
            </IconButton>

            <Avatar sx={{
              width: { xs: 56, md: 72 },
              height: { xs: 56, md: 72 },
              mx: 'auto',
              mb: { xs: 2, md: 3 },
              bgcolor: 'warning.main',
              boxShadow: '0 8px 32px rgba(255, 152, 0, 0.3)'
            }}>
              <VpnKey fontSize="large" />
            </Avatar>

            <Typography
              variant="h4"
              fontWeight="700"
              color="text.primary"
              gutterBottom
              sx={{ fontSize: { xs: '1.75rem', md: '2.125rem' } }}
            >
              Reset Password
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontSize: { xs: '0.875rem', md: '1rem' } }}
            >
              {step === 0 && "Paste your JWT token to verify your identity"}
              {step === 1 && "Create a new password for your account"}
              {step === 2 && "Password reset completed successfully"}
            </Typography>
          </Box>

          {/* Progress Stepper */}
          <Stepper
            activeStep={step}
            sx={{
              mb: 4,
              '& .MuiStepLabel-label': {
                fontSize: { xs: '0.875rem', md: '1rem' }
              }
            }}
          >
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Error/Success Alert */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: { xs: '0.875rem', md: '1rem' }
                }
              }}
            >
              {error}
            </Alert>
          )}

          {success && (
            <Alert
              severity="success"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: { xs: '0.875rem', md: '1rem' }
                }
              }}
            >
              {success}
            </Alert>
          )}

          {/* Step Content */}
          {renderStepContent()}

          {/* Back to Login */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Remember your password?{' '}
              <Link 
                to="/auth/login" 
                style={{ 
                  textDecoration: 'none',
                  color: '#1976d2',
                  fontWeight: 600
                }}
              >
                Sign In
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ForgotPassword;
