import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import LandingPage from './pages/user/LandingPage';
import VideoPage from './pages/user/VideoPage';
import VideoPlay from './pages/user/VideoPlay';
import DataNews from './pages/user/DataNews';
import Saved from './pages/user/components/Saved';
import './App.css';
import Register from './pages/admin/auth/Register';
import AdminLogin from './pages/admin/auth/AdminLogin';
// User Auth Components
import UserLogin from './pages/user/auth/Login';
import UserRegister from './pages/user/auth/Register';
import ForgotPassword from './pages/user/auth/ForgotPassword';

// Component untuk redirect ke dashboard admin
const AdminDashboardRedirect = () => {
  React.useEffect(() => {
    // Check if user is authenticated
    const adminAuth = localStorage.getItem('admin_auth');
    if (!adminAuth) {
      // Not authenticated, redirect to admin login
      window.location.href = '/admin/login';
      return;
    }

    try {
      const authData = JSON.parse(adminAuth);
      if (!authData.isAuthenticated) {
        // Not authenticated, redirect to admin login
        window.location.href = '/admin/login';
        return;
      }
      // Authenticated, redirect to admin dashboard with clean URL
      window.location.href = 'http://localhost:3000/admin/dashboard';
    } catch (e) {
      // Invalid auth data, clear it and redirect to admin login
      localStorage.removeItem('admin_auth');
      window.location.href = '/admin/login';
    }
  }, []);

  return (
    <div style={{ padding: '20px', textAlign: 'center', fontFamily: 'Arial, sans-serif' }}>
      <div style={{ maxWidth: '400px', margin: '0 auto', marginTop: '100px' }}>
        <h2 style={{ color: '#3B82F6', marginBottom: '20px' }}>🚀 Redirecting to Admin Dashboard...</h2>
        <p style={{ fontSize: '16px', color: '#666' }}>
          Mengalihkan ke dashboard admin...
        </p>
      </div>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
        {/* User Routes */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/home" element={<LandingPage />} />
        <Route path="/video" element={<VideoPage />} />
        <Route path="/video-play" element={<VideoPlay />} />
        <Route path="/data-news" element={<DataNews />} />
        <Route path="/news" element={<DataNews />} />
        <Route path="/news/:id" element={<DataNews />} />
        <Route path="/saved" element={<Saved />} />
        <Route path="/bookmark" element={<Saved />} />

        {/* User Auth Routes */}
        <Route path="/auth/login" element={<UserLogin />} />
        <Route path="/auth/register" element={<UserRegister />} />
        <Route path="/auth/forgot-password" element={<ForgotPassword />} />

        {/* Admin Routes */}
        <Route path="/admin" element={<AdminDashboardRedirect />} />
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route path="/admin/register" element={<Register />} />

        {/* Dashboard Routes - Clean URLs without .php */}
        <Route path="/dashboard" element={<AdminDashboardRedirect />} />
        <Route path="/admin/dashboard" element={<AdminDashboardRedirect />} />

        {/* Fallback Route */}
        <Route path="*" element={<LandingPage />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
