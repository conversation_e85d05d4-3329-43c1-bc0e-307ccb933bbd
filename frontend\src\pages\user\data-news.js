import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

const DataNews = () => {
    // Data News Component - Fixed Version
    const { id } = useParams();
    const navigate = useNavigate();
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
    const [news, setNews] = useState(null);
    const [relatedNews, setRelatedNews] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [themeSettings, setThemeSettings] = useState({
        primary_color: '#3B82F6',
        secondary_color: '#10B981',
        accent_color: '#F59E0B'
    });
    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });
    const [bottomNav, setBottomNav] = useState(0);

    useEffect(() => {
        if (id) {
            fetchNewsDetail(id);
        }
        // Fetch kostum data
        fetchKostumData();
        // Load theme settings
        loadThemeSettings();
    }, [id]);

    // Load theme settings from admin
    const loadThemeSettings = async () => {
        try {
            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const text = await response.text();
            console.log('Raw theme response:', text);

            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text:', text);
                throw new Error('Invalid JSON response');
            }

            if (data.success && data.data) {
                setThemeSettings({
                    primary_color: data.data.primary_color || '#3B82F6',
                    secondary_color: data.data.secondary_color || '#10B981',
                    accent_color: data.data.accent_color || '#F59E0B'
                });
                console.log('🎨 Theme settings loaded:', data.data);
            }
        } catch (error) {
            console.error('❌ Error loading theme settings:', error);
        }
    };

    const fetchKostumData = async () => {
        try {
            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');
            const data = await response.json();

            if (data.success && data.data) {
                const settings = data.data;
                setKostum({
                    logo: settings.website_logo || '',
                    title: settings.website_name || 'React News Portal'
                });
            }
        } catch (error) {
            // Fallback to default values if API fails
            setKostum({
                logo: '',
                title: 'React News Portal'
            });
        }
    };

    const fetchNewsDetail = async (newsId) => {
        try {
            setLoading(true);

            // Fetch news detail (views already incremented from landing page)
            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.data) {
                setNews(data.data);
                // Fetch related news
                if (data.data.category_id) {
                    fetchRelatedNews(data.data.category_id, newsId);
                }
            } else {
                setError('Berita tidak ditemukan');
            }
        } catch (error) {
            console.error('Error fetching news:', error);
            setError('Gagal memuat berita. Silakan coba lagi.');
        } finally {
            setLoading(false);
        }
    };

    const fetchRelatedNews = async (categoryId, currentNewsId) => {
        try {
            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && Array.isArray(data.data)) {
                // Filter out current news and limit to 3
                const filtered = data.data
                    .filter(item => item.id !== parseInt(currentNewsId))
                    .slice(0, 3);
                setRelatedNews(filtered);
            } else {
                setRelatedNews([]);
            }
        } catch (error) {
            console.error('Error fetching related news:', error);
            setRelatedNews([]);
        }
    };

    const getImageUrl = (imagePath) => {
        if (!imagePath) return 'https://source.unsplash.com/800x400/?news';

        console.log('data-news.js - Processing image path:', imagePath);

        // Jika sudah URL lengkap, gunakan langsung
        if (imagePath.startsWith('http')) {
            console.log('data-news.js - Using full URL:', imagePath);
            return imagePath;
        }

        // If it's already the correct path, use it directly
        if (imagePath.startsWith('/react-news/frontend/uploads/')) {
            const finalUrl = `http://localhost${imagePath}`;
            console.log('data-news.js - Using direct path:', finalUrl);
            return finalUrl;
        }

        // Extract filename from any path format
        let filename = '';

        if (imagePath.startsWith('/react-news/uploads/')) {
            filename = imagePath.replace('/react-news/uploads/', '');
        } else if (imagePath.startsWith('/uploads/')) {
            filename = imagePath.replace('/uploads/', '');
        } else if (imagePath.startsWith('assets/news/')) {
            filename = imagePath.replace('assets/news/', '');
        } else if (!imagePath.includes('/')) {
            // Just filename
            filename = imagePath;
        } else {
            // Extract filename from any other path
            filename = imagePath.split('/').pop();
        }

        // Use consistent frontend/uploads path
        const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;
        console.log('data-news.js - Final URL:', finalUrl);
        return finalUrl;
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleRelatedNewsClick = (newsId) => {
        navigate(`/data-news/${newsId}`);
    };

    const handleBackToHome = () => {
        navigate('/');
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div
                        className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
                        style={{ borderBottomColor: themeSettings.primary_color }}
                    ></div>
                    <p className="text-gray-600">Memuat berita...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4">
                        <i className="fas fa-exclamation-triangle mr-2"></i>
                        {error}
                    </div>
                    <button
                        onClick={handleBackToHome}
                        className="text-white px-6 py-2 rounded-lg transition-colors"
                        style={{
                            backgroundColor: themeSettings.primary_color,
                            ':hover': { backgroundColor: themeSettings.secondary_color }
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}
                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}
                    >
                        <i className="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </button>
                </div>
            </div>
        );
    }

    if (!news) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600">Berita tidak ditemukan</p>
                    <button
                        onClick={handleBackToHome}
                        className="mt-4 text-white px-6 py-2 rounded-lg transition-colors"
                        style={{
                            backgroundColor: themeSettings.primary_color
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}
                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}
                    >
                        <i className="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </button>
                </div>
            </div>
        );
    }

    return (
        <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>
            {/* Responsive Navigation Bar */}
            <AppBar position="fixed" color="inherit" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>
                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                        <Avatar
                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}
                            alt="Logo"
                            sx={{ width: 48, height: 48, mr: 2 }}
                            onError={(e) => { e.target.src = '/logo192.png'; }}
                        />
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>
                            {kostum.title}
                        </Typography>
                    </Box>
                    <IconButton
                        edge="end"
                        color="primary"
                        onClick={() => navigate('/')}
                        sx={{ mr: 1 }}
                    >
                        <MenuIcon fontSize="large" />
                    </IconButton>
                </Toolbar>
            </AppBar>

            {/* Header/Breadcrumb */}
            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>
                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>
                        <Box
                            component="button"
                            onClick={handleBackToHome}
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: 'text.secondary',
                                '&:hover': { color: 'primary.main' },
                                transition: 'color 0.2s',
                                border: 'none',
                                background: 'none',
                                cursor: 'pointer'
                            }}
                        >
                            <i className="fas fa-home" style={{ marginRight: '4px' }}></i>
                            Beranda
                        </Box>
                        <i className="fas fa-chevron-right" style={{ fontSize: '12px' }}></i>
                        <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.primary' }}>
                            Detail Berita
                        </Typography>
                    </Box>
                </Box>
            </Box>

            {/* Main Content */}
            <Box sx={{
                maxWidth: '1200px',
                mx: 'auto',
                px: 2,
                py: 4,
                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav
            }}>
                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>
                    {/* Main Article */}
                    <Box>
                        <article className="bg-white rounded-lg shadow-md overflow-hidden">
                            {/* Article Header */}
                            <div className="relative">
                                <img 
                                    src={getImageUrl(news.image)} 
                                    alt={news.title}
                                    className="w-full h-64 md:h-80 object-cover"
                                    onError={(e) => {
                                        e.target.src = 'https://picsum.photos/800/400?random=2';
                                    }}
                                />
                                <div className="absolute top-4 left-4">
                                    <span
                                        className="text-white px-3 py-1 rounded-full text-sm font-medium"
                                        style={{ backgroundColor: themeSettings.primary_color }}
                                    >
                                        {news.category_name || 'Berita'}
                                    </span>
                                </div>
                            </div>

                            {/* Article Content */}
                            <div className="p-6 md:p-8">
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight">
                                    {news.title}
                                </h1>

                                {/* Meta Information */}
                                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b">
                                    <div className="flex items-center">
                                        <i
                                            className="fas fa-calendar-alt mr-2"
                                            style={{ color: themeSettings.primary_color }}
                                        ></i>
                                        {formatDate(news.created_at || news.date)}
                                    </div>
                                    <div className="flex items-center">
                                        <i
                                            className="fas fa-clock mr-2"
                                            style={{ color: themeSettings.primary_color }}
                                        ></i>
                                        {formatTime(news.created_at || news.date)}
                                    </div>
                                    <div className="flex items-center">
                                        <i
                                            className="fas fa-eye mr-2"
                                            style={{ color: themeSettings.primary_color }}
                                        ></i>
                                        {news.views || 0} views
                                    </div>
                                </div>

                                {/* Article Body */}
                                <div className="prose prose-lg max-w-none">
                                    <div 
                                        className="text-gray-700 leading-relaxed whitespace-pre-line"
                                        dangerouslySetInnerHTML={{ 
                                            __html: news.content.replace(/\n/g, '<br>') 
                                        }}
                                    />
                                </div>

                                {/* Share Buttons */}
                                <div className="mt-8 pt-6 border-t">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Bagikan Artikel</h3>
                                    <div className="flex space-x-3">
                                        <button
                                            className="text-white px-4 py-2 rounded-lg transition-colors"
                                            style={{ backgroundColor: themeSettings.primary_color }}
                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}
                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}
                                        >
                                            <i className="fab fa-facebook-f mr-2"></i>
                                            Facebook
                                        </button>
                                        <button
                                            className="text-white px-4 py-2 rounded-lg transition-colors"
                                            style={{ backgroundColor: themeSettings.accent_color }}
                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.primary_color}
                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.accent_color}
                                        >
                                            <i className="fab fa-twitter mr-2"></i>
                                            Twitter
                                        </button>
                                        <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                            <i className="fab fa-whatsapp mr-2"></i>
                                            WhatsApp
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </Box>

                    {/* Sidebar */}
                    <Box>
                        {/* Related News */}
                        {relatedNews.length > 0 && (
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    <i
                                        className="fas fa-newspaper mr-2"
                                        style={{ color: themeSettings.primary_color }}
                                    ></i>
                                    Berita Terkait
                                </h3>
                                <div className="space-y-4">
                                    {relatedNews.map((item) => (
                                        <div 
                                            key={item.id}
                                            onClick={() => handleRelatedNewsClick(item.id)}
                                            className="flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                                        >
                                            <img 
                                                src={getImageUrl(item.image)} 
                                                alt={item.title}
                                                className="w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0"
                                                onError={(e) => {
                                                    e.target.src = 'https://picsum.photos/150/150?random=3';
                                                }}
                                            />
                                            <div className="flex-1 min-w-0">
                                                <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                                                    {item.title}
                                                </h4>
                                                <p className="text-xs text-gray-600">
                                                    {formatDate(item.created_at || item.date)}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Back to Home Button */}
                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>
                            <Box
                                component="button"
                                onClick={handleBackToHome}
                                sx={{
                                    width: '100%',
                                    bgcolor: 'primary.main',
                                    color: 'white',
                                    py: 1.5,
                                    px: 2,
                                    borderRadius: 2,
                                    border: 'none',
                                    cursor: 'pointer',
                                    fontWeight: 500,
                                    transition: 'background-color 0.2s',
                                    '&:hover': {
                                        bgcolor: 'primary.dark'
                                    }
                                }}
                            >
                                <i className="fas fa-arrow-left" style={{ marginRight: '8px' }}></i>
                                Kembali ke Beranda
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </Box>

            {/* Custom Bottom Navigation - Mobile Only */}
            <Box sx={{
                position: 'fixed',
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 1300,
                display: { xs: 'block', md: 'none' }, // Hide on desktop
                backgroundColor: 'white',
                borderTop: '1px solid #e0e0e0',
                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'
            }}>
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-around',
                    alignItems: 'center',
                    height: 64,
                    px: 1
                }}>
                    <Box
                        onClick={() => navigate('/')}
                        className="bottom-nav-item active"
                        style={{ cursor: 'pointer' }}
                    >
                        <i className="fas fa-home bottom-nav-icon text-blue-600"></i>
                        <Typography variant="caption" className="bottom-nav-label" sx={{ color: 'primary.main' }}>
                            Home
                        </Typography>
                    </Box>

                    <Box
                        onClick={() => navigate('/')}
                        className="bottom-nav-item"
                        style={{ cursor: 'pointer' }}
                    >
                        <i className="fas fa-search bottom-nav-icon text-gray-500"></i>
                        <Typography variant="caption" className="bottom-nav-label" sx={{ color: 'text.secondary' }}>
                            Cari
                        </Typography>
                    </Box>

                    <Box
                        onClick={() => navigate('/saved')}
                        className="bottom-nav-item"
                        style={{ cursor: 'pointer' }}
                    >
                        <i className="fas fa-bookmark bottom-nav-icon text-gray-500"></i>
                        <Typography variant="caption" className="bottom-nav-label" sx={{ color: 'text.secondary' }}>
                            Simpan
                        </Typography>
                    </Box>
                </Box>
            </Box>
        </Box>
    );
};

export default DataNews;
