[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js": "20", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js": "21", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js": "22", "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js": "23", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js": "24", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js": "25", "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js": "26", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPlay.js": "27", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\DataNews.js": "28", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\DetailNews.js": "29"}, {"size": 535, "mtime": 1752195971363, "results": "30", "hashOfConfig": "31"}, {"size": 3627, "mtime": 1752832296782, "results": "32", "hashOfConfig": "31"}, {"size": 362, "mtime": 1752195971670, "results": "33", "hashOfConfig": "31"}, {"size": 96273, "mtime": 1752833309288, "results": "34", "hashOfConfig": "31"}, {"size": 243, "mtime": 1752201497899, "results": "35", "hashOfConfig": "31"}, {"size": 662, "mtime": 1752201773682, "results": "36", "hashOfConfig": "31"}, {"size": 313, "mtime": 1752201773682, "results": "37", "hashOfConfig": "31"}, {"size": 822, "mtime": 1752201773678, "results": "38", "hashOfConfig": "31"}, {"size": 688, "mtime": 1752201773679, "results": "39", "hashOfConfig": "31"}, {"size": 749, "mtime": 1752201773681, "results": "40", "hashOfConfig": "31"}, {"size": 5407, "mtime": 1752367525610, "results": "41", "hashOfConfig": "31"}, {"size": 218, "mtime": 1752206025063, "results": "42", "hashOfConfig": "31"}, {"size": 4157, "mtime": 1752220101050, "results": "43", "hashOfConfig": "31"}, {"size": 18132, "mtime": 1752811518929, "results": "44", "hashOfConfig": "31"}, {"size": 0, "mtime": 1752306675896, "results": "45", "hashOfConfig": "31"}, {"size": 11682, "mtime": 1752310168602, "results": "46", "hashOfConfig": "31"}, {"size": 13390, "mtime": 1752310218662, "results": "47", "hashOfConfig": "31"}, {"size": 16930, "mtime": 1752310279691, "results": "48", "hashOfConfig": "31"}, {"size": 4742, "mtime": 1752366365655, "results": "49", "hashOfConfig": "31"}, {"size": 13804, "mtime": 1752827509245, "results": "50", "hashOfConfig": "31"}, {"size": 14630, "mtime": 1752826007221, "results": "51", "hashOfConfig": "31"}, {"size": 8407, "mtime": 1752547081826, "results": "52", "hashOfConfig": "31"}, {"size": 4610, "mtime": 1752811030471, "results": "53", "hashOfConfig": "31"}, {"size": 10520, "mtime": 1752575575599, "results": "54", "hashOfConfig": "31"}, {"size": 12348, "mtime": 1752820085730, "results": "55", "hashOfConfig": "31"}, {"size": 39385, "mtime": 1752731241042, "results": "56", "hashOfConfig": "31"}, {"size": 1127, "mtime": 1752819638484, "results": "57", "hashOfConfig": "31"}, {"size": 17475, "mtime": 1752829969577, "results": "58", "hashOfConfig": "31"}, {"size": 11507, "mtime": 1752833333037, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["147", "148"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js", ["149"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js", ["150", "151", "152"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPlay.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\DataNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\DetailNews.js", ["153", "154", "155"], [], {"ruleId": "156", "severity": 1, "message": "157", "line": 735, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 735, "endColumn": 18}, {"ruleId": "156", "severity": 1, "message": "157", "line": 1165, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 1165, "endColumn": 18}, {"ruleId": "156", "severity": 1, "message": "160", "line": 33, "column": 11, "nodeType": "158", "messageId": "159", "endLine": 33, "endColumn": 19}, {"ruleId": "156", "severity": 1, "message": "161", "line": 18, "column": 8, "nodeType": "158", "messageId": "159", "endLine": 18, "endColumn": 17}, {"ruleId": "156", "severity": 1, "message": "162", "line": 79, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 79, "endColumn": 20}, {"ruleId": "156", "severity": 1, "message": "163", "line": 80, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 80, "endColumn": 19}, {"ruleId": "156", "severity": 1, "message": "164", "line": 22, "column": 11, "nodeType": "158", "messageId": "159", "endLine": 22, "endColumn": 19}, {"ruleId": "156", "severity": 1, "message": "165", "line": 37, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 37, "endColumn": 17}, {"ruleId": "166", "severity": 1, "message": "167", "line": 53, "column": 6, "nodeType": "168", "endLine": 53, "endColumn": 10, "suggestions": "169"}, "no-unused-vars", "'isNewNews' is assigned a value but never used.", "Identifier", "unusedVar", "'register' is assigned a value but never used.", "'PauseIcon' is defined but never used.", "'videoHeight' is assigned a value but never used.", "'videoWidth' is assigned a value but never used.", "'HomeIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchNewsDetail' and 'incrementViews'. Either include them or remove the dependency array.", "ArrayExpression", ["170"], {"desc": "171", "fix": "172"}, "Update the dependencies array to be: [fetchNewsDetail, id, incrementViews]", {"range": "173", "text": "174"}, [1239, 1243], "[fetchNewsDetail, id, incrementViews]"]