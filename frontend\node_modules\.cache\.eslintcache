[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js": "20", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js": "21", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js": "22", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js": "23", "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js": "24", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js": "25", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js": "26", "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js": "27", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPlay.js": "28"}, {"size": 535, "mtime": 1752195971363, "results": "29", "hashOfConfig": "30"}, {"size": 3401, "mtime": 1752819945597, "results": "31", "hashOfConfig": "30"}, {"size": 362, "mtime": 1752195971670, "results": "32", "hashOfConfig": "30"}, {"size": 96195, "mtime": 1752825763552, "results": "33", "hashOfConfig": "30"}, {"size": 243, "mtime": 1752201497899, "results": "34", "hashOfConfig": "30"}, {"size": 662, "mtime": 1752201773682, "results": "35", "hashOfConfig": "30"}, {"size": 313, "mtime": 1752201773682, "results": "36", "hashOfConfig": "30"}, {"size": 822, "mtime": 1752201773678, "results": "37", "hashOfConfig": "30"}, {"size": 688, "mtime": 1752201773679, "results": "38", "hashOfConfig": "30"}, {"size": 749, "mtime": 1752201773681, "results": "39", "hashOfConfig": "30"}, {"size": 5407, "mtime": 1752367525610, "results": "40", "hashOfConfig": "30"}, {"size": 218, "mtime": 1752206025063, "results": "41", "hashOfConfig": "30"}, {"size": 4157, "mtime": 1752220101050, "results": "42", "hashOfConfig": "30"}, {"size": 18132, "mtime": 1752811518929, "results": "43", "hashOfConfig": "30"}, {"size": 0, "mtime": 1752306675896, "results": "44", "hashOfConfig": "30"}, {"size": 11682, "mtime": 1752310168602, "results": "45", "hashOfConfig": "30"}, {"size": 13390, "mtime": 1752310218662, "results": "46", "hashOfConfig": "30"}, {"size": 16930, "mtime": 1752310279691, "results": "47", "hashOfConfig": "30"}, {"size": 4742, "mtime": 1752366365655, "results": "48", "hashOfConfig": "30"}, {"size": 34535, "mtime": 1752826375663, "results": "49", "hashOfConfig": "30"}, {"size": 13804, "mtime": 1752827509245, "results": "50", "hashOfConfig": "30"}, {"size": 14630, "mtime": 1752826007221, "results": "51", "hashOfConfig": "30"}, {"size": 8407, "mtime": 1752547081826, "results": "52", "hashOfConfig": "30"}, {"size": 4610, "mtime": 1752811030471, "results": "53", "hashOfConfig": "30"}, {"size": 10520, "mtime": 1752575575599, "results": "54", "hashOfConfig": "30"}, {"size": 12348, "mtime": 1752820085730, "results": "55", "hashOfConfig": "30"}, {"size": 39385, "mtime": 1752731241042, "results": "56", "hashOfConfig": "30"}, {"size": 1127, "mtime": 1752819638484, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["142", "143"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js", ["144", "145"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js", ["146"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js", ["147", "148", "149"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPlay.js", [], [], {"ruleId": "150", "severity": 1, "message": "151", "line": 735, "column": 9, "nodeType": "152", "messageId": "153", "endLine": 735, "endColumn": 18}, {"ruleId": "150", "severity": 1, "message": "151", "line": 1165, "column": 9, "nodeType": "152", "messageId": "153", "endLine": 1165, "endColumn": 18}, {"ruleId": "150", "severity": 1, "message": "154", "line": 24, "column": 11, "nodeType": "152", "messageId": "153", "endLine": 24, "endColumn": 20}, {"ruleId": "155", "severity": 1, "message": "156", "line": 45, "column": 8, "nodeType": "157", "endLine": 45, "endColumn": 12, "suggestions": "158"}, {"ruleId": "150", "severity": 1, "message": "159", "line": 33, "column": 11, "nodeType": "152", "messageId": "153", "endLine": 33, "endColumn": 19}, {"ruleId": "150", "severity": 1, "message": "160", "line": 18, "column": 8, "nodeType": "152", "messageId": "153", "endLine": 18, "endColumn": 17}, {"ruleId": "150", "severity": 1, "message": "161", "line": 79, "column": 9, "nodeType": "152", "messageId": "153", "endLine": 79, "endColumn": 20}, {"ruleId": "150", "severity": 1, "message": "162", "line": 80, "column": 9, "nodeType": "152", "messageId": "153", "endLine": 80, "endColumn": 19}, "no-unused-vars", "'isNewNews' is assigned a value but never used.", "Identifier", "unusedVar", "'isDesktop' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchNewsDetail'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], "'register' is assigned a value but never used.", "'PauseIcon' is defined but never used.", "'videoHeight' is assigned a value but never used.", "'videoWidth' is assigned a value but never used.", {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [fetchNewsDetail, id]", {"range": "166", "text": "167"}, [1733, 1737], "[fetchNewsDetail, id]"]