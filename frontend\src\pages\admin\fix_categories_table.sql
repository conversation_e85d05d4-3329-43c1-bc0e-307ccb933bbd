-- Fix categories table structure
-- Run this SQL to fix the categories table and add missing slug column

USE react_news;

-- Check current table structure
DESCRIBE categories;

-- Add slug column if it doesn't exist
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS slug VARCHAR(255) NOT NULL DEFAULT '' AFTER name;

-- Add index for slug
ALTER TABLE categories 
ADD INDEX IF NOT EXISTS idx_slug (slug);

-- Update existing categories to have slugs if they don't have them
UPDATE categories 
SET slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(name, ' ', '-'), '&', 'and'), '.', ''), ',', ''))
WHERE slug = '' OR slug IS NULL;

-- Make sure all slugs are unique
SET @counter = 0;
UPDATE categories c1
JOIN (
    SELECT id, name, 
           ROW_NUMBER() OVER (PARTITION BY slug ORDER BY id) as rn
    FROM categories
) c2 ON c1.id = c2.id
SET c1.slug = CASE 
    WHEN c2.rn = 1 THEN c1.slug
    ELSE CONCAT(c1.slug, '-', c2.rn)
END
WHERE c2.rn > 1;

-- Show updated table structure
DESCRIBE categories;

-- Show all categories with their slugs
SELECT id, name, slug, color, created_at FROM categories ORDER BY id;

-- Show success message
SELECT 'Categories table structure fixed successfully!' as message;
