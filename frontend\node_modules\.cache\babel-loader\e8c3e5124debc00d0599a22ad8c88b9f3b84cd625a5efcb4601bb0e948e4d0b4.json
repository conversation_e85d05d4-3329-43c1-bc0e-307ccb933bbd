{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\auth\\\\ForgotPassword.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Box, Card, CardContent, TextField, Button, Typography, Alert, InputAdornment, IconButton, Avatar, Stepper, Step, StepLabel } from '@mui/material';\nimport { Lock, ArrowBack, VpnKey, CheckCircle } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ForgotPassword = () => {\n  _s();\n  const navigate = useNavigate();\n  const [step, setStep] = useState(0); // 0: paste token, 1: new password, 2: success\n  const [formData, setFormData] = useState({\n    jwtToken: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const steps = ['Paste JWT Token', 'Reset Password', 'Complete'];\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n  const handleTokenSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/verify-token', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          token: formData.jwtToken\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setSuccess('JWT Token berhasil diverifikasi. Silakan masukkan password baru Anda.');\n        setStep(1);\n      } else {\n        setError(data.message || 'JWT Token tidak valid atau sudah kadaluarsa');\n      }\n    } catch (error) {\n      console.error('Token verification error:', error);\n      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePasswordSubmit = async e => {\n    e.preventDefault();\n    if (formData.newPassword !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    if (formData.newPassword.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/reset-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          token: formData.jwtToken,\n          newPassword: formData.newPassword\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setSuccess('Password reset successfully!');\n        setStep(2);\n        setTimeout(() => {\n          navigate('/auth/login');\n        }, 3000);\n      } else {\n        setError(data.message || 'Failed to reset password');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderStepContent = () => {\n    switch (step) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleTokenSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 3,\n              borderRadius: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Paste your JWT token that you received during registration. This token is required to reset your password.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"jwtToken\",\n            label: \"JWT Token\",\n            placeholder: \"Paste JWT token yang Anda terima saat registrasi di sini...\",\n            value: formData.jwtToken,\n            onChange: handleChange,\n            required: true,\n            multiline: true,\n            rows: 4,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(VpnKey, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            disabled: loading,\n            sx: {\n              mb: 3,\n              py: {\n                xs: 1.5,\n                md: 2\n              },\n              borderRadius: 2,\n              textTransform: 'none',\n              fontSize: {\n                xs: 16,\n                md: 18\n              },\n              fontWeight: 600,\n              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n              '&:hover': {\n                boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                transform: 'translateY(-2px)'\n              },\n              '&:disabled': {\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n              },\n              transition: 'all 0.3s ease'\n            },\n            children: loading ? 'Verifying...' : 'Verify JWT Token'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handlePasswordSubmit,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"newPassword\",\n            type: \"password\",\n            label: \"New Password\",\n            placeholder: \"Masukkan password baru Anda (min. 6 karakter)\",\n            value: formData.newPassword,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"confirmPassword\",\n            type: \"password\",\n            label: \"Confirm New Password\",\n            placeholder: \"Konfirmasi password baru Anda\",\n            value: formData.confirmPassword,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            disabled: loading,\n            sx: {\n              mb: 3,\n              py: {\n                xs: 1.5,\n                md: 2\n              },\n              borderRadius: 2,\n              textTransform: 'none',\n              fontSize: {\n                xs: 16,\n                md: 18\n              },\n              fontWeight: 600,\n              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n              '&:hover': {\n                boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                transform: 'translateY(-2px)'\n              },\n              '&:disabled': {\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n              },\n              transition: 'all 0.3s ease'\n            },\n            children: loading ? 'Resetting...' : 'Reset Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            sx: {\n              color: 'success.main',\n              fontSize: 64,\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"Password Reset Successful!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Your password has been reset successfully. You will be redirected to the login page shortly.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => navigate('/auth/login'),\n            sx: {\n              py: 1.5,\n              px: 4,\n              borderRadius: 2,\n              textTransform: 'none',\n              fontSize: 16,\n              fontWeight: 600\n            },\n            children: \"Go to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      backgroundColor: '#ffffff',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      p: {\n        xs: 2,\n        sm: 3,\n        md: 4\n      },\n      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: {\n          xs: '100%',\n          sm: 450,\n          md: 480\n        },\n        width: '100%',\n        borderRadius: {\n          xs: 2,\n          md: 4\n        },\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n        border: '1px solid rgba(0, 0, 0, 0.08)',\n        backgroundColor: '#ffffff'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: {\n            xs: 3,\n            sm: 4,\n            md: 5\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: {\n              xs: 3,\n              md: 4\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => navigate('/auth/login'),\n            sx: {\n              position: 'absolute',\n              top: {\n                xs: 12,\n                md: 16\n              },\n              left: {\n                xs: 12,\n                md: 16\n              },\n              backgroundColor: 'rgba(0, 0, 0, 0.04)',\n              '&:hover': {\n                backgroundColor: 'rgba(0, 0, 0, 0.08)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: {\n                xs: 56,\n                md: 72\n              },\n              height: {\n                xs: 56,\n                md: 72\n              },\n              mx: 'auto',\n              mb: {\n                xs: 2,\n                md: 3\n              },\n              bgcolor: 'warning.main',\n              boxShadow: '0 8px 32px rgba(255, 152, 0, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(VpnKey, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"700\",\n            color: \"text.primary\",\n            gutterBottom: true,\n            sx: {\n              fontSize: {\n                xs: '1.75rem',\n                md: '2.125rem'\n              }\n            },\n            children: \"Reset Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            },\n            children: [step === 0 && \"Paste your JWT token to verify your identity\", step === 1 && \"Create a new password for your account\", step === 2 && \"Password reset completed successfully\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n          activeStep: step,\n          sx: {\n            mb: 4,\n            '& .MuiStepLabel-label': {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            }\n          },\n          children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n            children: /*#__PURE__*/_jsxDEV(StepLabel, {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)\n          }, label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3,\n            borderRadius: 2,\n            '& .MuiAlert-message': {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            }\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mb: 3,\n            borderRadius: 2,\n            '& .MuiAlert-message': {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            }\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this), renderStepContent(), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Remember your password?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/auth/login\",\n              style: {\n                textDecoration: 'none',\n                color: '#1976d2',\n                fontWeight: 600\n              },\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 5\n  }, this);\n};\n_s(ForgotPassword, \"nku/wb5jB8EaR8OACZGquPsLbBY=\", false, function () {\n  return [useNavigate];\n});\n_c = ForgotPassword;\nexport default ForgotPassword;\nvar _c;\n$RefreshReg$(_c, \"ForgotPassword\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "InputAdornment", "IconButton", "Avatar", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Lock", "ArrowBack", "VpnKey", "CheckCircle", "jsxDEV", "_jsxDEV", "ForgotPassword", "_s", "navigate", "step", "setStep", "formData", "setFormData", "jwtToken", "newPassword", "confirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "steps", "handleChange", "e", "target", "name", "value", "handleTokenSubmit", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "token", "data", "json", "message", "console", "handlePasswordSubmit", "length", "setTimeout", "renderStepContent", "onSubmit", "children", "severity", "sx", "mb", "borderRadius", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "placeholder", "onChange", "required", "multiline", "rows", "backgroundColor", "slotProps", "input", "startAdornment", "position", "color", "type", "size", "disabled", "py", "xs", "md", "textTransform", "fontSize", "fontWeight", "boxShadow", "transform", "transition", "textAlign", "gutterBottom", "onClick", "px", "minHeight", "display", "alignItems", "justifyContent", "p", "sm", "backgroundImage", "max<PERSON><PERSON><PERSON>", "width", "border", "top", "left", "height", "mx", "bgcolor", "activeStep", "map", "to", "style", "textDecoration", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/auth/ForgotPassword.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Alert,\n  InputAdornment,\n  IconButton,\n  Avatar,\n  Stepper,\n  Step,\n  StepLabel\n} from '@mui/material';\nimport {\n  Lock,\n  ArrowBack,\n  Vpn<PERSON>ey,\n  CheckCircle\n} from '@mui/icons-material';\n\nconst ForgotPassword = () => {\n  const navigate = useNavigate();\n  const [step, setStep] = useState(0); // 0: paste token, 1: new password, 2: success\n  const [formData, setFormData] = useState({\n    jwtToken: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const steps = ['Paste JWT Token', 'Reset Password', 'Complete'];\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n\n  const handleTokenSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/verify-token', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          token: formData.jwtToken\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setSuccess('JWT Token berhasil diverifikasi. Silakan masukkan password baru Anda.');\n        setStep(1);\n      } else {\n        setError(data.message || 'JWT Token tidak valid atau sudah kadaluarsa');\n      }\n    } catch (error) {\n      console.error('Token verification error:', error);\n      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handlePasswordSubmit = async (e) => {\n    e.preventDefault();\n\n    if (formData.newPassword !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (formData.newPassword.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/reset-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          token: formData.jwtToken,\n          newPassword: formData.newPassword\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setSuccess('Password reset successfully!');\n        setStep(2);\n        setTimeout(() => {\n          navigate('/auth/login');\n        }, 3000);\n      } else {\n        setError(data.message || 'Failed to reset password');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 0:\n        return (\n          <form onSubmit={handleTokenSubmit}>\n            <Alert severity=\"info\" sx={{ mb: 3, borderRadius: 2 }}>\n              <Typography variant=\"body2\">\n                Paste your JWT token that you received during registration. This token is required to reset your password.\n              </Typography>\n            </Alert>\n\n            <TextField\n              fullWidth\n              name=\"jwtToken\"\n              label=\"JWT Token\"\n              placeholder=\"Paste JWT token yang Anda terima saat registrasi di sini...\"\n              value={formData.jwtToken}\n              onChange={handleChange}\n              required\n              multiline\n              rows={4}\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <VpnKey color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mb: 3,\n                py: { xs: 1.5, md: 2 },\n                borderRadius: 2,\n                textTransform: 'none',\n                fontSize: { xs: 16, md: 18 },\n                fontWeight: 600,\n                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n                '&:hover': {\n                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                  transform: 'translateY(-2px)'\n                },\n                '&:disabled': {\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n                },\n                transition: 'all 0.3s ease'\n              }}\n            >\n              {loading ? 'Verifying...' : 'Verify JWT Token'}\n            </Button>\n          </form>\n        );\n\n      case 1:\n        return (\n          <form onSubmit={handlePasswordSubmit}>\n            <TextField\n              fullWidth\n              name=\"newPassword\"\n              type=\"password\"\n              label=\"New Password\"\n              placeholder=\"Masukkan password baru Anda (min. 6 karakter)\"\n              value={formData.newPassword}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Lock color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n            <TextField\n              fullWidth\n              name=\"confirmPassword\"\n              type=\"password\"\n              label=\"Confirm New Password\"\n              placeholder=\"Konfirmasi password baru Anda\"\n              value={formData.confirmPassword}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Lock color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mb: 3,\n                py: { xs: 1.5, md: 2 },\n                borderRadius: 2,\n                textTransform: 'none',\n                fontSize: { xs: 16, md: 18 },\n                fontWeight: 600,\n                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n                '&:hover': {\n                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                  transform: 'translateY(-2px)'\n                },\n                '&:disabled': {\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n                },\n                transition: 'all 0.3s ease'\n              }}\n            >\n              {loading ? 'Resetting...' : 'Reset Password'}\n            </Button>\n          </form>\n        );\n\n      case 2:\n        return (\n          <Box sx={{ textAlign: 'center' }}>\n            <CheckCircle sx={{ color: 'success.main', fontSize: 64, mb: 2 }} />\n            <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom>\n              Password Reset Successful!\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Your password has been reset successfully. You will be redirected to the login page shortly.\n            </Typography>\n            <Button\n              variant=\"contained\"\n              onClick={() => navigate('/auth/login')}\n              sx={{\n                py: 1.5,\n                px: 4,\n                borderRadius: 2,\n                textTransform: 'none',\n                fontSize: 16,\n                fontWeight: 600\n              }}\n            >\n              Go to Login\n            </Button>\n          </Box>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Box sx={{\n      minHeight: '100vh',\n      backgroundColor: '#ffffff',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      p: { xs: 2, sm: 3, md: 4 },\n      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'\n    }}>\n      <Card sx={{\n        maxWidth: { xs: '100%', sm: 450, md: 480 },\n        width: '100%',\n        borderRadius: { xs: 2, md: 4 },\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n        border: '1px solid rgba(0, 0, 0, 0.08)',\n        backgroundColor: '#ffffff'\n      }}>\n        <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>\n          {/* Header */}\n          <Box sx={{ textAlign: 'center', mb: { xs: 3, md: 4 } }}>\n            <IconButton\n              onClick={() => navigate('/auth/login')}\n              sx={{\n                position: 'absolute',\n                top: { xs: 12, md: 16 },\n                left: { xs: 12, md: 16 },\n                backgroundColor: 'rgba(0, 0, 0, 0.04)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.08)'\n                }\n              }}\n            >\n              <ArrowBack />\n            </IconButton>\n\n            <Avatar sx={{\n              width: { xs: 56, md: 72 },\n              height: { xs: 56, md: 72 },\n              mx: 'auto',\n              mb: { xs: 2, md: 3 },\n              bgcolor: 'warning.main',\n              boxShadow: '0 8px 32px rgba(255, 152, 0, 0.3)'\n            }}>\n              <VpnKey fontSize=\"large\" />\n            </Avatar>\n\n            <Typography\n              variant=\"h4\"\n              fontWeight=\"700\"\n              color=\"text.primary\"\n              gutterBottom\n              sx={{ fontSize: { xs: '1.75rem', md: '2.125rem' } }}\n            >\n              Reset Password\n            </Typography>\n            <Typography\n              variant=\"body1\"\n              color=\"text.secondary\"\n              sx={{ fontSize: { xs: '0.875rem', md: '1rem' } }}\n            >\n              {step === 0 && \"Paste your JWT token to verify your identity\"}\n              {step === 1 && \"Create a new password for your account\"}\n              {step === 2 && \"Password reset completed successfully\"}\n            </Typography>\n          </Box>\n\n          {/* Progress Stepper */}\n          <Stepper\n            activeStep={step}\n            sx={{\n              mb: 4,\n              '& .MuiStepLabel-label': {\n                fontSize: { xs: '0.875rem', md: '1rem' }\n              }\n            }}\n          >\n            {steps.map((label) => (\n              <Step key={label}>\n                <StepLabel>{label}</StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n\n          {/* Error/Success Alert */}\n          {error && (\n            <Alert\n              severity=\"error\"\n              sx={{\n                mb: 3,\n                borderRadius: 2,\n                '& .MuiAlert-message': {\n                  fontSize: { xs: '0.875rem', md: '1rem' }\n                }\n              }}\n            >\n              {error}\n            </Alert>\n          )}\n\n          {success && (\n            <Alert\n              severity=\"success\"\n              sx={{\n                mb: 3,\n                borderRadius: 2,\n                '& .MuiAlert-message': {\n                  fontSize: { xs: '0.875rem', md: '1rem' }\n                }\n              }}\n            >\n              {success}\n            </Alert>\n          )}\n\n          {/* Step Content */}\n          {renderStepContent()}\n\n          {/* Back to Login */}\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Remember your password?{' '}\n              <Link \n                to=\"/auth/login\" \n                style={{ \n                  textDecoration: 'none',\n                  color: '#1976d2',\n                  fontWeight: 600\n                }}\n              >\n                Sign In\n              </Link>\n            </Typography>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default ForgotPassword;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,QACJ,eAAe;AACtB,SACEC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,WAAW,QACN,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC;IACvC6B,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMsC,KAAK,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;EAE/D,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFR,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMS,iBAAiB,GAAG,MAAOJ,CAAC,IAAK;IACrCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6C,EAAE;QAC1EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE1B,QAAQ,CAACE;QAClB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMyB,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAAClB,OAAO,EAAE;QAChBC,UAAU,CAAC,uEAAuE,CAAC;QACnFX,OAAO,CAAC,CAAC,CAAC;MACZ,CAAC,MAAM;QACLS,QAAQ,CAACmB,IAAI,CAACE,OAAO,IAAI,6CAA6C,CAAC;MACzE;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,oEAAoE,CAAC;IAChF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMyB,oBAAoB,GAAG,MAAOlB,CAAC,IAAK;IACxCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAIlB,QAAQ,CAACG,WAAW,KAAKH,QAAQ,CAACI,eAAe,EAAE;MACrDI,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAIR,QAAQ,CAACG,WAAW,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACnCxB,QAAQ,CAAC,6CAA6C,CAAC;MACvD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE1B,QAAQ,CAACE,QAAQ;UACxBC,WAAW,EAAEH,QAAQ,CAACG;QACxB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMwB,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAAClB,OAAO,EAAE;QAChBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CX,OAAO,CAAC,CAAC,CAAC;QACVkC,UAAU,CAAC,MAAM;UACfpC,QAAQ,CAAC,aAAa,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLW,QAAQ,CAACmB,IAAI,CAACE,OAAO,IAAI,0BAA0B,CAAC;MACtD;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,QAAQ,CAAC,kCAAkC,CAAC;IAC9C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQpC,IAAI;MACV,KAAK,CAAC;QACJ,oBACEJ,OAAA;UAAMyC,QAAQ,EAAElB,iBAAkB;UAAAmB,QAAA,gBAChC1C,OAAA,CAACZ,KAAK;YAACuD,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAJ,QAAA,eACpD1C,OAAA,CAACb,UAAU;cAAC4D,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAERnD,OAAA,CAACf,SAAS;YACRmE,SAAS;YACT/B,IAAI,EAAC,UAAU;YACfgC,KAAK,EAAC,WAAW;YACjBC,WAAW,EAAC,6DAA6D;YACzEhC,KAAK,EAAEhB,QAAQ,CAACE,QAAS;YACzB+C,QAAQ,EAAErC,YAAa;YACvBsC,QAAQ;YACRC,SAAS;YACTC,IAAI,EAAE,CAAE;YACRd,EAAE,EAAE;cACFC,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BC,YAAY,EAAE,CAAC;gBACfa,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YAEFC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZ9D,OAAA,CAACX,cAAc;kBAAC0E,QAAQ,EAAC,OAAO;kBAAArB,QAAA,eAC9B1C,OAAA,CAACH,MAAM;oBAACmE,KAAK,EAAC;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnD,OAAA,CAACd,MAAM;YACL+E,IAAI,EAAC,QAAQ;YACbb,SAAS;YACTL,OAAO,EAAC,WAAW;YACnBmB,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAExD,OAAQ;YAClBiC,EAAE,EAAE;cACFC,EAAE,EAAE,CAAC;cACLuB,EAAE,EAAE;gBAAEC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACtBxB,YAAY,EAAE,CAAC;cACfyB,aAAa,EAAE,MAAM;cACrBC,QAAQ,EAAE;gBAAEH,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAC5BG,UAAU,EAAE,GAAG;cACfC,SAAS,EAAE,oCAAoC;cAC/C,SAAS,EAAE;gBACTA,SAAS,EAAE,qCAAqC;gBAChDC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZD,SAAS,EAAE;cACb,CAAC;cACDE,UAAU,EAAE;YACd,CAAE;YAAAlC,QAAA,EAED/B,OAAO,GAAG,cAAc,GAAG;UAAkB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGX,KAAK,CAAC;QACJ,oBACEnD,OAAA;UAAMyC,QAAQ,EAAEJ,oBAAqB;UAAAK,QAAA,gBACnC1C,OAAA,CAACf,SAAS;YACRmE,SAAS;YACT/B,IAAI,EAAC,aAAa;YAClB4C,IAAI,EAAC,UAAU;YACfZ,KAAK,EAAC,cAAc;YACpBC,WAAW,EAAC,+CAA+C;YAC3DhC,KAAK,EAAEhB,QAAQ,CAACG,WAAY;YAC5B8C,QAAQ,EAAErC,YAAa;YACvBsC,QAAQ;YACRZ,EAAE,EAAE;cACFC,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BC,YAAY,EAAE,CAAC;gBACfa,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZ9D,OAAA,CAACX,cAAc;kBAAC0E,QAAQ,EAAC,OAAO;kBAAArB,QAAA,eAC9B1C,OAAA,CAACL,IAAI;oBAACqE,KAAK,EAAC;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnD,OAAA,CAACf,SAAS;YACRmE,SAAS;YACT/B,IAAI,EAAC,iBAAiB;YACtB4C,IAAI,EAAC,UAAU;YACfZ,KAAK,EAAC,sBAAsB;YAC5BC,WAAW,EAAC,+BAA+B;YAC3ChC,KAAK,EAAEhB,QAAQ,CAACI,eAAgB;YAChC6C,QAAQ,EAAErC,YAAa;YACvBsC,QAAQ;YACRZ,EAAE,EAAE;cACFC,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BC,YAAY,EAAE,CAAC;gBACfa,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZ9D,OAAA,CAACX,cAAc;kBAAC0E,QAAQ,EAAC,OAAO;kBAAArB,QAAA,eAC9B1C,OAAA,CAACL,IAAI;oBAACqE,KAAK,EAAC;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnD,OAAA,CAACd,MAAM;YACL+E,IAAI,EAAC,QAAQ;YACbb,SAAS;YACTL,OAAO,EAAC,WAAW;YACnBmB,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAExD,OAAQ;YAClBiC,EAAE,EAAE;cACFC,EAAE,EAAE,CAAC;cACLuB,EAAE,EAAE;gBAAEC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACtBxB,YAAY,EAAE,CAAC;cACfyB,aAAa,EAAE,MAAM;cACrBC,QAAQ,EAAE;gBAAEH,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAC5BG,UAAU,EAAE,GAAG;cACfC,SAAS,EAAE,oCAAoC;cAC/C,SAAS,EAAE;gBACTA,SAAS,EAAE,qCAAqC;gBAChDC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZD,SAAS,EAAE;cACb,CAAC;cACDE,UAAU,EAAE;YACd,CAAE;YAAAlC,QAAA,EAED/B,OAAO,GAAG,cAAc,GAAG;UAAgB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGX,KAAK,CAAC;QACJ,oBACEnD,OAAA,CAAClB,GAAG;UAAC8D,EAAE,EAAE;YAAEiC,SAAS,EAAE;UAAS,CAAE;UAAAnC,QAAA,gBAC/B1C,OAAA,CAACF,WAAW;YAAC8C,EAAE,EAAE;cAAEoB,KAAK,EAAE,cAAc;cAAEQ,QAAQ,EAAE,EAAE;cAAE3B,EAAE,EAAE;YAAE;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnEnD,OAAA,CAACb,UAAU;YAAC4D,OAAO,EAAC,IAAI;YAAC0B,UAAU,EAAC,MAAM;YAACK,YAAY;YAAApC,QAAA,EAAC;UAExD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACb,UAAU;YAAC4D,OAAO,EAAC,OAAO;YAACiB,KAAK,EAAC,gBAAgB;YAACpB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,EAAC;UAElE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACd,MAAM;YACL6D,OAAO,EAAC,WAAW;YACnBgC,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC,aAAa,CAAE;YACvCyC,EAAE,EAAE;cACFwB,EAAE,EAAE,GAAG;cACPY,EAAE,EAAE,CAAC;cACLlC,YAAY,EAAE,CAAC;cACfyB,aAAa,EAAE,MAAM;cACrBC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE;YACd,CAAE;YAAA/B,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEnD,OAAA,CAAClB,GAAG;IAAC8D,EAAE,EAAE;MACPqC,SAAS,EAAE,OAAO;MAClBtB,eAAe,EAAE,SAAS;MAC1BuB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,CAAC,EAAE;QAAEhB,EAAE,EAAE,CAAC;QAAEiB,EAAE,EAAE,CAAC;QAAEhB,EAAE,EAAE;MAAE,CAAC;MAC1BiB,eAAe,EAAE;IACnB,CAAE;IAAA7C,QAAA,eACA1C,OAAA,CAACjB,IAAI;MAAC6D,EAAE,EAAE;QACR4C,QAAQ,EAAE;UAAEnB,EAAE,EAAE,MAAM;UAAEiB,EAAE,EAAE,GAAG;UAAEhB,EAAE,EAAE;QAAI,CAAC;QAC1CmB,KAAK,EAAE,MAAM;QACb3C,YAAY,EAAE;UAAEuB,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC9BI,SAAS,EAAE,sEAAsE;QACjFgB,MAAM,EAAE,+BAA+B;QACvC/B,eAAe,EAAE;MACnB,CAAE;MAAAjB,QAAA,eACA1C,OAAA,CAAChB,WAAW;QAAC4D,EAAE,EAAE;UAAEyC,CAAC,EAAE;YAAEhB,EAAE,EAAE,CAAC;YAAEiB,EAAE,EAAE,CAAC;YAAEhB,EAAE,EAAE;UAAE;QAAE,CAAE;QAAA5B,QAAA,gBAE9C1C,OAAA,CAAClB,GAAG;UAAC8D,EAAE,EAAE;YAAEiC,SAAS,EAAE,QAAQ;YAAEhC,EAAE,EAAE;cAAEwB,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE;UAAE,CAAE;UAAA5B,QAAA,gBACrD1C,OAAA,CAACV,UAAU;YACTyF,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC,aAAa,CAAE;YACvCyC,EAAE,EAAE;cACFmB,QAAQ,EAAE,UAAU;cACpB4B,GAAG,EAAE;gBAAEtB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACvBsB,IAAI,EAAE;gBAAEvB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACxBX,eAAe,EAAE,qBAAqB;cACtC,SAAS,EAAE;gBACTA,eAAe,EAAE;cACnB;YACF,CAAE;YAAAjB,QAAA,eAEF1C,OAAA,CAACJ,SAAS;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEbnD,OAAA,CAACT,MAAM;YAACqD,EAAE,EAAE;cACV6C,KAAK,EAAE;gBAAEpB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACzBuB,MAAM,EAAE;gBAAExB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAC1BwB,EAAE,EAAE,MAAM;cACVjD,EAAE,EAAE;gBAAEwB,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACpByB,OAAO,EAAE,cAAc;cACvBrB,SAAS,EAAE;YACb,CAAE;YAAAhC,QAAA,eACA1C,OAAA,CAACH,MAAM;cAAC2E,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAETnD,OAAA,CAACb,UAAU;YACT4D,OAAO,EAAC,IAAI;YACZ0B,UAAU,EAAC,KAAK;YAChBT,KAAK,EAAC,cAAc;YACpBc,YAAY;YACZlC,EAAE,EAAE;cAAE4B,QAAQ,EAAE;gBAAEH,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE;cAAW;YAAE,CAAE;YAAA5B,QAAA,EACrD;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACb,UAAU;YACT4D,OAAO,EAAC,OAAO;YACfiB,KAAK,EAAC,gBAAgB;YACtBpB,EAAE,EAAE;cAAE4B,QAAQ,EAAE;gBAAEH,EAAE,EAAE,UAAU;gBAAEC,EAAE,EAAE;cAAO;YAAE,CAAE;YAAA5B,QAAA,GAEhDtC,IAAI,KAAK,CAAC,IAAI,8CAA8C,EAC5DA,IAAI,KAAK,CAAC,IAAI,wCAAwC,EACtDA,IAAI,KAAK,CAAC,IAAI,uCAAuC;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNnD,OAAA,CAACR,OAAO;UACNwG,UAAU,EAAE5F,IAAK;UACjBwC,EAAE,EAAE;YACFC,EAAE,EAAE,CAAC;YACL,uBAAuB,EAAE;cACvB2B,QAAQ,EAAE;gBAAEH,EAAE,EAAE,UAAU;gBAAEC,EAAE,EAAE;cAAO;YACzC;UACF,CAAE;UAAA5B,QAAA,EAEDzB,KAAK,CAACgF,GAAG,CAAE5C,KAAK,iBACfrD,OAAA,CAACP,IAAI;YAAAiD,QAAA,eACH1C,OAAA,CAACN,SAAS;cAAAgD,QAAA,EAAEW;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADrBE,KAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EAGTtC,KAAK,iBACJb,OAAA,CAACZ,KAAK;UACJuD,QAAQ,EAAC,OAAO;UAChBC,EAAE,EAAE;YACFC,EAAE,EAAE,CAAC;YACLC,YAAY,EAAE,CAAC;YACf,qBAAqB,EAAE;cACrB0B,QAAQ,EAAE;gBAAEH,EAAE,EAAE,UAAU;gBAAEC,EAAE,EAAE;cAAO;YACzC;UACF,CAAE;UAAA5B,QAAA,EAED7B;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEApC,OAAO,iBACNf,OAAA,CAACZ,KAAK;UACJuD,QAAQ,EAAC,SAAS;UAClBC,EAAE,EAAE;YACFC,EAAE,EAAE,CAAC;YACLC,YAAY,EAAE,CAAC;YACf,qBAAqB,EAAE;cACrB0B,QAAQ,EAAE;gBAAEH,EAAE,EAAE,UAAU;gBAAEC,EAAE,EAAE;cAAO;YACzC;UACF,CAAE;UAAA5B,QAAA,EAED3B;QAAO;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,EAGAX,iBAAiB,CAAC,CAAC,eAGpBxC,OAAA,CAAClB,GAAG;UAAC8D,EAAE,EAAE;YAAEiC,SAAS,EAAE;UAAS,CAAE;UAAAnC,QAAA,eAC/B1C,OAAA,CAACb,UAAU;YAAC4D,OAAO,EAAC,OAAO;YAACiB,KAAK,EAAC,gBAAgB;YAAAtB,QAAA,GAAC,yBAC1B,EAAC,GAAG,eAC3B1C,OAAA,CAACnB,IAAI;cACHqH,EAAE,EAAC,aAAa;cAChBC,KAAK,EAAE;gBACLC,cAAc,EAAE,MAAM;gBACtBpC,KAAK,EAAE,SAAS;gBAChBS,UAAU,EAAE;cACd,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjD,EAAA,CA1bID,cAAc;EAAA,QACDrB,WAAW;AAAA;AAAAyH,EAAA,GADxBpG,cAAc;AA4bpB,eAAeA,cAAc;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}