<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/connect.php';

// Test add user functionality
try {
    echo "Testing add user functionality...\n";
    
    // Test database connection
    $pdo = getConnection();
    echo "Database connection: OK\n";
    
    // Test table creation
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'user') DEFAULT 'user',
            email_verified_at TIMESTAMP NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            last_login_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "Table creation: OK\n";
    
    // Test data
    $name = 'Test User';
    $email = '<EMAIL>';
    $password = 'password123';
    $role = 'user';
    $status = 'active';
    
    // Check if email exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        echo "Email already exists, deleting...\n";
        $stmt = $pdo->prepare("DELETE FROM users WHERE email = ?");
        $stmt->execute([$email]);
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    echo "Password hashing: OK\n";
    
    // Insert user
    $stmt = $pdo->prepare("
        INSERT INTO users (name, email, password, role, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
    ");
    $result = $stmt->execute([$name, $email, $hashedPassword, $role, $status]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "User inserted successfully with ID: " . $userId . "\n";
        
        // Clean up
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        echo "Test user deleted\n";
        
        echo "SUCCESS: Add user functionality works!\n";
    } else {
        echo "ERROR: Failed to insert user\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
