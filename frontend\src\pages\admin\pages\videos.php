<?php
// Suppress PHP errors to prevent breaking JavaScript
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

require_once __DIR__ . '/../config.php';

// Get database connection
$pdo = getConnection();

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

// Handle delete video
if (isset($_POST['delete_video'])) {
    $videoId = $_POST['video_id'];

    try {
        // Get video title before deletion for notification
        $stmt = $pdo->prepare("SELECT title FROM videos WHERE id = ?");
        $stmt->execute([$videoId]);
        $video = $stmt->fetch(PDO::FETCH_ASSOC);
        $videoTitle = $video ? $video['title'] : 'Video';

        // Delete video and related data (cascading)
        $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
        $stmt->execute([$videoId]);

        // Add notification to database
        if (function_exists('addNotification')) {
            addNotification(
                'Video Berhasil Dihapus',
                "Video \"$videoTitle\" berhasil dihapus dari sistem",
                'delete',
                'video',
                'success',
                $videoId
            );
        }

        $notification = ['message' => 'Video berhasil dihapus!', 'type' => 'success'];
    } catch (Exception $e) {
        $notification = ['message' => 'Gagal menghapus video: ' . $e->getMessage(), 'type' => 'error'];
    }
}

// Handle delete selected videos
if (isset($_POST['delete_selected_videos']) && isset($_POST['selected_video_ids'])) {
    $selectedIds = $_POST['selected_video_ids'];
    $deletedCount = 0;

    try {
        $pdo->beginTransaction();

        foreach ($selectedIds as $videoId) {
            // Get video info first (for file cleanup)
            $stmt = $pdo->prepare("SELECT thumbnail, video_type FROM videos WHERE id = ?");
            $stmt->execute([$videoId]);
            $video = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($video) {
                // Delete video record
                $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
                if ($stmt->execute([$videoId])) {
                    $deletedCount++;

                    // Note: video_base64 data is automatically deleted with the record
                    // No need to clean up physical video files since we store in database

                    // Clean up thumbnail file if it's a local file
                    if ($video['thumbnail'] && !str_starts_with($video['thumbnail'], 'http')) {
                        $thumbnailPath = __DIR__ . '/../../../uploads/' . basename($video['thumbnail']);
                        if (file_exists($thumbnailPath)) {
                            unlink($thumbnailPath);
                        }
                    }
                }
            }
        }

        $pdo->commit();

        // Add notification to database
        if ($deletedCount > 0 && function_exists('addNotification')) {
            addNotification(
                'Video Berhasil Dihapus',
                "$deletedCount video berhasil dihapus secara bersamaan",
                'delete',
                'video',
                'success'
            );
        }

        $notification = ['message' => "$deletedCount video berhasil dihapus!", 'type' => 'success'];
    } catch (Exception $e) {
        $pdo->rollback();
        $notification = ['message' => 'Gagal menghapus video: ' . $e->getMessage(), 'type' => 'error'];
    }
}

// Get videos with pagination
$page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$limit = 5; // Changed to 5 videos per page
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$category = isset($_GET['category']) ? $_GET['category'] : '';

$whereClause = "WHERE 1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (title LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category)) {
    $whereClause .= " AND category = ?";
    $params[] = $category;
}

// Get total count
$countStmt = $pdo->prepare("SELECT COUNT(*) FROM videos $whereClause");
$countStmt->execute($params);
$totalVideos = (int)$countStmt->fetchColumn();
$totalPages = $totalVideos > 0 ? ceil($totalVideos / $limit) : 1;

// Get videos
$stmt = $pdo->prepare("SELECT * FROM videos $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
$stmt->execute($params);
$videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get categories for filter
$categoryStmt = $pdo->prepare("SELECT DISTINCT category FROM videos WHERE category IS NOT NULL ORDER BY category");
$categoryStmt->execute();
$categories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
?>

<div class="space-y-6">
    <!-- Header with Add Button -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Kelola Video</h1>
            <p class="text-gray-600">Kelola video berita dan konten multimedia</p>
        </div>
        <div class="flex gap-2">
            <!-- Delete Selected Button (Hidden by default) -->
            <button id="deleteSelectedBtn" onclick="deleteSelected()"
                    class="hidden bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                <i class="fas fa-trash"></i>
                Hapus Terpilih (<span id="selectedCount">0</span>)
            </button>

            <a href="?page=add-video" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                <i class="fas fa-plus"></i>
                Tambah Video
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <form method="GET" class="flex flex-col sm:flex-row gap-4">
            <input type="hidden" name="page" value="videos">
            
            <div class="flex-1">
                <input 
                    type="text" 
                    name="search" 
                    value="<?php echo htmlspecialchars($search); ?>"
                    placeholder="Cari video..." 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
            </div>
            
            <div class="sm:w-48">
                <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Semua Kategori</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo htmlspecialchars($cat); ?>" <?php echo $category === $cat ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($cat); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                <i class="fas fa-search"></i>
                Cari
            </button>
            
            <?php if (!empty($search) || !empty($category)): ?>
                <a href="?page=videos" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-times"></i>
                    Reset
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Videos Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- Table Header Info -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                <h3 class="text-lg font-medium text-gray-900">Daftar Video</h3>
                <div class="text-sm text-gray-600">
                    <span class="font-medium"><?php echo count($videos); ?></span> dari
                    <span class="font-medium"><?php echo $totalVideos; ?></span> video
                    <?php if (!empty($search) || !empty($category)): ?>
                        <span class="text-blue-600">(difilter)</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-left">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Video</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipe</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategori</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statistik</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($videos)): ?>
                        <tr>
                            <td colspan="9" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-video text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg">Belum ada video</p>
                                <p class="text-sm">Mulai dengan menambahkan video pertama Anda</p>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($videos as $index => $video): ?>
                            <tr class="hover:bg-gray-50">
                                <!-- Checkbox -->
                                <td class="px-3 py-4">
                                    <input type="checkbox" name="selected_videos[]" value="<?php echo $video['id']; ?>"
                                           onchange="updateSelectedCount()"
                                           class="video-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>

                                <!-- Nomor -->
                                <td class="px-3 py-4 text-sm font-medium text-gray-900">
                                    <?php echo $offset + $index + 1; ?>
                                </td>

                                <!-- Video Info -->
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-16 w-24">
                                            <?php
                                            $video_type = $video['video_type'] ?? 'youtube';
                                            if ($video_type === 'upload'):
                                                // Handle uploaded video preview from database
                                                $hasVideoData = !empty($video['video_base64']);
                                                $videoFormat = $video['video_format'] ?? 'mp4';
                                            ?>
                                                <div class="relative h-16 w-24 rounded-lg overflow-hidden bg-gray-200 group cursor-pointer"
                                                     onclick="previewVideo(<?php echo $video['id']; ?>, 'upload')">
                                                    <?php if ($hasVideoData): ?>
                                                        <!-- Video preview container -->
                                                        <div class="h-full w-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center relative">
                                                            <!-- Video icon -->
                                                            <i class="fas fa-play-circle text-blue-600 text-2xl"></i>

                                                            <!-- Video info overlay -->
                                                            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs p-1">
                                                                <div class="flex justify-between items-center">
                                                                    <span class="uppercase"><?php echo htmlspecialchars($videoFormat); ?></span>
                                                                    <span><?php echo htmlspecialchars($video['duration'] ?? '00:00'); ?></span>
                                                                </div>
                                                            </div>

                                                            <!-- Hover effect -->
                                                            <div class="absolute inset-0 bg-blue-600 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                                                                <i class="fas fa-eye text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"></i>
                                                            </div>
                                                        </div>
                                                    <?php else: ?>
                                                        <!-- No video data indicator -->
                                                        <div class="h-full w-full bg-red-100 flex items-center justify-center text-red-400">
                                                            <i class="fas fa-exclamation-triangle text-xl" title="Data video tidak ditemukan"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <?php if (!empty($video['youtube_id'])): ?>
                                                    <img
                                                        src="https://img.youtube.com/vi/<?php echo htmlspecialchars($video['youtube_id']); ?>/mqdefault.jpg"
                                                        alt="YouTube Thumbnail"
                                                        class="h-16 w-24 object-cover rounded-lg"
                                                        onerror="this.parentElement.innerHTML='<div class=\'h-16 w-24 bg-red-100 rounded-lg flex items-center justify-center text-red-400\'><i class=\'fab fa-youtube text-xl\'></i></div>'"
                                                    >
                                                <?php else: ?>
                                                    <div class="h-16 w-24 bg-gray-200 rounded-lg flex items-center justify-center text-gray-400">
                                                        <i class="fas fa-image text-xl"></i>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 line-clamp-2">
                                                <?php echo htmlspecialchars($video['title']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500 line-clamp-1 mt-1">
                                                <?php echo htmlspecialchars(substr($video['description'], 0, 100)) . '...'; ?>
                                            </div>
                                            <div class="text-xs text-gray-400 mt-1 flex items-center space-x-3">
                                                <span><i class="fas fa-clock mr-1"></i><?php echo htmlspecialchars($video['duration']); ?></span>
                                                <?php if ($video_type === 'upload' && !empty($video['file_size'])): ?>
                                                    <span><i class="fas fa-hdd mr-1"></i><?php echo formatFileSize($video['file_size']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $video_type = $video['video_type'] ?? 'youtube';
                                    $type_color = $video_type === 'upload' ? 'bg-purple-100 text-purple-800' : 'bg-red-100 text-red-800';
                                    $type_icon = $video_type === 'upload' ? 'fas fa-upload' : 'fab fa-youtube';
                                    $type_text = $video_type === 'upload' ? 'Upload' : 'YouTube';
                                    ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $type_color; ?>">
                                        <i class="<?php echo $type_icon; ?> mr-1"></i>
                                        <?php echo $type_text; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo htmlspecialchars($video['category']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $video['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                        <i class="fas fa-circle text-xs mr-1"></i>
                                        <?php echo $video['status'] === 'published' ? 'Published' : 'Draft'; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="space-y-1">
                                        <div><i class="fas fa-eye text-gray-400 mr-1"></i> <?php echo number_format($video['views']); ?></div>
                                        <div><i class="fas fa-heart text-red-400 mr-1"></i> <?php echo number_format($video['likes']); ?></div>
                                        <div><i class="fas fa-comment text-blue-400 mr-1"></i> <?php echo number_format($video['comments_count']); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('d M Y', strtotime($video['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="?page=edit-video&id=<?php echo $video['id']; ?>" 
                                           class="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php
                                        $video_type = $video['video_type'] ?? 'youtube';
                                        if ($video_type === 'upload' && !empty($video['video_path'])):
                                        ?>
                                            <a href="../user/video.php?id=<?php echo $video['id']; ?>"
                                               target="_blank"
                                               class="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                                               title="Lihat Video">
                                                <i class="fas fa-play"></i>
                                            </a>
                                        <?php else: ?>
                                            <a href="<?php echo htmlspecialchars($video['youtube_url']); ?>"
                                               target="_blank"
                                               class="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                                               title="Lihat Video">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                        <button class="delete-video-btn text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                                                data-video-id="<?php echo $video['id']; ?>"
                                                data-video-title="<?php echo htmlspecialchars($video['title'], ENT_QUOTES, 'UTF-8'); ?>"
                                                title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <!-- Mobile Pagination -->
                <div class="sm:hidden">
                    <div class="text-center mb-3">
                        <p class="text-sm text-gray-700">
                            Halaman <?php echo $page; ?> dari <?php echo $totalPages; ?>
                            (<?php echo $totalVideos; ?> video total)
                        </p>
                    </div>
                    <div class="flex justify-between">
                        <?php if ($page > 1): ?>
                            <a href="?page=videos&p=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-angle-left mr-1"></i> Sebelumnya
                            </a>
                        <?php else: ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                <i class="fas fa-angle-left mr-1"></i> Sebelumnya
                            </span>
                        <?php endif; ?>

                        <?php if ($page < $totalPages): ?>
                            <a href="?page=videos&p=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Selanjutnya <i class="fas fa-angle-right ml-1"></i>
                            </a>
                        <?php else: ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                Selanjutnya <i class="fas fa-angle-right ml-1"></i>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            <?php if ($totalVideos > 0): ?>
                                Menampilkan <span class="font-medium"><?php echo $offset + 1; ?></span> sampai
                                <span class="font-medium"><?php echo min($offset + $limit, $totalVideos); ?></span> dari
                                <span class="font-medium"><?php echo $totalVideos; ?></span> video
                                <span class="text-gray-500">(<?php echo $limit; ?> per halaman)</span>
                            <?php else: ?>
                                Tidak ada video ditemukan
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <!-- First Page -->
                            <?php if ($page > 1): ?>
                                <a href="?page=videos&p=1&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            <?php endif; ?>

                            <!-- Previous Page -->
                            <?php if ($page > 1): ?>
                                <a href="?page=videos&p=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <a href="?page=videos&p=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?php echo $i === $page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <!-- Next Page -->
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=videos&p=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            <?php endif; ?>

                            <!-- Last Page -->
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=videos&p=<?php echo $totalPages; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal delete sudah diganti dengan SweetAlert -->

<!-- Delete Selected Videos Modal -->
<div id="deleteSelectedModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-2">Hapus Video Terpilih</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Apakah Anda yakin ingin menghapus <span id="selectedVideoCount">0</span> video yang dipilih?
                    Tindakan ini tidak dapat dibatalkan.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form method="POST" class="inline" id="deleteSelectedForm">
                    <input type="hidden" name="delete_selected_videos" value="1">
                    <div id="selectedVideoIds"></div>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 mr-2">
                        Hapus Semua
                    </button>
                </form>
                <button onclick="closeDeleteSelectedModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Batal
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

async function deleteVideo(id, title) {
    const result = await Swal.fire({
        title: 'Hapus Video',
        text: `Apakah Anda yakin ingin menghapus video "${title}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    });

    if (result.isConfirmed) {
        try {
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus video',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            const formData = new FormData();
            formData.append('action', 'delete_video');
            formData.append('id', id);

            const response = await fetch(API_BASE, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: data.message || 'Gagal menghapus video',
                    icon: 'error'
                });
            }
        } catch (error) {
            console.error('Error:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menghapus video',
                icon: 'error'
            });
        }
    }
}

// Event delegation for delete buttons
document.addEventListener('click', function(e) {
    if (e.target.closest('.delete-video-btn')) {
        const btn = e.target.closest('.delete-video-btn');
        const videoId = btn.getAttribute('data-video-id');
        const videoTitle = btn.getAttribute('data-video-title');
        deleteVideo(videoId, videoTitle);
    }
});

// Modal functions sudah diganti dengan SweetAlert

// Checkbox functionality
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const videoCheckboxes = document.querySelectorAll('.video-checkbox');

    videoCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.video-checkbox:checked');
    const count = selectedCheckboxes.length;
    const totalCheckboxes = document.querySelectorAll('.video-checkbox').length;

    // Update selected count display
    document.getElementById('selectedCount').textContent = count;

    // Show/hide delete selected button
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    if (count > 0) {
        deleteBtn.classList.remove('hidden');
        deleteBtn.classList.add('flex');
    } else {
        deleteBtn.classList.add('hidden');
        deleteBtn.classList.remove('flex');
    }

    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAll');
    if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === totalCheckboxes) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// Handle video loading success
function handleVideoLoad(video) {
    video.style.display = 'block';
    const placeholder = video.nextElementSibling;
    if (placeholder) {
        placeholder.style.display = 'none';
    }
}

// Handle video loading error
function handleVideoError(video) {
    video.style.display = 'none';
    const placeholder = video.nextElementSibling;
    if (placeholder) {
        placeholder.style.display = 'flex';
        placeholder.innerHTML = '<i class="fas fa-exclamation-triangle text-red-400 text-xl" title="Video tidak dapat dimuat"></i>';
    }
}

function deleteSelected() {
    const selectedCheckboxes = document.querySelectorAll('.video-checkbox:checked');
    const count = selectedCheckboxes.length;

    if (count === 0) {
        alert('Pilih video yang ingin dihapus terlebih dahulu');
        return;
    }

    // Update modal content
    document.getElementById('selectedVideoCount').textContent = count;

    // Clear previous hidden inputs
    const selectedVideoIds = document.getElementById('selectedVideoIds');
    selectedVideoIds.innerHTML = '';

    // Add hidden inputs for selected video IDs
    selectedCheckboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_video_ids[]';
        hiddenInput.value = checkbox.value;
        selectedVideoIds.appendChild(hiddenInput);
    });

    // Show modal
    document.getElementById('deleteSelectedModal').classList.remove('hidden');
}

function closeDeleteSelectedModal() {
    document.getElementById('deleteSelectedModal').classList.add('hidden');
}

// Close delete selected modal when clicking outside
document.getElementById('deleteSelectedModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteSelectedModal();
    }
});

// Show notification if there's one from PHP
<?php if (isset($notification)): ?>
document.addEventListener('DOMContentLoaded', function() {
    showNotification(<?php echo json_encode($notification['message']); ?>, <?php echo json_encode($notification['type']); ?>);
});
<?php endif; ?>

</script>

<style>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
    