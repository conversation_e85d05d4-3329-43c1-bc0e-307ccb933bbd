import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Avatar,
  Dialog,
  DialogContent,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Person,
  ArrowBack,
  ContentCopy,
  CheckCircle
} from '@mui/icons-material';

const Register = () => {
  const navigate = useNavigate();
  const { register } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showTokenModal, setShowTokenModal] = useState(false);
  const [jwtToken, setJwtToken] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:5000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password
        })
      });

      const result = await response.json();

      if (result.success) {
        setJwtToken(result.token);
        setShowTokenModal(true);
        setSuccess('Registrasi berhasil! Silakan simpan JWT token untuk recovery password.');
      } else {
        setError(result.message || 'Registrasi gagal');
      }
    } catch (error) {
      console.error('Register error:', error);
      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');
    }

    setLoading(false);
  };

  const handleCopyToken = async () => {
    try {
      await navigator.clipboard.writeText(jwtToken);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy token:', err);
    }
  };

  const handleCloseModal = () => {
    setShowTokenModal(false);
    navigate('/auth/login');
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      p: { xs: 2, sm: 3, md: 4 },
      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'
    }}>
      <Card sx={{
        maxWidth: { xs: '100%', sm: 420, md: 450 },
        width: '100%',
        borderRadius: { xs: 2, md: 4 },
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
        border: '1px solid rgba(0, 0, 0, 0.08)',
        backgroundColor: '#ffffff'
      }}>
        <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 3, md: 4 } }}>
            <IconButton
              onClick={() => navigate('/')}
              sx={{
                position: 'absolute',
                top: { xs: 12, md: 16 },
                left: { xs: 12, md: 16 },
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.08)'
                }
              }}
            >
              <ArrowBack />
            </IconButton>

            <Avatar sx={{
              width: { xs: 56, md: 72 },
              height: { xs: 56, md: 72 },
              mx: 'auto',
              mb: { xs: 2, md: 3 },
              bgcolor: 'secondary.main',
              boxShadow: '0 8px 32px rgba(220, 0, 78, 0.3)'
            }}>
              <Person fontSize="large" />
            </Avatar>

            <Typography
              variant="h4"
              fontWeight="700"
              color="text.primary"
              gutterBottom
              sx={{ fontSize: { xs: '1.75rem', md: '2.125rem' } }}
            >
              Create Account
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontSize: { xs: '0.875rem', md: '1rem' } }}
            >
              Join us today and stay updated with latest news
            </Typography>
          </Box>

          {/* Error/Success Alert */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: { xs: '0.875rem', md: '1rem' }
                }
              }}
            >
              {error}
            </Alert>
          )}

          {success && (
            <Alert
              severity="success"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: { xs: '0.875rem', md: '1rem' }
                }
              }}
            >
              {success}
            </Alert>
          )}

          {/* Register Form */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              name="name"
              label="Full Name"
              placeholder="Masukkan nama lengkap Anda"
              value={formData.name}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />

            <TextField
              fullWidth
              name="email"
              type="email"
              label="Email Address"
              placeholder="Masukkan alamat email Anda"
              value={formData.email}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />

            <TextField
              fullWidth
              name="password"
              type={showPassword ? 'text' : 'password'}
              label="Password"
              placeholder="Masukkan password Anda (min. 6 karakter)"
              value={formData.password}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }
              }}
            />

            <TextField
              fullWidth
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              label="Confirm Password"
              placeholder="Konfirmasi password Anda"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff'
                  }
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{
                mb: 3,
                py: { xs: 1.5, md: 2 },
                borderRadius: 2,
                textTransform: 'none',
                fontSize: { xs: 16, md: 18 },
                fontWeight: 600,
                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',
                  transform: 'translateY(-2px)'
                },
                '&:disabled': {
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {loading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </form>

          {/* Links */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Already have an account?{' '}
              <Link 
                to="/auth/login" 
                style={{ 
                  textDecoration: 'none',
                  color: '#1976d2',
                  fontWeight: 600
                }}
              >
                Sign In
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* JWT Token Modal */}
      <Dialog
        open={showTokenModal}
        onClose={handleCloseModal}
        maxWidth="md"
        fullWidth
      >
        <Box sx={{ textAlign: 'center', p: 3, pb: 1 }}>
          <CheckCircle sx={{ color: 'success.main', fontSize: 48, mb: 2 }} />
          <Typography variant="h5" fontWeight="bold" component="h2">
            Registration Successful!
          </Typography>
        </Box>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Important:</strong> Save this JWT token securely. You'll need it to reset your password if you forget it.
            </Typography>
          </Alert>

          <Typography variant="subtitle1" fontWeight="600" gutterBottom>
            Your JWT Token:
          </Typography>

          <Box sx={{
            p: 2,
            bgcolor: 'grey.100',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.300',
            mb: 2
          }}>
            <Typography
              variant="body2"
              sx={{
                wordBreak: 'break-all',
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }}
            >
              {jwtToken}
            </Typography>
          </Box>

          <Button
            fullWidth
            variant="outlined"
            startIcon={<ContentCopy />}
            onClick={handleCopyToken}
            sx={{ mb: 2 }}
          >
            {copySuccess ? 'Token Copied!' : 'Copy Token'}
          </Button>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            fullWidth
            variant="contained"
            onClick={handleCloseModal}
            sx={{ py: 1.5 }}
          >
            Continue to Login
          </Button>
        </DialogActions>
      </Dialog>

      {/* Copy Success Snackbar */}
      <Snackbar
        open={copySuccess}
        autoHideDuration={2000}
        onClose={() => setCopySuccess(false)}
        message="Token copied to clipboard!"
      />
    </Box>
  );
};

export default Register;
