import React, { useEffect, useState } from 'react';
import {
  Box, Typography, Card, CardContent, CardMedia, IconButton, Chip, CircularProgress,
  AppBar, Toolbar, Avatar, But<PERSON>, <PERSON>er, Divider, Stack
} from '@mui/material';
import BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';
import ShareIcon from '@mui/icons-material/Share';
import HomeIcon from '@mui/icons-material/Home';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';

// Helper function to get correct image URL from database
const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return 'https://source.unsplash.com/300x200/?news';
  }

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // If it's already the correct path, use it directly
  if (imagePath.startsWith('/react-news/frontend/uploads/')) {
    return `http://localhost${imagePath}`;
  }

  // Extract filename from any path format stored in database
  let filename = '';

  if (imagePath.startsWith('/react-news/uploads/')) {
    filename = imagePath.replace('/react-news/uploads/', '');
  } else if (imagePath.startsWith('/uploads/')) {
    filename = imagePath.replace('/uploads/', '');
  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {
    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');
  } else if (imagePath.startsWith('admin/uploads/')) {
    filename = imagePath.replace('admin/uploads/', '');
  } else if (imagePath.startsWith('uploads/')) {
    filename = imagePath.replace('uploads/', '');
  } else if (imagePath.startsWith('assets/news/')) {
    filename = imagePath.replace('assets/news/', '');
  } else if (!imagePath.includes('/')) {
    // Just filename from database
    filename = imagePath;
  } else {
    // Extract filename from any other path
    filename = imagePath.split('/').pop();
  }

  // Use consistent frontend/uploads path for all images
  return `http://localhost/react-news/frontend/uploads/${filename}`;
};

const Saved = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [savedNews, setSavedNews] = useState([]);
  const [kostum, setKostum] = useState({ logo: '', title: '' });
  const [bottomValue, setBottomValue] = useState(2); // 2 = Simpan
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load website settings from database (same as LandingPage)
  useEffect(() => {
    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')
      .then(res => res.json())
      .then(response => {
        if (response.success && response.data) {
          const data = response.data;
          console.log('Website settings loaded:', data);

          // Logo is now stored as base64 data in database
          let logoPath = data.logo_file_path && data.logo_file_path.startsWith('data:')
            ? data.logo_file_path
            : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';

          setKostum({
            logo: logoPath,
            title: data.nama_website || 'React News Portal',
            description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'
          });

          // Update document title
          document.title = `Berita Tersimpan - ${data.nama_website || 'React News Portal'}`;
        } else {
          throw new Error('Invalid API response');
        }
      })
      .catch(err => {
        console.log('Database settings not available, using defaults:', err);
        setKostum({
          logo: '/logo192.png',
          title: 'React News Portal',
          description: 'Portal berita terkini dan terpercaya'
        });
        document.title = 'Berita Tersimpan - React News Portal';
      });
  }, []);

  // Load saved news from database
  useEffect(() => {
    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      navigate('/auth/login');
      return;
    }

    setLoading(true);
    fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news&user_id=${user.id}`)
      .then(res => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.text(); // Get as text first to debug
      })
      .then(text => {
        console.log('Raw response:', text);
        try {
          return JSON.parse(text);
        } catch (e) {
          console.error('JSON parse error:', e);
          console.error('Response text:', text);
          throw new Error('Invalid JSON response');
        }
      })
      .then(data => {
        console.log('Saved news loaded:', data);
        if (data.success && Array.isArray(data.data)) {
          // Map the data to match expected format
          const mappedData = data.data.map(item => {
            console.log('📸 Image data from database:', {
              id: item.id,
              title: item.title,
              originalImage: item.image,
              processedImage: getImageUrl(item.image)
            });

            return {
              id: item.id,
              title: item.title,
              description: item.description || item.content,
              content: item.content,
              image: item.image, // Raw image path from database posts table
              category: item.category_name || 'Umum',
              date: item.created_at || item.date,
              author: item.author_name || 'Admin',
              slug: item.slug,
              views: item.views || 0,
              likes: item.likes || 0,
              share: item.share || 0
            };
          });
          setSavedNews(mappedData);
        } else {
          setSavedNews([]);
        }
        setLoading(false);
      })
      .catch(err => {
        console.error('Error loading saved news:', err);
        setSavedNews([]);
        setLoading(false);
      });
  }, [isAuthenticated, user, navigate]);

  const handleBookmark = async (news) => {
    if (!user) {
      window.alert('Silakan login terlebih dahulu');
      navigate('/auth/login');
      return;
    }

    try {
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'remove_saved_news',
          id: news.id,
          user_id: user.id
        })
      });

      const result = await response.json();

      if (result.success) {
        setSavedNews(prev => prev.filter(n => n.id !== news.id));
        window.alert('Berita dihapus dari bookmark');
      } else {
        window.alert('Gagal menghapus berita dari bookmark: ' + result.message);
      }
    } catch (error) {
      console.error('Error removing bookmark:', error);
      window.alert('Gagal menghapus berita dari bookmark');
    }
  };

  const handleNewsClick = async (newsId) => {
    // Increment views when clicking news card
    try {
      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'increment_views',
          id: newsId
        })
      });
    } catch (error) {
      console.log('Could not increment views:', error);
    }

    // Navigate to news detail
    navigate(`/news/${newsId}`);
  };

  const handleShare = async (news) => {
    try {
      // Update share count in database
      fetch(`http://localhost:5000/api/posts/${news.id}/share`, {
        method: 'POST'
      }).catch(err => console.error('Error updating share count:', err));

      // Generate URL-friendly title for the link
      const urlTitle = news.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(link);
      window.alert('Link berita berhasil disalin ke clipboard!');

    } catch (error) {
      console.error('Failed to copy link:', error);
      window.alert('Gagal menyalin link berita');
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'blue.50', width: '100vw', overflow: 'hidden' }}>
      {/* Navbar - Simple with sidebar icon only */}
      <AppBar position="fixed" color="inherit" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>
        <Toolbar sx={{ minHeight: 80, px: 6 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Avatar
              src={kostum.logo}
              alt="Logo"
              sx={{ width: 48, height: 48, mr: 2 }}
              onError={(e) => { e.target.src = '/logo192.png'; }}
            />
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>
              {kostum.title}
            </Typography>
          </Box>
          <IconButton
            edge="end"
            color="primary"
            onClick={() => setSidebarOpen(true)}
            sx={{ mr: 1 }}
          >
            <MenuIcon fontSize="large" />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        anchor="right"
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        sx={{ zIndex: 1400 }}
      >
        <Box sx={{ width: 300, p: 3 }}>
          <IconButton
            onClick={() => setSidebarOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
            aria-label="Tutup"
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              src={kostum.logo}
              alt="Logo"
              sx={{ width: 32, height: 32, mr: 1 }}
              onError={(e) => { e.target.src = '/logo192.png'; }}
            />
            <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>
              {kostum.title}
            </Typography>
          </Box>
          <Divider sx={{ mb: 2 }} />
          <Stack spacing={2}>
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              sx={{ textTransform: 'none' }}
              startIcon={<HomeIcon />}
              onClick={() => {
                navigate('/');
                setSidebarOpen(false);
              }}
            >
              Beranda
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              sx={{ textTransform: 'none' }}
              startIcon={<BookmarkIcon />}
            >
              Berita Tersimpan
            </Button>
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              sx={{ textTransform: 'none' }}
              startIcon={<AdminPanelSettingsIcon />}
              onClick={() => {
                window.open('/dashboard', '_blank');
                setSidebarOpen(false);
              }}
            >
              Admin Dashboard
            </Button>
          </Stack>
        </Box>
      </Drawer>
      {/* Content Area with proper spacing from navbar */}
      <Box sx={{ mt: 12, px: 3, pb: 3 }}>
        {/* Total Saved Alert/Table */}
        <Box sx={{
          width: '100%',
          background: '#e1f5fe',
          color: '#007bff',
          textAlign: 'center',
          fontWeight: 600,
          fontSize: 16,
          padding: '12px 0',
          marginBottom: 3,
          borderRadius: 2,
          borderBottom: '1.5px solid #b3e5fc'
        }}>
          Total Berita Disimpan: {savedNews.length}
        </Box>

      <Box sx={{ padding: 2, paddingBottom: 10 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress />
          </Box>
        ) : savedNews.length === 0 ? (
          <Box sx={{ textAlign: 'center', padding: 5 }}>
            <BookmarkIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
            <Typography variant="h6" sx={{ color: 'grey.600', mb: 1 }}>
              Belum ada berita yang disimpan
            </Typography>
            <Typography variant="body2" sx={{ color: 'grey.500', mb: 3 }}>
              Mulai simpan berita favorit Anda dengan menekan ikon bookmark
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/')}
              sx={{ borderRadius: 2 }}
            >
              Jelajahi Berita
            </Button>
          </Box>
        ) : (
          savedNews.map((news, index) => (
            <Card key={`saved-${news.id}-${index}`} sx={{ marginBottom: 2, boxShadow: 2 }}>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' } }}>
                {/* Clickable Image Area */}
                <CardMedia
                  component="img"
                  onClick={() => handleNewsClick(news.id)}
                  sx={{
                    width: { xs: '100%', sm: 200 },
                    height: { xs: 200, sm: 150 },
                    objectFit: 'cover',
                    cursor: 'pointer'
                  }}
                  image={getImageUrl(news.image)}
                  alt={news.title}
                  onError={(e) => { e.target.src = 'https://source.unsplash.com/300x200/?news'; }}
                />
                {/* Clickable Content Area */}
                <CardContent
                  onClick={() => handleNewsClick(news.id)}
                  sx={{
                    flex: 1,
                    padding: 2,
                    cursor: 'pointer'
                  }}
                >
                  <Typography variant="h6" component="h3" sx={{ marginBottom: 1, fontWeight: 600 }}>
                    {news.title}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1, marginBottom: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={news.category}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Typography variant="caption" sx={{ color: 'grey.600', alignSelf: 'center' }}>
                      {formatDate(news.date)} • {news.views || 0} views
                    </Typography>
                  </Box>

                  <Typography variant="body2" sx={{ color: 'grey.700', marginBottom: 2 }}>
                    {news.description?.substring(0, 150)}...
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        handleBookmark(news);
                      }}
                      color="primary"
                      title="Hapus dari simpan"
                    >
                      <BookmarkAddedIcon />
                    </IconButton>
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        handleShare(news);
                      }}
                      color="primary"
                      title="Bagikan"
                    >
                      <ShareIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Box>
            </Card>
          ))
        )}
      </Box>
      {/* Custom Bottom Navigation */}
      <Box sx={{
        position: 'fixed',
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1200,
        display: 'block',
        backgroundColor: 'white',
        borderTop: '1px solid #e0e0e0',
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          height: 64,
          px: 1
        }}>
          <Box
            onClick={() => window.location.href = '/'}
            className={`bottom-nav-item ${bottomValue === 0 ? 'active' : ''}`}
          >
            <i className={`fas fa-home bottom-nav-icon ${bottomValue === 0 ? 'text-blue-600' : 'text-gray-500'}`}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomValue === 0 ? 'primary.main' : 'text.secondary' }}>
              Home
            </Typography>
          </Box>

          <Box
            onClick={() => setBottomValue(1)}
            className={`bottom-nav-item ${bottomValue === 1 ? 'active' : ''}`}
          >
            <i className={`fas fa-search bottom-nav-icon ${bottomValue === 1 ? 'text-blue-600' : 'text-gray-500'}`}></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: bottomValue === 1 ? 'primary.main' : 'text.secondary' }}>
              Cari
            </Typography>
          </Box>

          <Box className="bottom-nav-item active">
            <i className="fas fa-bookmark bottom-nav-icon text-blue-600"></i>
            <Typography variant="caption" className="bottom-nav-label" sx={{ color: 'primary.main', fontWeight: 600 }}>
              Simpan
            </Typography>
          </Box>
        </Box>
      </Box>
      </Box>
    </Box>
  );
};

export default Saved;
