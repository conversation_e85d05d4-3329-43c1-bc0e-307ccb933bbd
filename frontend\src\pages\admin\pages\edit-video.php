<!-- Edit Video -->
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Edit Video</h1>
                <p class="text-gray-600 mt-1">Edit informasi video yang sudah ada</p>
            </div>
            <a href="?page=videos" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Kembali
            </a>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading-state" class="bg-white rounded-xl shadow-sm p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-gray-600">Memuat data video...</p>
    </div>

    <!-- Edit Form -->
    <form id="editVideoForm" enctype="multipart/form-data" class="space-y-6" style="display: none;">
        <input type="hidden" id="video_id" name="video_id">
        
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Dasar</h3>
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Judul Video <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Masukkan judul video...">
                </div>
                
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Deskripsi Singkat <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" name="description" rows="3" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Deskripsi singkat yang akan ditampilkan di halaman utama..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">Maksimal 200 karakter</p>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        Konten Lengkap
                    </label>
                    <textarea id="content" name="content" rows="6"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Konten lengkap yang akan ditampilkan di halaman detail video..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">Konten lengkap yang akan ditampilkan di halaman detail video</p>
                </div>
            </div>
        </div>
        
        <!-- Video Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Video</h3>

            <!-- Video Type Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Tipe Video</label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="video_type" value="youtube" id="type_youtube" class="mr-2" checked onchange="toggleVideoType()">
                        <span class="text-sm">YouTube Video</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="video_type" value="upload" id="type_upload" class="mr-2" onchange="toggleVideoType()">
                        <span class="text-sm">Upload Video File</span>
                    </label>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- YouTube URL Section -->
                <div class="lg:col-span-2" id="youtube_section">
                    <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                        URL YouTube <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="youtube_url" name="youtube_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://www.youtube.com/watch?v=XXXXXXXXXXX">
                    <p class="text-sm text-gray-500 mt-1">Masukkan URL video YouTube (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)</p>
                </div>

                <!-- Video File Upload Section -->
                <div class="lg:col-span-2" id="upload_section" style="display: none;">
                    <label for="video_file" class="block text-sm font-medium text-gray-700 mb-2">
                        File Video <span class="text-red-500">*</span>
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="video_file" name="video_file" accept="video/*" class="hidden" onchange="previewVideoFile(this)">
                        <div id="video-upload-area" onclick="document.getElementById('video_file').click()" class="cursor-pointer">
                            <i class="fas fa-video text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload video baru</p>
                            <p class="text-sm text-gray-500 mt-1">Format: MP4, AVI, MOV, WebM, MKV (Max: 100MB)</p>
                            <p class="text-sm text-gray-500">Kosongkan jika tidak ingin mengubah video</p>
                        </div>
                    </div>

                    <!-- Current Video Info -->
                    <div id="current-video-info" class="mt-4" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Video Saat Ini:</label>
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-video text-blue-500"></i>
                                <div>
                                    <p id="current-video-name" class="font-medium text-gray-800"></p>
                                    <p id="current-video-size" class="text-sm text-gray-500"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Video</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" class="hidden" onchange="previewThumbnail(this)">
                        <div id="thumbnail-upload-area" onclick="document.getElementById('thumbnail').click()" class="cursor-pointer">
                            <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload thumbnail baru</p>
                            <p class="text-sm text-gray-500 mt-1">Kosongkan jika tidak ingin mengubah thumbnail</p>
                        </div>
                    </div>
                    
                    <!-- Current Thumbnail -->
                    <div id="current-thumbnail" class="mt-4" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Saat Ini:</label>
                        <div class="border rounded-lg p-2 bg-gray-50">
                            <img id="current-thumbnail-img" src="" alt="Current Thumbnail" class="max-w-full h-32 object-cover rounded mx-auto">
                        </div>
                    </div>
                </div>
                
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                    <select id="category" name="category"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="Umum">Umum</option>
                        <option value="Teknologi">Teknologi</option>
                        <option value="Bisnis">Bisnis</option>
                        <option value="Olahraga">Olahraga</option>
                        <option value="Hiburan">Hiburan</option>
                        <option value="Politik">Politik</option>
                        <option value="Kesehatan">Kesehatan</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Additional Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Pengaturan Tambahan</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input type="text" id="tags" name="tags"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="tag1, tag2, tag3">
                    <p class="text-sm text-gray-500 mt-1">Pisahkan tag dengan koma</p>
                </div>
                
                <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">Durasi (menit:detik)</label>
                    <input type="text" id="duration" name="duration"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="03:45">
                    <p class="text-sm text-gray-500 mt-1">Format: mm:ss (contoh: 03:45)</p>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status" name="status"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>
                
                <div class="flex items-center">
                    <div class="flex items-center h-5">
                        <input type="checkbox" id="featured" name="featured" value="1"
                               class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="featured" class="font-medium text-gray-700">Tampilkan di Featured</label>
                        <p class="text-gray-500">Video akan ditampilkan di bagian featured</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Video Statistics -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Statistik Video</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="stat-views">0</div>
                    <div class="text-sm text-gray-500">Views</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600" id="stat-likes">0</div>
                    <div class="text-sm text-gray-500">Likes</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="stat-shares">0</div>
                    <div class="text-sm text-gray-500">Shares</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" id="stat-comments">0</div>
                    <div class="text-sm text-gray-500">Comments</div>
                </div>
            </div>
        </div>
        
        <!-- YouTube Preview -->
        <div id="preview-container" class="bg-white rounded-xl shadow-sm p-6" style="display: none;">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Preview YouTube</h3>
            <div class="aspect-w-16 aspect-h-9">
                <iframe id="youtube-preview" class="w-full h-64 rounded-lg" src="" allowfullscreen></iframe>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-end space-x-4">
                <a href="?page=videos" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Update Video
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Get video ID from URL parameter
const urlParams = new URLSearchParams(window.location.search);
const videoId = urlParams.get('id');

// Load video data when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (videoId) {
        loadVideoData(videoId);
    } else {
        showError('ID video tidak ditemukan');
    }
});

// Function to load video data
async function loadVideoData(id) {
    try {
        const response = await fetch(`${API_BASE}?action=get_video&id=${id}`);
        const data = await response.json();
        
        if (data.success && data.data) {
            populateForm(data.data);
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('editVideoForm').style.display = 'block';
        } else {
            showError('Video tidak ditemukan');
        }
    } catch (error) {
        console.error('Error loading video:', error);
        showError('Terjadi kesalahan saat memuat data video');
    }
}

// Function to populate form with video data
function populateForm(video) {
    document.getElementById('video_id').value = video.id;
    document.getElementById('title').value = video.title || '';
    document.getElementById('description').value = video.description || '';
    document.getElementById('content').value = video.content || '';
    document.getElementById('youtube_url').value = video.youtube_url || '';
    document.getElementById('category').value = video.category || 'Umum';
    document.getElementById('tags').value = video.tags || '';
    document.getElementById('duration').value = video.duration || '';
    document.getElementById('status').value = video.status || 'published';
    document.getElementById('featured').checked = video.featured == 1;

    // Set video type and toggle sections
    const videoType = video.video_type || 'youtube';
    if (videoType === 'upload') {
        document.getElementById('type_upload').checked = true;
    } else {
        document.getElementById('type_youtube').checked = true;
    }
    toggleVideoType();

    // Show current video info for upload type
    if (videoType === 'upload' && video.video_path) {
        const currentVideoInfo = document.getElementById('current-video-info');
        const currentVideoName = document.getElementById('current-video-name');
        const currentVideoSize = document.getElementById('current-video-size');

        // Extract filename from path
        const filename = video.video_path.split('/').pop();
        currentVideoName.textContent = filename;

        if (video.file_size) {
            const sizeInMB = (video.file_size / (1024 * 1024)).toFixed(2);
            currentVideoSize.textContent = `${sizeInMB} MB • ${(video.file_format || 'Unknown').toUpperCase()}`;
        }

        currentVideoInfo.style.display = 'block';
    }
    
    // Show current thumbnail if exists
    if (video.thumbnail) {
        const currentThumbnail = document.getElementById('current-thumbnail');
        const currentThumbnailImg = document.getElementById('current-thumbnail-img');
        currentThumbnailImg.src = getCorrectImageUrl(video.thumbnail);
        currentThumbnail.style.display = 'block';
    }
    
    // Show statistics
    document.getElementById('stat-views').textContent = video.views || 0;
    document.getElementById('stat-likes').textContent = video.likes || 0;
    document.getElementById('stat-shares').textContent = video.shares || 0;
    document.getElementById('stat-comments').textContent = video.comments_count || 0;
    
    // Show YouTube preview if URL exists
    if (video.youtube_url) {
        updateYouTubePreview(video.youtube_url);
    }
}

// Helper function to get correct image URL
function getCorrectImageUrl(imagePath) {
    if (!imagePath) {
        return '/uploads/default-video.jpg';
    }

    // If it's already a full URL, return as is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath;
    }

    // Extract filename from path
    const filename = imagePath.split('/').pop();

    // Use localhost to access uploads folder
    const url = `http://localhost/react-news/uploads/${filename}`;
    return url;
}

// Extract YouTube ID from URL
function getYouTubeId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Update YouTube preview when URL changes
function updateYouTubePreview(youtubeUrl) {
    const youtubeId = getYouTubeId(youtubeUrl);
    const previewContainer = document.getElementById('preview-container');
    const youtubePreview = document.getElementById('youtube-preview');

    if (youtubeId) {
        youtubePreview.src = `https://www.youtube.com/embed/${youtubeId}`;
        previewContainer.style.display = 'block';
    } else {
        previewContainer.style.display = 'none';
    }
}

// Toggle video type sections
function toggleVideoType() {
    const youtubeRadio = document.getElementById('type_youtube');
    const uploadRadio = document.getElementById('type_upload');
    const youtubeSection = document.getElementById('youtube_section');
    const uploadSection = document.getElementById('upload_section');
    const youtubeUrl = document.getElementById('youtube_url');
    const videoFile = document.getElementById('video_file');

    if (youtubeRadio.checked) {
        youtubeSection.style.display = 'block';
        uploadSection.style.display = 'none';
        youtubeUrl.required = true;
        videoFile.required = false;
    } else {
        youtubeSection.style.display = 'none';
        uploadSection.style.display = 'block';
        youtubeUrl.required = false;
        videoFile.required = false; // Not required for edit (only if changing)
    }
}

// Preview video file function
function previewVideoFile(input) {
    const uploadArea = document.getElementById('video-upload-area');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileSize = (file.size / (1024 * 1024)).toFixed(2); // Convert to MB

        uploadArea.innerHTML = `
            <div class="relative">
                <div class="flex items-center justify-center space-x-3 p-4 bg-blue-50 rounded-lg">
                    <i class="fas fa-video text-blue-500 text-2xl"></i>
                    <div class="text-left">
                        <p class="font-medium text-gray-800">${file.name}</p>
                        <p class="text-sm text-gray-500">${fileSize} MB</p>
                    </div>
                </div>
                <div class="mt-2">
                    <button type="button" onclick="removeVideoFile()" class="text-red-500 text-sm hover:text-red-700">
                        <i class="fas fa-times mr-1"></i>Hapus
                    </button>
                </div>
            </div>
        `;
    }
}

// Remove video file function
function removeVideoFile() {
    const input = document.getElementById('video_file');
    const uploadArea = document.getElementById('video-upload-area');

    input.value = '';
    uploadArea.innerHTML = `
        <i class="fas fa-video text-4xl text-gray-400 mb-2"></i>
        <p class="text-gray-600">Klik untuk upload video baru</p>
        <p class="text-sm text-gray-500 mt-1">Format: MP4, AVI, MOV, WebM, MKV (Max: 100MB)</p>
        <p class="text-sm text-gray-500">Kosongkan jika tidak ingin mengubah video</p>
    `;
}

// Update YouTube preview when URL input changes
document.getElementById('youtube_url').addEventListener('input', function() {
    updateYouTubePreview(this.value);
});

// Preview thumbnail function
function previewThumbnail(input) {
    const uploadArea = document.getElementById('thumbnail-upload-area');

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadArea.innerHTML = `
                <div class="relative">
                    <img src="${e.target.result}" alt="Preview" class="max-w-full h-32 object-cover rounded-lg mx-auto">
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">${input.files[0].name}</p>
                        <button type="button" onclick="removeThumbnail()" class="text-red-500 text-sm hover:text-red-700">
                            <i class="fas fa-times mr-1"></i>Hapus
                        </button>
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Remove thumbnail function
function removeThumbnail() {
    const input = document.getElementById('thumbnail');
    const uploadArea = document.getElementById('thumbnail-upload-area');

    input.value = '';
    uploadArea.innerHTML = `
        <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
        <p class="text-gray-600">Klik untuk upload thumbnail baru</p>
        <p class="text-sm text-gray-500 mt-1">Kosongkan jika tidak ingin mengubah thumbnail</p>
    `;
}

// Form submission
document.getElementById('editVideoForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const videoType = document.querySelector('input[name="video_type"]:checked').value;

    // Validate based on video type
    if (videoType === 'youtube') {
        const youtubeUrl = document.getElementById('youtube_url').value;
        const youtubeId = getYouTubeId(youtubeUrl);
        if (!youtubeId) {
            Swal.fire({
                icon: 'error',
                title: 'URL YouTube Tidak Valid',
                text: 'Masukkan URL YouTube yang valid (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)'
            });
            return;
        }
    }

    // Create FormData
    const formData = new FormData(this);
    formData.append('action', 'update_video');

    if (videoType === 'youtube') {
        const youtubeUrl = document.getElementById('youtube_url').value;
        const youtubeId = getYouTubeId(youtubeUrl);
        formData.append('youtube_id', youtubeId);
    }

    try {
        // Show loading
        Swal.fire({
            title: 'Menyimpan...',
            text: 'Mohon tunggu sebentar',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Send request
        const response = await fetch(API_BASE, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: 'Video berhasil diupdate',
                confirmButtonText: 'OK'
            }).then(() => {
                window.location.href = '?page=videos';
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan saat mengupdate video'
            });
        }
    } catch (error) {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Gagal!',
            text: 'Terjadi kesalahan saat mengupdate video'
        });
    }
});

// Show error function
function showError(message) {
    document.getElementById('loading-state').innerHTML = `
        <div class="text-center">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <p class="text-red-600 mb-4">${message}</p>
            <a href="?page=videos" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                Kembali ke Daftar Video
            </a>
        </div>
    `;
}
</script>
</script>
