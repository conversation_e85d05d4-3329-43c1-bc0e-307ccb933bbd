<?php
// Video Play - Clean TikTok-style video player
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../admin/config.php';
    $pdo = getConnection(); // Get PDO connection
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get video ID from URL
$videoId = $_GET['id'] ?? 1;

// Get videos from database
try {
    $stmt = $pdo->prepare("
        SELECT id, title, description, content, youtube_url, youtube_id,
               video_base64, video_type, video_size, video_format,
               thumbnail, category, tags, duration, status, views, likes,
               shares, comments_count, featured, created_at, updated_at
        FROM videos
        WHERE status = 'published'
        ORDER BY id DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($videos)) {
        // Create sample data if no videos exist
        $videos = [[
            'id' => 1,
            'title' => 'Sample YouTube Video',
            'description' => 'This is a sample video for testing',
            'content' => 'Sample content',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'video_base64' => null,
            'video_type' => 'youtube',
            'video_size' => null,
            'video_format' => null,
            'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            'category' => 'Sample',
            'tags' => 'sample,test',
            'duration' => '03:32',
            'status' => 'published',
            'views' => 100,
            'likes' => 25,
            'shares' => 5,
            'comments_count' => 10,
            'featured' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]];
    }
} catch (Exception $e) {
    die("Database query error: " . $e->getMessage());
}

// Helper functions
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : null;
}

function getYouTubeEmbedUrl($videoId) {
    $params = http_build_query([
        'autoplay' => '0',
        'mute' => '0',
        'controls' => '1',
        'rel' => '0',
        'modestbranding' => '1',
        'enablejsapi' => '1',
        'playsinline' => '1',
        'fs' => '1',
        'cc_load_policy' => '0',
        'iv_load_policy' => '3',
        'origin' => 'http://localhost'
    ]);
    return "https://www.youtube.com/embed/{$videoId}?{$params}";
}

function getVideoSource($video) {
    $video_type = $video['video_type'] ?? 'youtube';

    if ($video_type === 'upload' && !empty($video['video_base64'])) {
        $video_format = $video['video_format'] ?? 'mp4';
        $mimeType = 'video/' . $video_format;
        $dataUrl = 'data:' . $mimeType . ';base64,' . $video['video_base64'];
        
        return [
            'type' => 'upload',
            'src' => $dataUrl,
            'format' => $video_format,
            'size' => $video['video_size'] ?? 0
        ];
    } else {
        $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);
        return [
            'type' => 'youtube',
            'src' => getYouTubeEmbedUrl($youtubeId),
            'youtube_id' => $youtubeId
        ];
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'baru saja';
    if ($time < 3600) return floor($time/60) . ' menit yang lalu';
    if ($time < 86400) return floor($time/3600) . ' jam yang lalu';
    if ($time < 2592000) return floor($time/86400) . ' hari yang lalu';
    if ($time < 31536000) return floor($time/2592000) . ' bulan yang lalu';
    return floor($time/31536000) . ' tahun yang lalu';
}

function getSaveCount($pdo, $videoId) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM video_saves WHERE video_id = ?");
        $stmt->execute([$videoId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    } catch (Exception $e) {
        return 0;
    }
}

// Find current video
$currentVideo = null;
foreach ($videos as $video) {
    if ($video['id'] == $videoId) {
        $currentVideo = $video;
        break;
    }
}

if (!$currentVideo) {
    $currentVideo = $videos[0];
}

// Prepare videos with sources
$videosWithSources = [];
foreach ($videos as $video) {
    $videosWithSources[] = [
        'video' => $video,
        'source' => getVideoSource($video)
    ];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?= htmlspecialchars($currentVideo['title']) ?> - Video Player</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjM0I4MkY2Ij4KICA8cGF0aCBkPSJNOCAydjIwbDEzLTEwTDh6Ii8+Cjwvc3ZnPg==">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            overflow: hidden;
        }

        /* Desktop Layout - Mobile-like with 9:16 video ratio */
        .desktop-container {
            width: 100vw;
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
            background: #000;
            padding-top: 60px; /* Space for navbar */
        }

        .desktop-video-item {
            width: 100vw;
            height: 100vh;
            scroll-snap-align: start;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 120px; /* Add padding for action buttons */
        }

        /* Dynamic Video Container - adapts to video aspect ratio */
        .desktop-video-container {
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
        }

        /* Portrait videos (9:16, 4:5, etc.) */
        .desktop-video-container.portrait {
            width: 400px;
            height: 711px; /* 400 * 16/9 = 711px for 9:16 ratio */
        }

        /* Landscape videos (16:9, 4:3, etc.) */
        .desktop-video-container.landscape {
            width: 711px; /* 16:9 ratio */
            height: 400px;
        }

        /* Adjust container size based on screen height */
        @media (max-height: 800px) {
            .desktop-video-container.portrait {
                width: 300px;
                height: 533px; /* 300 * 16/9 */
            }
            .desktop-video-container.landscape {
                width: 533px;
                height: 300px;
            }
        }

        @media (min-height: 1000px) {
            .desktop-video-container.portrait {
                width: 500px;
                height: 889px; /* 500 * 16/9 */
            }
            .desktop-video-container.landscape {
                width: 889px;
                height: 500px;
            }
        }

        /* Desktop Action Buttons - Right side like mobile */
        .desktop-actions {
            position: fixed;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 30;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .desktop-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            width: 60px;
            height: 60px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .desktop-action-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            color: #000;
            border-color: rgba(255, 255, 255, 0.8);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .desktop-action-btn i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .desktop-action-btn .count {
            font-size: 11px;
            font-weight: 600;
        }

        /* Active states for desktop */
        .desktop-action-btn.liked i {
            color: #ff0050;
        }

        .desktop-action-btn.saved i {
            color: #ffd700;
        }

        /* Responsive positioning for desktop actions */
        @media (max-width: 1200px) {
            .desktop-actions {
                right: 30px;
            }
        }

        @media (max-width: 1024px) {
            .desktop-actions {
                right: 20px;
            }
        }

        @media (max-width: 768px) {
            .desktop-actions {
                display: none; /* Hide on mobile, use mobile actions instead */
            }
        }

        /* Desktop Video Info - Bottom left like mobile */
        .desktop-video-info {
            position: absolute;
            left: 20px;
            right: 100px;
            bottom: 20px;
            z-index: 10;
            color: white;
        }

        /* Smooth scrolling for desktop */
        .desktop-container {
            scroll-behavior: smooth;
        }

        /* Desktop video navigation hint */
        .desktop-nav-hint {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 13px;
            z-index: 100;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Video title and description styling */
        .desktop-video-info .video-title {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .video-description {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .video-stats {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .tag {
            backdrop-filter: blur(10px);
        }

        /* Hide desktop nav hint on mobile */
        @media (max-width: 768px) {
            .desktop-nav-hint {
                display: none;
            }
        }

        /* Mobile Layout */
        .mobile-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
            background: #000;
            padding-top: 60px; /* Space for mobile navbar */
        }

        .video-item {
            width: 100vw;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            background: #000;
        }

        /* Video Styles */
        .video-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000;
            overflow: hidden;
        }

        .video-element {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        /* Portrait video (9:16 or taller) - fill container */
        .video-element.portrait {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Landscape video (16:9 or wider) - maintain aspect ratio, center in portrait container */
        .video-element.landscape {
            width: auto;
            height: 56.25vw; /* 9/16 of viewport width for 16:9 video */
            max-height: 100vh;
            object-fit: contain;
        }

        /* For desktop, adjust landscape video sizing */
        @media (min-width: 768px) {
            .video-element.landscape {
                height: 60vh;
                width: auto;
                max-width: 100%;
            }
        }

        /* Video element styling */
        .desktop-video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Portrait video styling */
        .desktop-video-container.portrait video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Landscape video styling */
        .desktop-video-container.landscape video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .desktop-video-container video::-webkit-media-controls {
            display: none !important;
        }

        .desktop-video-container video::-webkit-media-controls-enclosure {
            display: none !important;
        }

        .desktop-video-container video::-webkit-media-controls-panel {
            display: none !important;
        }

        .desktop-video-container video::-moz-media-controls {
            display: none !important;
        }

        /* Custom play button overlay */
        .custom-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 15;
        }

        .custom-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .custom-play-btn i {
            font-size: 32px;
            color: #000;
            margin-left: 4px; /* Adjust play icon position */
        }

        .custom-play-btn.playing {
            display: none !important;
        }

        /* Ensure video is clickable */
        .desktop-video-container video {
            cursor: pointer;
            position: relative;
            z-index: 1;
        }

        /* Volume control button */
        .volume-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 15;
        }

        .volume-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .volume-btn i {
            font-size: 20px;
            color: #fff;
        }

        .volume-btn.muted i {
            color: #ff4444;
        }

        /* Desktop Navbar */
        .desktop-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .back-btn, .menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .back-btn:hover, .menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .navbar-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            flex: 1;
            margin: 0 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Desktop Sidebar */
        .desktop-sidebar {
            position: fixed;
            top: 0;
            right: -320px;
            width: 320px;
            height: 100vh;
            background: white;
            z-index: 2000;
            transition: right 0.3s ease;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .desktop-sidebar.open {
            right: 0;
        }

        .sidebar-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .sidebar-logo span {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
        }

        .sidebar-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            color: #6b7280;
        }

        .sidebar-close:hover {
            background: #f3f4f6;
        }

        .sidebar-menu {
            flex: 1;
            padding: 20px;
        }

        .menu-section {
            margin-bottom: 30px;
        }

        .menu-section h3 {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #374151;
            text-decoration: none;
            border-radius: 8px;
            transition: background-color 0.2s;
            margin-bottom: 4px;
        }

        .menu-item:hover {
            background: #f3f4f6;
        }

        .menu-item.active {
            background: #dbeafe;
            color: #2563eb;
        }

        .menu-item i {
            width: 20px;
            text-align: center;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Mobile Navbar */
        .mobile-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 15px;
        }

        .mobile-navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .mobile-back-btn, .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .mobile-back-btn:hover, .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .mobile-navbar-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            flex: 1;
            margin: 0 15px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Mobile Sidebar */
        .mobile-sidebar {
            position: fixed;
            top: 0;
            right: -280px;
            width: 280px;
            height: 100vh;
            background: white;
            z-index: 2000;
            transition: right 0.3s ease;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar.open {
            right: 0;
        }

        .mobile-sidebar-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .mobile-sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .mobile-sidebar-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mobile-sidebar-logo img {
            width: 28px;
            height: 28px;
            border-radius: 50%;
        }

        .mobile-sidebar-logo span {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
        }

        .mobile-sidebar-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 6px;
            border-radius: 50%;
            color: #6b7280;
        }

        .mobile-sidebar-close:hover {
            background: #f3f4f6;
        }

        .mobile-sidebar-menu {
            flex: 1;
            padding: 15px;
        }

        .mobile-menu-section {
            margin-bottom: 25px;
        }

        .mobile-menu-section h3 {
            font-size: 11px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .mobile-menu-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 14px;
            color: #374151;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.2s;
            margin-bottom: 3px;
            font-size: 14px;
        }

        .mobile-menu-item:hover {
            background: #f3f4f6;
        }

        .mobile-menu-item.active {
            background: #dbeafe;
            color: #2563eb;
        }

        .mobile-menu-item i {
            width: 18px;
            text-align: center;
        }

        .mobile-sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Mobile Action Buttons */
        .mobile-actions {
            position: absolute;
            right: 15px;
            bottom: 100px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(0, 0, 0, 0.7);
            transform: scale(1.1);
        }

        .action-btn i {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .action-btn .count {
            font-size: 12px;
            font-weight: 600;
        }

        /* Active states */
        .action-btn.liked i {
            color: #ff0050;
        }

        .action-btn.saved i {
            color: #ffd700;
        }

        /* Mobile Video Info */
        .mobile-info {
            position: absolute;
            left: 15px;
            right: 80px;
            bottom: 20px;
            z-index: 10;
            color: white;
        }

        .video-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .video-description {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .video-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            opacity: 0.8;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .desktop-container {
                display: none;
            }
        }

        @media (min-width: 769px) {
            .mobile-container {
                display: none;
            }
        }

        /* Comment Modal */
        .comment-modal {
            position: fixed;
            bottom: -100%;
            left: 0;
            right: 0;
            height: 70vh;
            background: white;
            border-radius: 20px 20px 0 0;
            z-index: 1000;
            transition: bottom 0.3s ease;
            display: flex;
            flex-direction: column;
            box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .comment-modal.show {
            bottom: 0;
        }

        .comment-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .comment-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Scrollable comments area styling */
        #commentContent {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        #commentContent::-webkit-scrollbar {
            width: 6px;
        }

        #commentContent::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        #commentContent::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        #commentContent::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Comment item styling */
        .comment-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 12px;
            margin-bottom: 12px;
        }

        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        /* Save button styling */
        .save-btn.saved,
        .desktop-action-btn.save-btn.saved {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-color: #3b82f6;
        }

        .save-btn.saved i,
        .desktop-action-btn.save-btn.saved i {
            color: #3b82f6;
        }

        .action-btn.save-btn.saved,
        .action-btn-desktop.save-btn.saved {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <!-- Desktop Layout - Mobile-like with 9:16 videos -->
    <div class="desktop-container">
        <!-- Desktop Navbar -->
        <div class="desktop-navbar">
            <div class="navbar-content">
                <button class="back-btn" onclick="goBackToHome()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="navbar-title" id="navbar-title">
                    <?= htmlspecialchars($currentVideo['title']) ?>
                </div>
                <button class="menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <?php foreach ($videosWithSources as $index => $videoData): ?>
            <?php
            $video = $videoData['video'];
            $source = $videoData['source'];
            $vType = $source['type'];
            ?>
            <div class="desktop-video-item" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $vType ?>" data-video-title="<?= htmlspecialchars($video['title']) ?>">
                <!-- 9:16 Video Container -->
                <div class="desktop-video-container">
                    <?php if ($vType === 'upload'): ?>
                        <video class="video-element"
                               playsinline
                               webkit-playsinline
                               loop
                               preload="metadata"
                               id="desktop-video-<?= $video['id'] ?>"
                               data-video-id="<?= $video['id'] ?>"
                               style="width: 100%; height: 100%; object-fit: cover;">
                            <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                            Your browser does not support the video tag.
                        </video>

                        <!-- Custom Play Button -->
                        <button class="custom-play-btn" id="play-btn-<?= $video['id'] ?>" onclick="toggleDesktopVideo(<?= $video['id'] ?>)">
                            <i class="fas fa-play"></i>
                        </button>

                        <!-- Volume Control Button -->
                        <button class="volume-btn" id="volume-btn-<?= $video['id'] ?>" onclick="toggleVolume(<?= $video['id'] ?>)">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    <?php else: ?>
                        <iframe class="video-element"
                                src="<?= htmlspecialchars($source['src']) ?>"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                                allowfullscreen
                                style="width: 100%; height: 100%;">
                        </iframe>
                    <?php endif; ?>



                    <!-- Desktop Video Info - Bottom left -->
                    <div class="desktop-video-info">
                        <h3 class="video-title text-lg font-bold mb-2"><?= htmlspecialchars($video['title']) ?></h3>
                        <p class="video-description text-sm opacity-90 mb-3">
                            <?= htmlspecialchars(strlen($video['description']) > 100 ? substr($video['description'], 0, 100) . '...' : $video['description']) ?>
                        </p>
                        <div class="video-stats flex gap-4 text-xs opacity-80">
                            <span class="stat-item view-count">
                                <i class="fas fa-eye mr-1"></i>
                                <?= number_format($video['views']) ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-heart mr-1"></i>
                                <?= number_format($video['likes']) ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock mr-1"></i>
                                <?= $video['duration'] ?? 'N/A' ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-calendar mr-1"></i>
                                <?= timeAgo($video['created_at']) ?>
                            </span>
                        </div>
                        <?php if (!empty($video['tags'])): ?>
                            <div class="video-tags flex flex-wrap gap-2 mt-2">
                                <?php foreach (explode(',', $video['tags']) as $tag): ?>
                                    <span class="tag bg-white bg-opacity-20 px-2 py-1 rounded text-xs">
                                        #<?= trim(htmlspecialchars($tag)) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Desktop Action Buttons - Outside video container -->
                <div class="desktop-actions">
                    <button class="desktop-action-btn like-btn" data-video-id="<?= $video['id'] ?>" onclick="handleLike(<?= $video['id'] ?>)">
                        <i class="far fa-heart"></i>
                        <span class="count"><?= number_format($video['likes']) ?></span>
                    </button>

                    <button class="desktop-action-btn comment-btn" data-video-id="<?= $video['id'] ?>" onclick="handleComment(<?= $video['id'] ?>)">
                        <i class="far fa-comment"></i>
                        <span class="count"><?= $video['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="desktop-action-btn save-btn" data-video-id="<?= $video['id'] ?>" onclick="handleSave(<?= $video['id'] ?>)">
                        <i class="far fa-bookmark"></i>
                        <span class="count"><?= getSaveCount($pdo, $video['id']) ?></span>
                    </button>

                    <button class="desktop-action-btn share-btn" data-video-id="<?= $video['id'] ?>" onclick="handleShare(<?= $video['id'] ?>)">
                        <i class="fas fa-share"></i>
                        <span class="count"><?= number_format($video['shares']) ?></span>
                    </button>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Desktop Navigation Hint -->
        <div class="desktop-nav-hint">
            <i class="fas fa-mouse mr-2"></i>
            Scroll untuk video berikutnya
        </div>
    </div>

    <!-- Desktop Sidebar -->
    <div class="desktop-sidebar" id="desktop-sidebar">
        <div class="sidebar-content">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="" alt="Logo" id="sidebar-logo">
                    <span id="sidebar-title">React News Portal</span>
                </div>
                <button class="sidebar-close" onclick="toggleSidebar()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-menu">
                <div class="menu-section">
                    <h3>Quick Actions</h3>
                    <a href="/" class="menu-item">
                        <i class="fas fa-home"></i>
                        <span>Beranda</span>
                    </a>
                    <a href="/saved" class="menu-item">
                        <i class="fas fa-bookmark"></i>
                        <span>Berita Tersimpan</span>
                    </a>
                    <a href="#" class="menu-item active">
                        <i class="fas fa-play-circle"></i>
                        <span>Video</span>
                    </a>
                </div>

                <div class="menu-section">
                    <h3>Navigation</h3>
                    <a href="/" class="menu-item">
                        <i class="fas fa-arrow-left"></i>
                        <span>Kembali ke Beranda</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay" onclick="toggleSidebar()"></div>
            <div class="mb-6">
                <h1 class="text-xl font-bold mb-3"><?= htmlspecialchars($currentVideo['title']) ?></h1>
                <p class="text-gray-300 text-sm mb-4"><?= htmlspecialchars($currentVideo['description']) ?></p>

                <!-- Desktop Action Buttons -->
                <div class="flex gap-4 mb-4">
                    <button class="action-btn-desktop like-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleLike(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-heart mr-2"></i>
                        <span class="count"><?= number_format($currentVideo['likes']) ?></span>
                    </button>

                    <button class="action-btn-desktop comment-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleComment(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-comment mr-2"></i>
                        <span class="count"><?= $currentVideo['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="action-btn-desktop save-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleSave(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-bookmark mr-2"></i>
                        <span class="count"><?= getSaveCount($pdo, $currentVideo['id']) ?></span>
                    </button>

                    <button class="action-btn-desktop share-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleShare(<?= $currentVideo['id'] ?>)">
                        <i class="fas fa-share mr-2"></i>
                        <span class="count"><?= number_format($currentVideo['shares']) ?></span>
                    </button>
                </div>

                <!-- Video Stats -->
                <div class="text-sm text-gray-400 space-y-2">
                    <div class="flex items-center gap-4">
                        <span><i class="fas fa-eye mr-1"></i> <?= number_format($currentVideo['views']) ?></span>
                        <span><i class="fas fa-clock mr-1"></i> <?= $currentVideo['duration'] ?? 'N/A' ?></span>
                    </div>
                    <div>
                        <i class="fas fa-calendar mr-1"></i> <?= timeAgo($currentVideo['created_at']) ?>
                    </div>
                    <?php if (!empty($currentVideo['tags'])): ?>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <?php foreach (explode(',', $currentVideo['tags']) as $tag): ?>
                                <span class="bg-gray-700 px-2 py-1 rounded text-xs">#<?= trim(htmlspecialchars($tag)) ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Video List -->
            <div class="border-t border-gray-700 pt-4">
                <h3 class="font-semibold mb-3">Video Lainnya</h3>
                <div class="space-y-3">
                    <?php foreach ($videos as $video): ?>
                        <?php if ($video['id'] != $currentVideo['id']): ?>
                            <a href="video-play.php?id=<?= $video['id'] ?>" class="block hover:bg-gray-800 p-2 rounded">
                                <div class="flex gap-3">
                                    <div class="w-20 h-12 bg-gray-700 rounded flex-shrink-0 flex items-center justify-center">
                                        <i class="fas fa-play text-white"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium truncate"><?= htmlspecialchars($video['title']) ?></h4>
                                        <p class="text-xs text-gray-400 mt-1"><?= number_format($video['views']) ?> views</p>
                                    </div>
                                </div>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Layout -->
    <div class="mobile-container">
        <!-- Mobile Navbar -->
        <div class="mobile-navbar">
            <div class="mobile-navbar-content">
                <button class="mobile-back-btn" onclick="goBackToHome()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="mobile-navbar-title" id="mobile-navbar-title">
                    <?= htmlspecialchars($currentVideo['title']) ?>
                </div>
                <button class="mobile-menu-btn" onclick="toggleMobileSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <?php foreach ($videosWithSources as $index => $videoData): ?>
            <?php
            $video = $videoData['video'];
            $source = $videoData['source'];
            $vType = $source['type'];
            ?>
            <div class="video-item" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $vType ?>" data-video-title="<?= htmlspecialchars($video['title']) ?>">
                <div class="video-container">
                    <?php if ($vType === 'upload'): ?>
                        <video class="video-element"
                               playsinline
                               webkit-playsinline
                               loop
                               id="mobile-video-<?= $video['id'] ?>"
                               onclick="togglePlay(<?= $video['id'] ?>)">
                            <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                        </video>
                    <?php else: ?>
                        <iframe class="video-element"
                                src="<?= htmlspecialchars($source['src']) ?>"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                                allowfullscreen>
                        </iframe>
                    <?php endif; ?>
                </div>

                <!-- Mobile Actions -->
                <div class="mobile-actions">
                    <button class="action-btn like-btn" data-video-id="<?= $video['id'] ?>" onclick="handleLike(<?= $video['id'] ?>)">
                        <i class="far fa-heart"></i>
                        <span class="count"><?= number_format($video['likes']) ?></span>
                    </button>

                    <button class="action-btn comment-btn" data-video-id="<?= $video['id'] ?>" onclick="handleComment(<?= $video['id'] ?>)">
                        <i class="far fa-comment"></i>
                        <span class="count"><?= $video['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="action-btn save-btn" data-video-id="<?= $video['id'] ?>" onclick="handleSave(<?= $video['id'] ?>)">
                        <i class="far fa-bookmark"></i>
                        <span class="count"><?= getSaveCount($pdo, $video['id']) ?></span>
                    </button>

                    <button class="action-btn share-btn" data-video-id="<?= $video['id'] ?>" onclick="handleShare(<?= $video['id'] ?>)">
                        <i class="fas fa-share"></i>
                        <span class="count"><?= number_format($video['shares']) ?></span>
                    </button>
                </div>

                <!-- Mobile Info -->
                <div class="mobile-info">
                    <h3 class="video-title"><?= htmlspecialchars($video['title']) ?></h3>
                    <p class="video-description"><?= htmlspecialchars(strlen($video['description']) > 80 ? substr($video['description'], 0, 80) . '...' : $video['description']) ?></p>
                    <div class="video-stats">
                        <span class="stat-item">
                            <i class="fas fa-eye"></i>
                            <?= number_format($video['views']) ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-heart"></i>
                            <?= number_format($video['likes']) ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-clock"></i>
                            <?= $video['duration'] ?? 'N/A' ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-calendar"></i>
                            <?= timeAgo($video['created_at']) ?>
                        </span>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Mobile Sidebar -->
    <div class="mobile-sidebar" id="mobile-sidebar">
        <div class="mobile-sidebar-content">
            <div class="mobile-sidebar-header">
                <div class="mobile-sidebar-logo">
                    <img src="" alt="Logo" id="mobile-sidebar-logo">
                    <span id="mobile-sidebar-title">React News Portal</span>
                </div>
                <button class="mobile-sidebar-close" onclick="toggleMobileSidebar()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mobile-sidebar-menu">
                <div class="mobile-menu-section">
                    <h3>Quick Actions</h3>
                    <a href="/" class="mobile-menu-item">
                        <i class="fas fa-home"></i>
                        <span>Beranda</span>
                    </a>
                    <a href="/saved" class="mobile-menu-item">
                        <i class="fas fa-bookmark"></i>
                        <span>Berita Tersimpan</span>
                    </a>
                    <a href="#" class="mobile-menu-item active">
                        <i class="fas fa-play-circle"></i>
                        <span>Video</span>
                    </a>
                </div>

                <div class="mobile-menu-section">
                    <h3>Navigation</h3>
                    <a href="/" class="mobile-menu-item">
                        <i class="fas fa-arrow-left"></i>
                        <span>Kembali ke Beranda</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="mobile-sidebar-overlay" id="mobile-sidebar-overlay" onclick="toggleMobileSidebar()"></div>

    <!-- Comment Modal -->
    <div class="comment-overlay" id="commentOverlay" onclick="closeCommentModal()"></div>
    <div class="comment-modal" id="commentModal">
        <div class="p-4 border-b">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Komentar</h3>
                <button onclick="closeCommentModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Scrollable Comments Area -->
        <div class="flex-1 overflow-y-auto p-4 min-h-[300px] max-h-[400px]" id="commentContent" style="max-height: 400px; overflow-y: auto;">
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-comments text-3xl mb-2"></i>
                <p>Belum ada komentar</p>
                <p class="text-sm">Jadilah yang pertama berkomentar!</p>
            </div>
        </div>

        <!-- Comment Form - Fixed at Bottom -->
        <div class="p-4 border-t bg-gray-50">
            <div class="flex gap-3">
                <input type="text"
                       id="commentInput"
                       placeholder="Tulis komentar..."
                       class="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       maxlength="500"
                       oninput="updateCommentCounter()"
                       onkeypress="if(event.key==='Enter') submitComment()">
                <button onclick="submitComment()"
                        id="commentSubmit"
                        class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="flex justify-between items-center mt-2">
                <div class="text-xs text-gray-500">
                    <span id="commentCounter">0/500</span>
                </div>
                <div class="text-xs text-gray-400">
                    Tekan Enter untuk kirim
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentVideoId = null;

        // Global function declarations (hoisted) - Define these first
        function handleLike(videoId) {
            console.log('handleLike called with videoId:', videoId);
            toggleLike(videoId);
        }

        function handleComment(videoId) {
            console.log('handleComment called with videoId:', videoId);
            currentVideoId = videoId;
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            if (overlay && modal) {
                overlay.classList.add('show');
                modal.classList.add('show');
                loadComments(videoId);
            } else {
                console.error('Comment modal elements not found');
            }
        }

        function handleSave(videoId) {
            console.log('handleSave called with videoId:', videoId);
            toggleSave(videoId);
        }

        async function handleShare(videoId) {
            console.log('handleShare called with videoId:', videoId);
            try {
                // Increment share count first
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}&platform=direct`
                });

                const data = await response.json();

                if (data.success) {
                    // Update share count (both mobile and desktop)
                    const shareButtons = document.querySelectorAll(`.share-btn[data-video-id="${videoId}"], .desktop-action-btn.share-btn[data-video-id="${videoId}"]`);
                    shareButtons.forEach(btn => {
                        const count = btn.querySelector('.count');
                        if (count) {
                            count.textContent = formatNumber(data.shares);
                        }
                    });
                }

                // Show share modal
                const shareUrl = `${window.location.origin}${window.location.pathname}?id=${videoId}`;
                showShareModal(shareUrl);

            } catch (error) {
                console.error('Error sharing video:', error);
                // Still show share modal even if increment fails
                const shareUrl = `${window.location.origin}${window.location.pathname}?id=${videoId}`;
                showShareModal(shareUrl);
            }
        }

        // Toggle Like function
        async function toggleLike(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=toggle_video_like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();

                if (data.success) {
                    // Update all like buttons for this video (both mobile and desktop)
                    const likeButtons = document.querySelectorAll(`.like-btn[data-video-id="${videoId}"], .desktop-action-btn.like-btn[data-video-id="${videoId}"]`);
                    likeButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const count = btn.querySelector('.count');

                        if (data.is_liked) {
                            icon.className = 'fas fa-heart';
                            btn.classList.add('liked');
                        } else {
                            icon.className = 'far fa-heart';
                            btn.classList.remove('liked');
                        }

                        if (count) {
                            count.textContent = formatNumber(data.likes);
                        }
                    });

                    showToast(data.is_liked ? 'Video disukai!' : 'Like dibatalkan', 'success');
                } else {
                    showToast('Gagal menyukai video', 'error');
                }
            } catch (error) {
                console.error('Error toggling like:', error);
                showToast('Gagal menyukai video', 'error');
            }
        }

        // Toggle Save function
        async function toggleSave(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=toggle_video_save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();

                if (data.success) {
                    // Update all save buttons for this video (both mobile and desktop)
                    const saveButtons = document.querySelectorAll(`.save-btn[data-video-id="${videoId}"], .desktop-action-btn.save-btn[data-video-id="${videoId}"]`);
                    saveButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const countElement = btn.querySelector('.count');

                        // Update icon based on save status
                        if (data.is_saved) {
                            icon.className = 'fas fa-bookmark';
                            btn.classList.add('saved');
                        } else {
                            icon.className = 'far fa-bookmark';
                            btn.classList.remove('saved');
                        }

                        // Always show save count as number
                        if (countElement) {
                            countElement.textContent = formatNumber(data.saves || 0);
                        }
                    });

                    showToast(data.message || (data.is_saved ? 'Video disimpan!' : 'Video dihapus dari simpanan'), 'success');
                } else {
                    showToast(data.message || 'Gagal menyimpan video', 'error');
                }
            } catch (error) {
                console.error('Error toggling save:', error);
                showToast('Gagal menyimpan video', 'error');
            }
        }

        // Load save status for current video
        async function loadSaveStatus(videoId) {
            try {
                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=check_video_save_status&video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    // Update all save buttons (mobile, desktop, and current video specific)
                    const saveButtons = document.querySelectorAll(`
                        .save-btn[data-video-id="${videoId}"],
                        .desktop-action-btn.save-btn[data-video-id="${videoId}"],
                        .action-btn-desktop.save-btn[data-video-id="${videoId}"]
                    `);

                    saveButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const countElement = btn.querySelector('.count');

                        // Update icon based on save status
                        if (data.is_saved) {
                            icon.className = 'fas fa-bookmark';
                            btn.classList.add('saved');
                        } else {
                            icon.className = 'far fa-bookmark';
                            btn.classList.remove('saved');
                        }

                        // Always show save count as number
                        if (countElement) {
                            countElement.textContent = formatNumber(data.saves || 0);
                        }
                    });

                    console.log(`Updated save status for video ${videoId}: ${data.is_saved ? 'saved' : 'not saved'}, count: ${data.saves}`);
                }
            } catch (error) {
                console.error('Error loading save status:', error);
            }
        }

        // Initialize video orientation detection
        function initializeVideoOrientation() {
            // Handle HTML5 videos
            const videos = document.querySelectorAll('video.video-element');
            videos.forEach(video => {
                video.addEventListener('loadedmetadata', function() {
                    detectVideoOrientation(this);
                });

                // If video is already loaded
                if (video.readyState >= 1) {
                    detectVideoOrientation(video);
                }
            });

            // Handle iframes (YouTube videos)
            const iframes = document.querySelectorAll('iframe.video-element');
            iframes.forEach(iframe => {
                // For YouTube videos, we'll assume they follow standard aspect ratios
                // Most YouTube videos are 16:9 (landscape)
                iframe.classList.add('landscape');
            });
        }

        // Detect video orientation and apply appropriate class
        function detectVideoOrientation(video) {
            const aspectRatio = video.videoWidth / video.videoHeight;
            const container = video.closest('.desktop-video-container');

            // Remove existing orientation classes from both video and container
            video.classList.remove('portrait', 'landscape');
            if (container) {
                container.classList.remove('portrait', 'landscape');
            }

            // Add appropriate class based on aspect ratio
            if (aspectRatio > 1) {
                // Landscape (wider than tall) - 16:9, 4:3, etc.
                video.classList.add('landscape');
                if (container) {
                    container.classList.add('landscape');
                }
                console.log('Video detected as landscape:', aspectRatio);
            } else {
                // Portrait (taller than wide) - 9:16, 4:5, etc.
                video.classList.add('portrait');
                if (container) {
                    container.classList.add('portrait');
                }
                console.log('Video detected as portrait:', aspectRatio);
            }
        }

        // Navbar and Sidebar Functions
        function toggleSidebar() {
            const sidebar = document.getElementById('desktop-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            } else {
                sidebar.classList.add('open');
                overlay.classList.add('show');
            }
        }

        // Update navbar title based on current video
        function updateNavbarTitle(videoTitle) {
            const navbarTitle = document.getElementById('navbar-title');
            if (navbarTitle) {
                navbarTitle.textContent = videoTitle;
            }
        }

        // Load sidebar logo and title from admin settings
        function loadSidebarSettings() {
            fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const logo = document.getElementById('sidebar-logo');
                        const title = document.getElementById('sidebar-title');

                        if (logo && data.data.logo_data) {
                            logo.src = data.data.logo_data;
                        }

                        if (title && data.data.nama_website) {
                            title.textContent = data.data.nama_website;
                        }
                    }
                })
                .catch(error => console.error('Error loading sidebar settings:', error));
        }

        // Navbar and Sidebar Functions
        function goBackToHome() {
            // Navigate to LandingPage.js using React route
            window.location.href = '/';
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('desktop-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            } else {
                sidebar.classList.add('open');
                overlay.classList.add('show');
            }
        }

        function toggleMobileSidebar() {
            const sidebar = document.getElementById('mobile-sidebar');
            const overlay = document.getElementById('mobile-sidebar-overlay');

            if (sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            } else {
                sidebar.classList.add('open');
                overlay.classList.add('show');
            }
        }

        // Update navbar title based on current video
        function updateNavbarTitle(videoTitle) {
            const navbarTitle = document.getElementById('navbar-title');
            if (navbarTitle) {
                navbarTitle.textContent = videoTitle;
            }
        }

        // Load sidebar logo and title from admin settings
        function loadSidebarSettings() {
            fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        // Desktop sidebar
                        const logo = document.getElementById('sidebar-logo');
                        const title = document.getElementById('sidebar-title');

                        // Mobile sidebar
                        const mobileLogo = document.getElementById('mobile-sidebar-logo');
                        const mobileTitle = document.getElementById('mobile-sidebar-title');

                        // Set logo with fallback
                        const logoSrc = data.data.logo_data || data.data.logo_file_path || '/logo192.png';
                        if (logo) {
                            logo.src = logoSrc;
                            logo.onerror = function() { this.src = '/logo192.png'; };
                        }
                        if (mobileLogo) {
                            mobileLogo.src = logoSrc;
                            mobileLogo.onerror = function() { this.src = '/logo192.png'; };
                        }

                        // Set website name with fallback
                        const websiteName = data.data.nama_website || 'React News Portal';
                        if (title) title.textContent = websiteName;
                        if (mobileTitle) mobileTitle.textContent = websiteName;

                        console.log('📱🖥️ Sidebar settings loaded:', {
                            logo: logoSrc,
                            name: websiteName
                        });
                    }
                })
                .catch(error => console.error('Error loading sidebar settings:', error));
        }

        // Initialize desktop video event listeners
        function initializeDesktopVideoListeners() {
            const desktopVideos = document.querySelectorAll('[id^="desktop-video-"]');

            desktopVideos.forEach(video => {
                const videoId = video.dataset.videoId;

                // Add loadedmetadata event listener
                video.addEventListener('loadedmetadata', function() {
                    console.log('🖥️ Desktop video metadata loaded:', videoId);
                    detectVideoOrientation(video);
                });

                // Add canplay event listener
                video.addEventListener('canplay', function() {
                    console.log('🖥️ Desktop video can play:', videoId);
                });

                // Add error event listener
                video.addEventListener('error', function(e) {
                    console.error('🖥️ Desktop video error:', videoId, e);
                });

                // Add play event listener
                video.addEventListener('play', function() {
                    console.log('🖥️ Desktop video started playing:', videoId);
                });

                // Add pause event listener
                video.addEventListener('pause', function() {
                    console.log('🖥️ Desktop video paused:', videoId);
                });

                // Add click event listener to video element as backup
                video.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🖥️ Desktop video clicked directly:', videoId);
                    toggleDesktopVideo(videoId);
                });
            });
        }

        // Initialize view count and auto-increment on page load
        document.addEventListener('DOMContentLoaded', function() {
            const videoId = <?= $currentVideo['id'] ?>;

            // Increment view immediately on page load
            incrementView(videoId);

            // Update action buttons for current video
            updateActionButtonsForVideo(videoId);

            // Initialize video orientation detection
            initializeVideoOrientation();

            // Initialize desktop video event listeners
            initializeDesktopVideoListeners();

            // Load sidebar settings from admin
            loadSidebarSettings();

            // Initialize view tracking for both mobile and desktop
            initializeViewTracking();
            initializeDesktopViewTracking();

            // Initialize desktop video click handlers
            initializeDesktopVideoHandlers();

            // Initialize comment input
            const commentInput = document.getElementById('commentInput');
            const commentSubmit = document.getElementById('commentSubmit');
            const commentCounter = document.getElementById('commentCounter');

            if (commentInput && commentSubmit && commentCounter) {
                commentInput.addEventListener('input', function() {
                    const length = this.value.length;
                    commentCounter.textContent = `${length}/500`;

                    if (length > 0 && length <= 500) {
                        commentSubmit.disabled = false;
                        commentSubmit.classList.remove('opacity-50');
                    } else {
                        commentSubmit.disabled = true;
                        commentSubmit.classList.add('opacity-50');
                    }
                });

                commentInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!commentSubmit.disabled) {
                            submitComment();
                        }
                    }
                });
            }
        });

        // Initialize view tracking for mobile scroll
        function initializeViewTracking() {
            const isMobile = window.innerWidth < 768;
            if (!isMobile) return;

            const mobileContainer = document.querySelector('.mobile-container');
            if (!mobileContainer) return;

            let viewedVideos = new Set();

            // Intersection Observer for tracking video views and auto pause
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const videoItem = entry.target;
                    const videoId = videoItem.dataset.videoId;
                    const video = document.getElementById(`mobile-video-${videoId}`);

                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        if (videoId) {
                            console.log('📱 Mobile video in view:', videoId);

                            // Update mobile navbar title with current video title
                            const videoTitle = videoItem.dataset.videoTitle || videoItem.querySelector('.video-title')?.textContent || 'Video';
                            const mobileNavbarTitle = document.getElementById('mobile-navbar-title');
                            if (mobileNavbarTitle) {
                                mobileNavbarTitle.textContent = videoTitle;
                            }

                            // Update action buttons for current video
                            updateActionButtonsForVideo(videoId);

                            if (!viewedVideos.has(videoId)) {
                                viewedVideos.add(videoId);
                                incrementView(videoId);
                                console.log('📱 Mobile view tracked for video:', videoId);
                            }
                        }
                    } else {
                        // Video is out of view, pause it
                        if (video && !video.paused) {
                            video.pause();
                            console.log('📱 Mobile video auto-paused (out of view):', videoId);
                        }
                    }
                });
            }, {
                threshold: 0.5 // Trigger when 50% of video is visible
            });

            // Observe all video items
            const videoItems = document.querySelectorAll('.video-item');
            videoItems.forEach(item => observer.observe(item));
        }

        // Initialize desktop view tracking
        function initializeDesktopViewTracking() {
            const isDesktop = window.innerWidth >= 769;
            if (!isDesktop) return;

            const desktopContainer = document.querySelector('.desktop-video-main');
            if (!desktopContainer) return;

            let viewedVideos = new Set();

            // Intersection Observer for desktop video tracking and auto pause
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const videoItem = entry.target;
                    const videoId = videoItem.dataset.videoId;
                    const video = document.getElementById(`desktop-video-${videoId}`);
                    const playBtn = document.getElementById(`play-btn-${videoId}`);

                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        if (videoId) {
                            console.log('🖥️ Desktop video in view:', videoId);

                            // Update navbar title with current video title
                            const videoTitle = videoItem.dataset.videoTitle || videoItem.querySelector('.video-title')?.textContent || 'Video';
                            updateNavbarTitle(videoTitle);

                            // Update action buttons for current video
                            updateActionButtonsForVideo(videoId);

                            if (!viewedVideos.has(videoId)) {
                                viewedVideos.add(videoId);
                                incrementView(videoId);
                                console.log('🖥️ Desktop view tracked for video:', videoId);
                            }
                        }
                    } else {
                        // Video is out of view, pause it
                        if (video && !video.paused) {
                            video.pause();
                            if (playBtn) {
                                playBtn.classList.remove('playing');
                                playBtn.innerHTML = '<i class="fas fa-play"></i>';
                            }
                            console.log('🖥️ Desktop video auto-paused (out of view):', videoId);
                        }
                    }
                });
            }, {
                threshold: 0.5
            });

            // Observe all desktop video items
            const desktopVideoItems = document.querySelectorAll('.desktop-video-item');
            desktopVideoItems.forEach(item => observer.observe(item));
        }

        // Handle desktop video click
        function handleDesktopVideoClick(videoId) {
            console.log('🖥️ Desktop video clicked:', videoId);
            toggleDesktopVideo(videoId);
        }

        // Toggle Desktop Video Play/Pause
        function toggleDesktopVideo(videoId) {
            const video = document.getElementById(`desktop-video-${videoId}`);
            const playBtn = document.getElementById(`play-btn-${videoId}`);

            if (!video) {
                console.error('🖥️ Desktop video element not found:', videoId);
                return;
            }

            console.log('🖥️ Toggle desktop video called:', videoId, 'paused:', video.paused);

            // Detect video orientation when video is loaded
            if (video.readyState >= 1) {
                detectVideoOrientation(video);
            }

            if (video.paused) {
                console.log('🖥️ Attempting to play desktop video:', videoId);

                // Pause other videos first
                pauseOtherDesktopVideos(videoId);

                // Try to play with audio first
                video.muted = false;
                video.volume = 1.0;

                const playPromise = video.play();

                if (playPromise !== undefined) {
                    playPromise.then(() => {
                        console.log('🖥️ Desktop video playing with audio:', videoId);
                        if (playBtn) {
                            playBtn.classList.add('playing');
                            playBtn.innerHTML = '<i class="fas fa-pause"></i>';
                            playBtn.style.display = 'none'; // Hide play button when playing
                        }
                    }).catch(error => {
                        console.error('🖥️ Error playing desktop video with audio:', error);

                        // Try with muted as fallback
                        video.muted = true;
                        video.play().then(() => {
                            console.log('🖥️ Desktop video playing muted (fallback):', videoId);
                            if (playBtn) {
                                playBtn.classList.add('playing');
                                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
                                playBtn.style.display = 'none';
                            }
                        }).catch(mutedError => {
                            console.error('🖥️ Error playing desktop video even muted:', mutedError);
                        });
                    });
                }
            } else {
                // Pause video
                console.log('🖥️ Pausing desktop video:', videoId);
                video.pause();
                if (playBtn) {
                    playBtn.classList.remove('playing');
                    playBtn.innerHTML = '<i class="fas fa-play"></i>';
                    playBtn.style.display = 'flex'; // Show play button when paused
                }
            }
        }

        // Pause other desktop videos
        function pauseOtherDesktopVideos(currentVideoId) {
            const allVideos = document.querySelectorAll('[id^="desktop-video-"]');
            allVideos.forEach(video => {
                const videoId = video.dataset.videoId;
                if (videoId !== currentVideoId && !video.paused) {
                    video.pause();
                    const playBtn = document.getElementById(`play-btn-${videoId}`);
                    if (playBtn) {
                        playBtn.classList.remove('playing');
                        playBtn.innerHTML = '<i class="fas fa-play"></i>';
                    }
                }
            });
        }

        // Initialize desktop video handlers
        function initializeDesktopVideoHandlers() {
            const isDesktop = window.innerWidth >= 769;
            if (!isDesktop) return;

            // Add click handlers to desktop videos
            const desktopVideos = document.querySelectorAll('[id^="desktop-video-"]');
            desktopVideos.forEach(video => {
                video.addEventListener('click', function() {
                    const videoId = this.dataset.videoId;
                    if (videoId) {
                        toggleDesktopVideo(videoId);
                    }
                });
            });
        }

        // Increment view count
        async function incrementView(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=increment_video_view', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `video_id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ View incremented for video:', videoId, 'New count:', data.views);

                    // Update view count in UI if available
                    updateViewCount(videoId, data.views);
                }
            } catch (error) {
                console.error('Error incrementing view:', error);
            }
        }

        // Update view count in UI
        function updateViewCount(videoId, newCount) {
            const viewElements = document.querySelectorAll(`[data-video-id="${videoId}"] .view-count, .video-item[data-video-id="${videoId}"] .stat-item:first-child`);
            viewElements.forEach(element => {
                if (element.textContent.includes('👁') || element.querySelector('.fa-eye')) {
                    const icon = element.querySelector('i') ? element.querySelector('i').outerHTML : '';
                    element.innerHTML = icon + ' ' + formatNumber(newCount);
                }
            });
        }

        // Toggle video play (mobile)
        function togglePlay(videoId) {
            const video = document.getElementById(`mobile-video-${videoId}`);
            if (video) {
                if (video.paused) {
                    // Ensure audio is not muted when playing
                    video.muted = false;
                    video.volume = 1.0; // Set volume to maximum
                    video.play().then(() => {
                        console.log('📱 Mobile video playing with audio:', videoId);
                    }).catch(error => {
                        console.error('Error playing mobile video:', error);
                        // If autoplay fails due to browser policy, try with muted first
                        video.muted = true;
                        video.play().then(() => {
                            console.log('📱 Mobile video playing muted (browser policy):', videoId);
                        });
                    });
                } else {
                    video.pause();
                    console.log('📱 Mobile video paused:', videoId);
                }
            }
        }

        // Toggle Volume (Desktop)
        function toggleVolume(videoId) {
            const video = document.getElementById(`desktop-video-${videoId}`);
            const volumeBtn = document.getElementById(`volume-btn-${videoId}`);

            if (!video || !volumeBtn) return;

            if (video.muted) {
                // Unmute
                video.muted = false;
                video.volume = 1.0;
                volumeBtn.classList.remove('muted');
                volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                console.log('🔊 Desktop video unmuted:', videoId);
            } else {
                // Mute
                video.muted = true;
                volumeBtn.classList.add('muted');
                volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                console.log('🔇 Desktop video muted:', videoId);
            }
        }

        // Re-initialize video orientation when video changes
        function reinitializeVideoOrientation() {
            setTimeout(() => {
                initializeVideoOrientation();
            }, 100);
        }

        // Debounce function to avoid too many API calls
        let updateTimeout;
        function updateActionButtonsForVideo(videoId) {
            console.log('Updating action buttons for video:', videoId);

            // Clear previous timeout
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }

            // Debounce the update calls
            updateTimeout = setTimeout(() => {
                // Load save status for current video
                loadSaveStatus(videoId);

                // Update like status and count
                updateLikeStatus(videoId);

                // Update comment count
                updateCommentCount(videoId);

                // Update share count
                updateShareCount(videoId);
            }, 300); // Wait 300ms before making API calls
        }

        // Update like status for video
        async function updateLikeStatus(videoId) {
            try {
                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=check_video_like_status&video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    // Update all like buttons (mobile, desktop, and current video specific)
                    const likeButtons = document.querySelectorAll(`
                        .like-btn[data-video-id="${videoId}"],
                        .desktop-action-btn.like-btn[data-video-id="${videoId}"],
                        .action-btn-desktop.like-btn[data-video-id="${videoId}"]
                    `);

                    likeButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const countElement = btn.querySelector('.count');

                        if (data.is_liked) {
                            icon.className = 'fas fa-heart';
                            btn.classList.add('liked');
                        } else {
                            icon.className = 'far fa-heart';
                            btn.classList.remove('liked');
                        }

                        if (countElement) {
                            countElement.textContent = formatNumber(data.likes || 0);
                        }
                    });

                    console.log(`Updated like status for video ${videoId}: ${data.is_liked ? 'liked' : 'not liked'}, count: ${data.likes}`);
                }
            } catch (error) {
                console.error('Error updating like status:', error);
            }
        }

        // Update comment count for video
        async function updateCommentCount(videoId) {
            try {
                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_comments&video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    // Update all comment buttons (mobile, desktop, and current video specific)
                    const commentButtons = document.querySelectorAll(`
                        .comment-btn[data-video-id="${videoId}"],
                        .desktop-action-btn.comment-btn[data-video-id="${videoId}"],
                        .action-btn-desktop.comment-btn[data-video-id="${videoId}"]
                    `);

                    commentButtons.forEach(btn => {
                        const countElement = btn.querySelector('.count');
                        if (countElement) {
                            countElement.textContent = formatNumber(data.count || 0);
                        }
                    });

                    console.log(`Updated comment count for video ${videoId}: ${data.count}`);
                }
            } catch (error) {
                console.error('Error updating comment count:', error);
            }
        }

        // Update share count for video
        async function updateShareCount(videoId) {
            try {
                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_share_count&video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    // Update all share buttons (mobile, desktop, and current video specific)
                    const shareButtons = document.querySelectorAll(`
                        .share-btn[data-video-id="${videoId}"],
                        .desktop-action-btn.share-btn[data-video-id="${videoId}"],
                        .action-btn-desktop.share-btn[data-video-id="${videoId}"]
                    `);

                    shareButtons.forEach(btn => {
                        const countElement = btn.querySelector('.count');
                        if (countElement) {
                            countElement.textContent = formatNumber(data.shares || 0);
                        }
                    });

                    console.log(`Updated share count for video ${videoId}: ${data.shares}`);
                }
            } catch (error) {
                console.error('Error updating share count:', error);
            }
        }

        // All handler functions moved to top of script

        // Handle Share function moved to top of script

        // Handle Comment
        function handleComment(videoId) {
            currentVideoId = videoId;
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.add('show');
            modal.classList.add('show');

            loadComments(videoId);
        }

        // Close Comment Modal
        function closeCommentModal() {
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.remove('show');
            modal.classList.remove('show');

            // Clear input
            const commentInput = document.getElementById('commentInput');
            if (commentInput) {
                commentInput.value = '';
                document.getElementById('commentCounter').textContent = '0/500';
            }
        }

        // Load Comments
        async function loadComments(videoId) {
            const commentContent = document.getElementById('commentContent');

            try {
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Memuat komentar...</p>
                    </div>
                `;

                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_comments&video_id=${videoId}`);
                const data = await response.json();

                if (data.success && data.comments && data.comments.length > 0) {
                    displayComments(data.comments);
                } else {
                    commentContent.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-comment text-2xl mb-2"></i>
                            <p>Belum ada komentar. Jadilah yang pertama!</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading comments:', error);
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Gagal memuat komentar</p>
                    </div>
                `;
            }
        }

        // Display Comments
        function displayComments(comments) {
            const commentContent = document.getElementById('commentContent');

            const commentsHtml = comments.map(comment => `
                <div class="comment-item">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
                            ${comment.user_name.charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-semibold text-gray-900 text-sm">${escapeHtml(comment.user_name)}</span>
                                <span class="text-xs text-gray-500">${comment.time_ago || comment.formatted_date}</span>
                            </div>
                            <p class="text-gray-700 text-sm leading-relaxed break-words">${escapeHtml(comment.comment_text || comment.comment)}</p>
                            <div class="flex items-center space-x-4 mt-2">
                                <button class="text-gray-500 hover:text-red-500 text-sm transition-colors" onclick="likeComment(${comment.id})">
                                    <i class="far fa-heart mr-1"></i>
                                    <span>${comment.likes || 0}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            commentContent.innerHTML = commentsHtml;
        }

        // Submit Comment
        async function submitComment() {
            if (!currentVideoId) return;

            const commentInput = document.getElementById('commentInput');
            const submitBtn = document.getElementById('commentSubmit');

            const comment = commentInput.value.trim();
            if (!comment) {
                showToast('Komentar tidak boleh kosong', 'error');
                return;
            }

            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mengirim...';

                const formData = new FormData();
                formData.append('action', 'add_video_comment');
                formData.append('video_id', currentVideoId);
                formData.append('comment', comment);
                formData.append('user_name', 'Anonymous');

                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    commentInput.value = '';
                    document.getElementById('commentCounter').textContent = '0/500';

                    loadComments(currentVideoId);
                    updateCommentCount(currentVideoId);

                    showToast('Komentar berhasil ditambahkan!', 'success');
                } else {
                    showToast(data.message || 'Gagal menambahkan komentar', 'error');
                }
            } catch (error) {
                console.error('Error submitting comment:', error);
                showToast('Gagal menambahkan komentar', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Kirim';
            }
        }

        // Like Comment
        async function likeComment(commentId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=like_video_comment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `comment_id=${commentId}`
                });

                const data = await response.json();

                if (data.success) {
                    const likeBtn = document.querySelector(`button[onclick="likeComment(${commentId})"] span`);
                    if (likeBtn) {
                        likeBtn.textContent = data.likes;
                    }
                    showToast('Komentar disukai!', 'success');
                } else {
                    showToast('Gagal menyukai komentar', 'error');
                }
            } catch (error) {
                console.error('Error liking comment:', error);
                showToast('Gagal menyukai komentar', 'error');
            }
        }

        // Update Comment Count
        function updateCommentCount(videoId) {
            fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_comments&video_id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const commentCount = data.count || 0;
                        const commentBtns = document.querySelectorAll(`.comment-btn[data-video-id="${videoId}"] .count, .desktop-action-btn.comment-btn[data-video-id="${videoId}"] .count`);
                        commentBtns.forEach(count => {
                            count.textContent = commentCount;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error updating comment count:', error);
                });
        }

        // Helper Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showShareModal(url) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <h3 class="text-lg font-semibold mb-4">Bagikan Video</h3>
                    <div class="mb-4">
                        <input type="text" value="${url}" readonly
                               class="w-full p-2 border rounded bg-gray-50" id="shareUrl">
                    </div>
                    <div class="flex space-x-2 mb-4">
                        <button onclick="copyShareUrl()" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                            <i class="fas fa-copy mr-2"></i>Salin Link
                        </button>
                        <button onclick="shareToWhatsApp('${url}')" class="flex-1 bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                            <i class="fab fa-whatsapp mr-2"></i>WhatsApp
                        </button>
                    </div>
                    <button onclick="closeShareModal()" class="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                        Tutup
                    </button>
                </div>
            `;

            document.body.appendChild(modal);
            document.getElementById('shareUrl').select();
        }

        function copyShareUrl() {
            const urlInput = document.getElementById('shareUrl');
            urlInput.select();
            document.execCommand('copy');
            showToast('Link berhasil disalin!', 'success');
            closeShareModal();
        }

        function shareToWhatsApp(url) {
            const text = encodeURIComponent(`Lihat video menarik ini: ${url}`);
            window.open(`https://wa.me/?text=${text}`, '_blank');
            closeShareModal();
        }

        function closeShareModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
            if (modal) {
                modal.remove();
            }
        }

        // Update comment counter
        function updateCommentCounter() {
            const input = document.getElementById('commentInput');
            const counter = document.getElementById('commentCounter');
            if (input && counter) {
                const length = input.value.length;
                counter.textContent = `${length}/500`;

                // Change color based on length
                if (length > 450) {
                    counter.style.color = '#ef4444'; // red
                } else if (length > 400) {
                    counter.style.color = '#f59e0b'; // yellow
                } else {
                    counter.style.color = '#6b7280'; // gray
                }
            }
        }
    </script>

    <!-- Old desktop button styles removed - using new desktop-action-btn styles -->
</body>
</html>
