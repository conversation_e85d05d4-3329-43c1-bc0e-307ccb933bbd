<?php
// Video Play - Clean TikTok-style video player
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../admin/config.php';
    $pdo = getConnection(); // Get PDO connection
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get video ID from URL
$videoId = $_GET['id'] ?? 1;

// Get videos from database
try {
    $stmt = $pdo->prepare("
        SELECT id, title, description, content, youtube_url, youtube_id,
               video_base64, video_type, video_size, video_format,
               thumbnail, category, tags, duration, status, views, likes,
               shares, comments_count, featured, created_at, updated_at
        FROM videos
        WHERE status = 'published'
        ORDER BY id DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($videos)) {
        // Create sample data if no videos exist
        $videos = [[
            'id' => 1,
            'title' => 'Sample YouTube Video',
            'description' => 'This is a sample video for testing',
            'content' => 'Sample content',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'video_base64' => null,
            'video_type' => 'youtube',
            'video_size' => null,
            'video_format' => null,
            'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            'category' => 'Sample',
            'tags' => 'sample,test',
            'duration' => '03:32',
            'status' => 'published',
            'views' => 100,
            'likes' => 25,
            'shares' => 5,
            'comments_count' => 10,
            'featured' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]];
    }
} catch (Exception $e) {
    die("Database query error: " . $e->getMessage());
}

// Helper functions
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : null;
}

function getYouTubeEmbedUrl($videoId) {
    $params = http_build_query([
        'autoplay' => '0',
        'mute' => '0',
        'controls' => '1',
        'rel' => '0',
        'modestbranding' => '1',
        'enablejsapi' => '1',
        'playsinline' => '1',
        'fs' => '1',
        'cc_load_policy' => '0',
        'iv_load_policy' => '3',
        'origin' => 'http://localhost'
    ]);
    return "https://www.youtube.com/embed/{$videoId}?{$params}";
}

function getVideoSource($video) {
    $video_type = $video['video_type'] ?? 'youtube';

    if ($video_type === 'upload' && !empty($video['video_base64'])) {
        $video_format = $video['video_format'] ?? 'mp4';
        $mimeType = 'video/' . $video_format;
        $dataUrl = 'data:' . $mimeType . ';base64,' . $video['video_base64'];
        
        return [
            'type' => 'upload',
            'src' => $dataUrl,
            'format' => $video_format,
            'size' => $video['video_size'] ?? 0
        ];
    } else {
        $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);
        return [
            'type' => 'youtube',
            'src' => getYouTubeEmbedUrl($youtubeId),
            'youtube_id' => $youtubeId
        ];
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'baru saja';
    if ($time < 3600) return floor($time/60) . ' menit yang lalu';
    if ($time < 86400) return floor($time/3600) . ' jam yang lalu';
    if ($time < 2592000) return floor($time/86400) . ' hari yang lalu';
    if ($time < 31536000) return floor($time/2592000) . ' bulan yang lalu';
    return floor($time/31536000) . ' tahun yang lalu';
}

// Find current video
$currentVideo = null;
foreach ($videos as $video) {
    if ($video['id'] == $videoId) {
        $currentVideo = $video;
        break;
    }
}

if (!$currentVideo) {
    $currentVideo = $videos[0];
}

// Prepare videos with sources
$videosWithSources = [];
foreach ($videos as $video) {
    $videosWithSources[] = [
        'video' => $video,
        'source' => getVideoSource($video)
    ];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?= htmlspecialchars($currentVideo['title']) ?> - Video Player</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjM0I4MkY2Ij4KICA8cGF0aCBkPSJNOCAydjIwbDEzLTEwTDh6Ii8+Cjwvc3ZnPg==">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            overflow: hidden;
        }

        /* Desktop Layout - Mobile-like with 9:16 video ratio */
        .desktop-container {
            width: 100vw;
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
            background: #000;
        }

        .desktop-video-item {
            width: 100vw;
            height: 100vh;
            scroll-snap-align: start;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 120px; /* Add padding for action buttons */
        }

        /* 9:16 Video Container */
        .desktop-video-container {
            width: 400px;  /* Fixed width for 9:16 ratio */
            height: 711px; /* 400 * 16/9 = 711px for 9:16 ratio */
            max-height: 90vh;
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Adjust video container width based on screen height */
        @media (max-height: 800px) {
            .desktop-video-container {
                width: 300px;
                height: 533px; /* 300 * 16/9 */
            }
        }

        @media (min-height: 1000px) {
            .desktop-video-container {
                width: 500px;
                height: 889px; /* 500 * 16/9 */
            }
        }

        /* Desktop Action Buttons - Right side like mobile */
        .desktop-actions {
            position: fixed;
            right: 390px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 30;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .desktop-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            width: 60px;
            height: 60px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .desktop-action-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            color: #000;
            border-color: rgba(255, 255, 255, 0.8);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .desktop-action-btn i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .desktop-action-btn .count {
            font-size: 11px;
            font-weight: 600;
        }

        /* Active states for desktop */
        .desktop-action-btn.liked i {
            color: #ff0050;
        }

        .desktop-action-btn.saved i {
            color: #ffd700;
        }

        /* Desktop Video Info - Bottom left like mobile */
        .desktop-video-info {
            position: absolute;
            left: 20px;
            right: 100px;
            bottom: 20px;
            z-index: 10;
            color: white;
        }

        /* Smooth scrolling for desktop */
        .desktop-container {
            scroll-behavior: smooth;
        }

        /* Desktop video navigation hint */
        .desktop-nav-hint {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 13px;
            z-index: 100;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Video title and description styling */
        .desktop-video-info .video-title {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .video-description {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .video-stats {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
        }

        .desktop-video-info .tag {
            backdrop-filter: blur(10px);
        }

        /* Hide desktop nav hint on mobile */
        @media (max-width: 768px) {
            .desktop-nav-hint {
                display: none;
            }
        }

        /* Mobile Layout */
        .mobile-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
        }

        .video-item {
            width: 100vw;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            background: #000;
        }

        /* Video Styles */
        .video-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .video-element {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* Hide HTML5 video controls */
        .desktop-video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .desktop-video-container video::-webkit-media-controls {
            display: none !important;
        }

        .desktop-video-container video::-webkit-media-controls-enclosure {
            display: none !important;
        }

        .desktop-video-container video::-webkit-media-controls-panel {
            display: none !important;
        }

        .desktop-video-container video::-moz-media-controls {
            display: none !important;
        }

        /* Custom play button overlay */
        .custom-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 15;
        }

        .custom-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .custom-play-btn i {
            font-size: 32px;
            color: #000;
            margin-left: 4px; /* Adjust play icon position */
        }

        .custom-play-btn.playing {
            display: none;
        }

        /* Mobile Action Buttons */
        .mobile-actions {
            position: absolute;
            right: 15px;
            bottom: 100px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(0, 0, 0, 0.7);
            transform: scale(1.1);
        }

        .action-btn i {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .action-btn .count {
            font-size: 12px;
            font-weight: 600;
        }

        /* Active states */
        .action-btn.liked i {
            color: #ff0050;
        }

        .action-btn.saved i {
            color: #ffd700;
        }

        /* Mobile Video Info */
        .mobile-info {
            position: absolute;
            left: 15px;
            right: 80px;
            bottom: 20px;
            z-index: 10;
            color: white;
        }

        .video-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .video-description {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .video-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            opacity: 0.8;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .desktop-container {
                display: none;
            }
        }

        @media (min-width: 769px) {
            .mobile-container {
                display: none;
            }
        }

        /* Comment Modal */
        .comment-modal {
            position: fixed;
            bottom: -100%;
            left: 0;
            right: 0;
            height: 70vh;
            background: white;
            border-radius: 20px 20px 0 0;
            z-index: 1000;
            transition: bottom 0.3s ease;
            display: flex;
            flex-direction: column;
            box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .comment-modal.show {
            bottom: 0;
        }

        .comment-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .comment-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Scrollable comments area styling */
        #commentContent {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        #commentContent::-webkit-scrollbar {
            width: 6px;
        }

        #commentContent::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        #commentContent::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        #commentContent::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Comment item styling */
        .comment-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 12px;
            margin-bottom: 12px;
        }

        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Desktop Layout - Mobile-like with 9:16 videos -->
    <div class="desktop-container">
        <?php foreach ($videosWithSources as $index => $videoData): ?>
            <?php
            $video = $videoData['video'];
            $source = $videoData['source'];
            $vType = $source['type'];
            ?>
            <div class="desktop-video-item" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $vType ?>">
                <!-- 9:16 Video Container -->
                <div class="desktop-video-container">
                    <?php if ($vType === 'upload'): ?>
                        <video class="video-element"
                               playsinline
                               webkit-playsinline
                               muted
                               loop
                               id="desktop-video-<?= $video['id'] ?>"
                               data-video-id="<?= $video['id'] ?>"
                               style="width: 100%; height: 100%; object-fit: cover;">
                            <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                            Your browser does not support the video tag.
                        </video>

                        <!-- Custom Play Button -->
                        <button class="custom-play-btn" id="play-btn-<?= $video['id'] ?>" onclick="toggleDesktopVideo(<?= $video['id'] ?>)">
                            <i class="fas fa-play"></i>
                        </button>
                    <?php else: ?>
                        <iframe class="video-element"
                                src="<?= htmlspecialchars($source['src']) ?>"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                                allowfullscreen
                                style="width: 100%; height: 100%;">
                        </iframe>
                    <?php endif; ?>



                    <!-- Desktop Video Info - Bottom left -->
                    <div class="desktop-video-info">
                        <h3 class="video-title text-lg font-bold mb-2"><?= htmlspecialchars($video['title']) ?></h3>
                        <p class="video-description text-sm opacity-90 mb-3">
                            <?= htmlspecialchars(strlen($video['description']) > 100 ? substr($video['description'], 0, 100) . '...' : $video['description']) ?>
                        </p>
                        <div class="video-stats flex gap-4 text-xs opacity-80">
                            <span class="stat-item view-count">
                                <i class="fas fa-eye mr-1"></i>
                                <?= number_format($video['views']) ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-heart mr-1"></i>
                                <?= number_format($video['likes']) ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock mr-1"></i>
                                <?= $video['duration'] ?? 'N/A' ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-calendar mr-1"></i>
                                <?= timeAgo($video['created_at']) ?>
                            </span>
                        </div>
                        <?php if (!empty($video['tags'])): ?>
                            <div class="video-tags flex flex-wrap gap-2 mt-2">
                                <?php foreach (explode(',', $video['tags']) as $tag): ?>
                                    <span class="tag bg-white bg-opacity-20 px-2 py-1 rounded text-xs">
                                        #<?= trim(htmlspecialchars($tag)) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Desktop Action Buttons - Outside video container -->
                <div class="desktop-actions">
                    <button class="desktop-action-btn like-btn" data-video-id="<?= $video['id'] ?>" onclick="handleLike(<?= $video['id'] ?>)">
                        <i class="far fa-heart"></i>
                        <span class="count"><?= number_format($video['likes']) ?></span>
                    </button>

                    <button class="desktop-action-btn comment-btn" data-video-id="<?= $video['id'] ?>" onclick="handleComment(<?= $video['id'] ?>)">
                        <i class="far fa-comment"></i>
                        <span class="count"><?= $video['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="desktop-action-btn save-btn" data-video-id="<?= $video['id'] ?>" onclick="handleSave(<?= $video['id'] ?>)">
                        <i class="far fa-bookmark"></i>
                        <span class="count">Simpan</span>
                    </button>

                    <button class="desktop-action-btn share-btn" data-video-id="<?= $video['id'] ?>" onclick="handleShare(<?= $video['id'] ?>)">
                        <i class="fas fa-share"></i>
                        <span class="count"><?= number_format($video['shares']) ?></span>
                    </button>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Desktop Navigation Hint -->
        <div class="desktop-nav-hint">
            <i class="fas fa-mouse mr-2"></i>
            Scroll untuk video berikutnya
        </div>
    </div>
            <div class="mb-6">
                <h1 class="text-xl font-bold mb-3"><?= htmlspecialchars($currentVideo['title']) ?></h1>
                <p class="text-gray-300 text-sm mb-4"><?= htmlspecialchars($currentVideo['description']) ?></p>

                <!-- Desktop Action Buttons -->
                <div class="flex gap-4 mb-4">
                    <button class="action-btn-desktop like-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleLike(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-heart mr-2"></i>
                        <span class="count"><?= number_format($currentVideo['likes']) ?></span>
                    </button>

                    <button class="action-btn-desktop comment-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleComment(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-comment mr-2"></i>
                        <span class="count"><?= $currentVideo['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="action-btn-desktop save-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleSave(<?= $currentVideo['id'] ?>)">
                        <i class="far fa-bookmark mr-2"></i>
                        Simpan
                    </button>

                    <button class="action-btn-desktop share-btn" data-video-id="<?= $currentVideo['id'] ?>" onclick="handleShare(<?= $currentVideo['id'] ?>)">
                        <i class="fas fa-share mr-2"></i>
                        <span class="count"><?= number_format($currentVideo['shares']) ?></span>
                    </button>
                </div>

                <!-- Video Stats -->
                <div class="text-sm text-gray-400 space-y-2">
                    <div class="flex items-center gap-4">
                        <span><i class="fas fa-eye mr-1"></i> <?= number_format($currentVideo['views']) ?></span>
                        <span><i class="fas fa-clock mr-1"></i> <?= $currentVideo['duration'] ?? 'N/A' ?></span>
                    </div>
                    <div>
                        <i class="fas fa-calendar mr-1"></i> <?= timeAgo($currentVideo['created_at']) ?>
                    </div>
                    <?php if (!empty($currentVideo['tags'])): ?>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <?php foreach (explode(',', $currentVideo['tags']) as $tag): ?>
                                <span class="bg-gray-700 px-2 py-1 rounded text-xs">#<?= trim(htmlspecialchars($tag)) ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Video List -->
            <div class="border-t border-gray-700 pt-4">
                <h3 class="font-semibold mb-3">Video Lainnya</h3>
                <div class="space-y-3">
                    <?php foreach ($videos as $video): ?>
                        <?php if ($video['id'] != $currentVideo['id']): ?>
                            <a href="video-play.php?id=<?= $video['id'] ?>" class="block hover:bg-gray-800 p-2 rounded">
                                <div class="flex gap-3">
                                    <div class="w-20 h-12 bg-gray-700 rounded flex-shrink-0 flex items-center justify-center">
                                        <i class="fas fa-play text-white"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium truncate"><?= htmlspecialchars($video['title']) ?></h4>
                                        <p class="text-xs text-gray-400 mt-1"><?= number_format($video['views']) ?> views</p>
                                    </div>
                                </div>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Layout -->
    <div class="mobile-container">
        <?php foreach ($videosWithSources as $index => $videoData): ?>
            <?php
            $video = $videoData['video'];
            $source = $videoData['source'];
            $vType = $source['type'];
            ?>
            <div class="video-item" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $vType ?>">
                <div class="video-container">
                    <?php if ($vType === 'upload'): ?>
                        <video class="video-element"
                               playsinline
                               webkit-playsinline
                               muted
                               loop
                               id="mobile-video-<?= $video['id'] ?>"
                               onclick="togglePlay(<?= $video['id'] ?>)">
                            <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                        </video>
                    <?php else: ?>
                        <iframe class="video-element"
                                src="<?= htmlspecialchars($source['src']) ?>"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                                allowfullscreen>
                        </iframe>
                    <?php endif; ?>
                </div>

                <!-- Mobile Actions -->
                <div class="mobile-actions">
                    <button class="action-btn like-btn" data-video-id="<?= $video['id'] ?>" onclick="handleLike(<?= $video['id'] ?>)">
                        <i class="far fa-heart"></i>
                        <span class="count"><?= number_format($video['likes']) ?></span>
                    </button>

                    <button class="action-btn comment-btn" data-video-id="<?= $video['id'] ?>" onclick="handleComment(<?= $video['id'] ?>)">
                        <i class="far fa-comment"></i>
                        <span class="count"><?= $video['comments_count'] ?? 0 ?></span>
                    </button>

                    <button class="action-btn save-btn" data-video-id="<?= $video['id'] ?>" onclick="handleSave(<?= $video['id'] ?>)">
                        <i class="far fa-bookmark"></i>
                        <span class="count">Simpan</span>
                    </button>

                    <button class="action-btn share-btn" data-video-id="<?= $video['id'] ?>" onclick="handleShare(<?= $video['id'] ?>)">
                        <i class="fas fa-share"></i>
                        <span class="count"><?= number_format($video['shares']) ?></span>
                    </button>
                </div>

                <!-- Mobile Info -->
                <div class="mobile-info">
                    <h3 class="video-title"><?= htmlspecialchars($video['title']) ?></h3>
                    <p class="video-description"><?= htmlspecialchars(strlen($video['description']) > 80 ? substr($video['description'], 0, 80) . '...' : $video['description']) ?></p>
                    <div class="video-stats">
                        <span class="stat-item">
                            <i class="fas fa-eye"></i>
                            <?= number_format($video['views']) ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-heart"></i>
                            <?= number_format($video['likes']) ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-clock"></i>
                            <?= $video['duration'] ?? 'N/A' ?>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-calendar"></i>
                            <?= timeAgo($video['created_at']) ?>
                        </span>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Comment Modal -->
    <div class="comment-overlay" id="commentOverlay" onclick="closeCommentModal()"></div>
    <div class="comment-modal" id="commentModal">
        <div class="p-4 border-b">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Komentar</h3>
                <button onclick="closeCommentModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Scrollable Comments Area -->
        <div class="flex-1 overflow-y-auto p-4 min-h-[300px] max-h-[400px]" id="commentContent" style="max-height: 400px; overflow-y: auto;">
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-comments text-3xl mb-2"></i>
                <p>Belum ada komentar</p>
                <p class="text-sm">Jadilah yang pertama berkomentar!</p>
            </div>
        </div>

        <!-- Comment Form - Fixed at Bottom -->
        <div class="p-4 border-t bg-gray-50">
            <div class="flex gap-3">
                <input type="text"
                       id="commentInput"
                       placeholder="Tulis komentar..."
                       class="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       maxlength="500"
                       oninput="updateCommentCounter()"
                       onkeypress="if(event.key==='Enter') submitComment()">
                <button onclick="submitComment()"
                        id="commentSubmit"
                        class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="flex justify-between items-center mt-2">
                <div class="text-xs text-gray-500">
                    <span id="commentCounter">0/500</span>
                </div>
                <div class="text-xs text-gray-400">
                    Tekan Enter untuk kirim
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentVideoId = null;

        // Global function declarations (hoisted) - Define these first
        function handleLike(videoId) {
            console.log('handleLike called with videoId:', videoId);
            toggleLike(videoId);
        }

        function handleComment(videoId) {
            console.log('handleComment called with videoId:', videoId);
            currentVideoId = videoId;
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            if (overlay && modal) {
                overlay.classList.add('show');
                modal.classList.add('show');
                loadComments(videoId);
            } else {
                console.error('Comment modal elements not found');
            }
        }

        function handleSave(videoId) {
            console.log('handleSave called with videoId:', videoId);
            toggleSave(videoId);
        }

        async function handleShare(videoId) {
            console.log('handleShare called with videoId:', videoId);
            try {
                // Increment share count first
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}&platform=direct`
                });

                const data = await response.json();

                if (data.success) {
                    // Update share count (both mobile and desktop)
                    const shareButtons = document.querySelectorAll(`.share-btn[data-video-id="${videoId}"], .desktop-action-btn.share-btn[data-video-id="${videoId}"]`);
                    shareButtons.forEach(btn => {
                        const count = btn.querySelector('.count');
                        if (count) {
                            count.textContent = formatNumber(data.shares);
                        }
                    });
                }

                // Show share modal
                const shareUrl = `${window.location.origin}${window.location.pathname}?id=${videoId}`;
                showShareModal(shareUrl);

            } catch (error) {
                console.error('Error sharing video:', error);
                // Still show share modal even if increment fails
                const shareUrl = `${window.location.origin}${window.location.pathname}?id=${videoId}`;
                showShareModal(shareUrl);
            }
        }

        // Toggle Like function
        async function toggleLike(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=toggle_video_like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();

                if (data.success) {
                    // Update all like buttons for this video (both mobile and desktop)
                    const likeButtons = document.querySelectorAll(`.like-btn[data-video-id="${videoId}"], .desktop-action-btn.like-btn[data-video-id="${videoId}"]`);
                    likeButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const count = btn.querySelector('.count');

                        if (data.is_liked) {
                            icon.className = 'fas fa-heart';
                            btn.classList.add('liked');
                        } else {
                            icon.className = 'far fa-heart';
                            btn.classList.remove('liked');
                        }

                        if (count) {
                            count.textContent = formatNumber(data.likes);
                        }
                    });

                    showToast(data.is_liked ? 'Video disukai!' : 'Like dibatalkan', 'success');
                } else {
                    showToast('Gagal menyukai video', 'error');
                }
            } catch (error) {
                console.error('Error toggling like:', error);
                showToast('Gagal menyukai video', 'error');
            }
        }

        // Toggle Save function
        async function toggleSave(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=toggle_video_save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `video_id=${videoId}`
                });

                const data = await response.json();

                if (data.success) {
                    // Update all save buttons for this video (both mobile and desktop)
                    const saveButtons = document.querySelectorAll(`.save-btn[data-video-id="${videoId}"], .desktop-action-btn.save-btn[data-video-id="${videoId}"]`);
                    saveButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        const text = btn.querySelector('.count');

                        if (data.is_saved) {
                            icon.className = 'fas fa-bookmark';
                            btn.classList.add('saved');
                            if (text) text.textContent = 'Tersimpan';
                        } else {
                            icon.className = 'far fa-bookmark';
                            btn.classList.remove('saved');
                            if (text) text.textContent = 'Simpan';
                        }
                    });

                    showToast(data.is_saved ? 'Video disimpan!' : 'Video dihapus dari simpanan', 'success');
                } else {
                    showToast('Gagal menyimpan video', 'error');
                }
            } catch (error) {
                console.error('Error toggling save:', error);
                showToast('Gagal menyimpan video', 'error');
            }
        }

        // Initialize view count and auto-increment on page load
        document.addEventListener('DOMContentLoaded', function() {
            const videoId = <?= $currentVideo['id'] ?>;

            // Increment view immediately on page load
            incrementView(videoId);

            // Initialize view tracking for both mobile and desktop
            initializeViewTracking();
            initializeDesktopViewTracking();

            // Initialize desktop video click handlers
            initializeDesktopVideoHandlers();

            // Initialize comment input
            const commentInput = document.getElementById('commentInput');
            const commentSubmit = document.getElementById('commentSubmit');
            const commentCounter = document.getElementById('commentCounter');

            if (commentInput && commentSubmit && commentCounter) {
                commentInput.addEventListener('input', function() {
                    const length = this.value.length;
                    commentCounter.textContent = `${length}/500`;

                    if (length > 0 && length <= 500) {
                        commentSubmit.disabled = false;
                        commentSubmit.classList.remove('opacity-50');
                    } else {
                        commentSubmit.disabled = true;
                        commentSubmit.classList.add('opacity-50');
                    }
                });

                commentInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!commentSubmit.disabled) {
                            submitComment();
                        }
                    }
                });
            }
        });

        // Initialize view tracking for mobile scroll
        function initializeViewTracking() {
            const isMobile = window.innerWidth < 768;
            if (!isMobile) return;

            const mobileContainer = document.querySelector('.mobile-container');
            if (!mobileContainer) return;

            let viewedVideos = new Set();

            // Intersection Observer for tracking video views
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        const videoItem = entry.target;
                        const videoId = videoItem.dataset.videoId;

                        if (videoId && !viewedVideos.has(videoId)) {
                            viewedVideos.add(videoId);
                            incrementView(videoId);
                            console.log('📱 Mobile view tracked for video:', videoId);
                        }
                    }
                });
            }, {
                threshold: 0.5 // Trigger when 50% of video is visible
            });

            // Observe all video items
            const videoItems = document.querySelectorAll('.video-item');
            videoItems.forEach(item => observer.observe(item));
        }

        // Initialize desktop view tracking
        function initializeDesktopViewTracking() {
            const isDesktop = window.innerWidth >= 769;
            if (!isDesktop) return;

            const desktopContainer = document.querySelector('.desktop-video-main');
            if (!desktopContainer) return;

            let viewedVideos = new Set();

            // Intersection Observer for desktop video tracking
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        const videoItem = entry.target;
                        const videoId = videoItem.dataset.videoId;

                        if (videoId && !viewedVideos.has(videoId)) {
                            viewedVideos.add(videoId);
                            incrementView(videoId);
                            console.log('🖥️ Desktop view tracked for video:', videoId);
                        }
                    }
                });
            }, {
                threshold: 0.5
            });

            // Observe all desktop video items
            const desktopVideoItems = document.querySelectorAll('.desktop-video-item');
            desktopVideoItems.forEach(item => observer.observe(item));
        }

        // Handle desktop video click
        function handleDesktopVideoClick(videoId) {
            console.log('🖥️ Desktop video clicked:', videoId);
            toggleDesktopVideo(videoId);
        }

        // Toggle Desktop Video Play/Pause
        function toggleDesktopVideo(videoId) {
            const video = document.getElementById(`desktop-video-${videoId}`);
            const playBtn = document.getElementById(`play-btn-${videoId}`);

            if (!video) return;

            if (video.paused) {
                // Play video
                video.play().then(() => {
                    console.log('🖥️ Desktop video playing:', videoId);
                    if (playBtn) {
                        playBtn.classList.add('playing');
                        playBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    }

                    // Pause other videos
                    pauseOtherDesktopVideos(videoId);
                }).catch(error => {
                    console.error('Error playing desktop video:', error);
                });
            } else {
                // Pause video
                video.pause();
                console.log('🖥️ Desktop video paused:', videoId);
                if (playBtn) {
                    playBtn.classList.remove('playing');
                    playBtn.innerHTML = '<i class="fas fa-play"></i>';
                }
            }
        }

        // Pause other desktop videos
        function pauseOtherDesktopVideos(currentVideoId) {
            const allVideos = document.querySelectorAll('[id^="desktop-video-"]');
            allVideos.forEach(video => {
                const videoId = video.dataset.videoId;
                if (videoId !== currentVideoId && !video.paused) {
                    video.pause();
                    const playBtn = document.getElementById(`play-btn-${videoId}`);
                    if (playBtn) {
                        playBtn.classList.remove('playing');
                        playBtn.innerHTML = '<i class="fas fa-play"></i>';
                    }
                }
            });
        }

        // Initialize desktop video handlers
        function initializeDesktopVideoHandlers() {
            const isDesktop = window.innerWidth >= 769;
            if (!isDesktop) return;

            // Add click handlers to desktop videos
            const desktopVideos = document.querySelectorAll('[id^="desktop-video-"]');
            desktopVideos.forEach(video => {
                video.addEventListener('click', function() {
                    const videoId = this.dataset.videoId;
                    if (videoId) {
                        toggleDesktopVideo(videoId);
                    }
                });
            });
        }

        // Increment view count
        async function incrementView(videoId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=increment_video_view', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `video_id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ View incremented for video:', videoId, 'New count:', data.views);

                    // Update view count in UI if available
                    updateViewCount(videoId, data.views);
                }
            } catch (error) {
                console.error('Error incrementing view:', error);
            }
        }

        // Update view count in UI
        function updateViewCount(videoId, newCount) {
            const viewElements = document.querySelectorAll(`[data-video-id="${videoId}"] .view-count, .video-item[data-video-id="${videoId}"] .stat-item:first-child`);
            viewElements.forEach(element => {
                if (element.textContent.includes('👁') || element.querySelector('.fa-eye')) {
                    const icon = element.querySelector('i') ? element.querySelector('i').outerHTML : '';
                    element.innerHTML = icon + ' ' + formatNumber(newCount);
                }
            });
        }

        // Toggle video play (mobile)
        function togglePlay(videoId) {
            const video = document.getElementById(`mobile-video-${videoId}`);
            if (video) {
                if (video.paused) {
                    video.play();
                } else {
                    video.pause();
                }
            }
        }

        // All handler functions moved to top of script

        // Handle Share function moved to top of script

        // Handle Comment
        function handleComment(videoId) {
            currentVideoId = videoId;
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.add('show');
            modal.classList.add('show');

            loadComments(videoId);
        }

        // Close Comment Modal
        function closeCommentModal() {
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.remove('show');
            modal.classList.remove('show');

            // Clear input
            const commentInput = document.getElementById('commentInput');
            if (commentInput) {
                commentInput.value = '';
                document.getElementById('commentCounter').textContent = '0/500';
            }
        }

        // Load Comments
        async function loadComments(videoId) {
            const commentContent = document.getElementById('commentContent');

            try {
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Memuat komentar...</p>
                    </div>
                `;

                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_comments&video_id=${videoId}`);
                const data = await response.json();

                if (data.success && data.comments && data.comments.length > 0) {
                    displayComments(data.comments);
                } else {
                    commentContent.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-comment text-2xl mb-2"></i>
                            <p>Belum ada komentar. Jadilah yang pertama!</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading comments:', error);
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Gagal memuat komentar</p>
                    </div>
                `;
            }
        }

        // Display Comments
        function displayComments(comments) {
            const commentContent = document.getElementById('commentContent');

            const commentsHtml = comments.map(comment => `
                <div class="comment-item">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
                            ${comment.user_name.charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-semibold text-gray-900 text-sm">${escapeHtml(comment.user_name)}</span>
                                <span class="text-xs text-gray-500">${comment.time_ago || comment.formatted_date}</span>
                            </div>
                            <p class="text-gray-700 text-sm leading-relaxed break-words">${escapeHtml(comment.comment_text || comment.comment)}</p>
                            <div class="flex items-center space-x-4 mt-2">
                                <button class="text-gray-500 hover:text-red-500 text-sm transition-colors" onclick="likeComment(${comment.id})">
                                    <i class="far fa-heart mr-1"></i>
                                    <span>${comment.likes || 0}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            commentContent.innerHTML = commentsHtml;
        }

        // Submit Comment
        async function submitComment() {
            if (!currentVideoId) return;

            const commentInput = document.getElementById('commentInput');
            const submitBtn = document.getElementById('commentSubmit');

            const comment = commentInput.value.trim();
            if (!comment) {
                showToast('Komentar tidak boleh kosong', 'error');
                return;
            }

            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mengirim...';

                const formData = new FormData();
                formData.append('action', 'add_video_comment');
                formData.append('video_id', currentVideoId);
                formData.append('comment', comment);
                formData.append('user_name', 'Anonymous');

                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    commentInput.value = '';
                    document.getElementById('commentCounter').textContent = '0/500';

                    loadComments(currentVideoId);
                    updateCommentCount(currentVideoId);

                    showToast('Komentar berhasil ditambahkan!', 'success');
                } else {
                    showToast(data.message || 'Gagal menambahkan komentar', 'error');
                }
            } catch (error) {
                console.error('Error submitting comment:', error);
                showToast('Gagal menambahkan komentar', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Kirim';
            }
        }

        // Like Comment
        async function likeComment(commentId) {
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=like_video_comment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `comment_id=${commentId}`
                });

                const data = await response.json();

                if (data.success) {
                    const likeBtn = document.querySelector(`button[onclick="likeComment(${commentId})"] span`);
                    if (likeBtn) {
                        likeBtn.textContent = data.likes;
                    }
                    showToast('Komentar disukai!', 'success');
                } else {
                    showToast('Gagal menyukai komentar', 'error');
                }
            } catch (error) {
                console.error('Error liking comment:', error);
                showToast('Gagal menyukai komentar', 'error');
            }
        }

        // Update Comment Count
        function updateCommentCount(videoId) {
            fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_video_comments&video_id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const commentCount = data.count || 0;
                        const commentBtns = document.querySelectorAll(`.comment-btn[data-video-id="${videoId}"] .count, .desktop-action-btn.comment-btn[data-video-id="${videoId}"] .count`);
                        commentBtns.forEach(count => {
                            count.textContent = commentCount;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error updating comment count:', error);
                });
        }

        // Helper Functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showShareModal(url) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <h3 class="text-lg font-semibold mb-4">Bagikan Video</h3>
                    <div class="mb-4">
                        <input type="text" value="${url}" readonly
                               class="w-full p-2 border rounded bg-gray-50" id="shareUrl">
                    </div>
                    <div class="flex space-x-2 mb-4">
                        <button onclick="copyShareUrl()" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                            <i class="fas fa-copy mr-2"></i>Salin Link
                        </button>
                        <button onclick="shareToWhatsApp('${url}')" class="flex-1 bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                            <i class="fab fa-whatsapp mr-2"></i>WhatsApp
                        </button>
                    </div>
                    <button onclick="closeShareModal()" class="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                        Tutup
                    </button>
                </div>
            `;

            document.body.appendChild(modal);
            document.getElementById('shareUrl').select();
        }

        function copyShareUrl() {
            const urlInput = document.getElementById('shareUrl');
            urlInput.select();
            document.execCommand('copy');
            showToast('Link berhasil disalin!', 'success');
            closeShareModal();
        }

        function shareToWhatsApp(url) {
            const text = encodeURIComponent(`Lihat video menarik ini: ${url}`);
            window.open(`https://wa.me/?text=${text}`, '_blank');
            closeShareModal();
        }

        function closeShareModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
            if (modal) {
                modal.remove();
            }
        }

        // Update comment counter
        function updateCommentCounter() {
            const input = document.getElementById('commentInput');
            const counter = document.getElementById('commentCounter');
            if (input && counter) {
                const length = input.value.length;
                counter.textContent = `${length}/500`;

                // Change color based on length
                if (length > 450) {
                    counter.style.color = '#ef4444'; // red
                } else if (length > 400) {
                    counter.style.color = '#f59e0b'; // yellow
                } else {
                    counter.style.color = '#6b7280'; // gray
                }
            }
        }
    </script>

    <!-- Old desktop button styles removed - using new desktop-action-btn styles -->
</body>
</html>
