-- Create saved_news table for bookmark functionality
-- Run this SQL to create the table for saving bookmarked news

USE react_news;

-- Create saved_news table
CREATE TABLE IF NOT EXISTS saved_news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    news_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (news_id) REFERENCES posts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_news (user_id, news_id)
);

-- Show table structure
DESCRIBE saved_news;

-- Show success message
SELECT 'Saved news table created successfully!' as message;
