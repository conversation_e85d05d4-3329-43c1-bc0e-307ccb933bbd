<?php
// Include the main config file which has the proper database connection
require_once __DIR__ . '/config.php';

// Function to send JSON response
function sendJsonResponse($data) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
    
    echo json_encode($data);
    exit();
}

// Note: getSetting, updateSetting and other database functions are now defined in config.php
?>
