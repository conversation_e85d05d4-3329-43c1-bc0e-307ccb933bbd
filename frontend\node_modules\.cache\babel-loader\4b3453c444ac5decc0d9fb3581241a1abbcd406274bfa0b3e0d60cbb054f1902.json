{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme, createTheme, ThemeProvider } from '@mui/material/styles';\nimport Drawer from '@mui/material/Drawer';\nimport Button from '@mui/material/Button';\nimport Stack from '@mui/material/Stack';\nimport Divider from '@mui/material/Divider';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  // Data News Component - Fixed Version\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [themeSettings, setThemeSettings] = useState({\n    primary_color: '#3B82F6',\n    secondary_color: '#10B981',\n    accent_color: '#F59E0B'\n  });\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: 'React News Portal'\n  });\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n    // Fetch kostum data\n    fetchKostumData();\n    // Load theme settings\n    loadThemeSettings();\n  }, [id]);\n\n  // Load theme settings from admin\n  const loadThemeSettings = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const text = await response.text();\n      console.log('Raw theme response:', text);\n      let data;\n      try {\n        data = JSON.parse(text);\n      } catch (e) {\n        console.error('JSON parse error:', e);\n        console.error('Response text:', text);\n        throw new Error('Invalid JSON response');\n      }\n      if (data.success && data.data) {\n        setThemeSettings({\n          primary_color: data.data.warna_primary || '#3B82F6',\n          secondary_color: data.data.warna_sidebar || '#10B981',\n          accent_color: data.data.warna_sidebar_header || '#F59E0B'\n        });\n        console.log('🎨 Theme settings loaded:', data.data);\n      }\n    } catch (error) {\n      console.error('❌ Error loading theme settings:', error);\n    }\n  };\n  const fetchKostumData = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      const data = await response.json();\n      if (data.success && data.data) {\n        const settings = data.data;\n        setKostum({\n          logo: settings.logo_data || settings.logo_file_path || '',\n          title: settings.nama_website || 'React News Portal'\n        });\n        console.log('🎨 Kostum data loaded:', {\n          logo: settings.logo_data ? 'Has logo data' : 'No logo data',\n          title: settings.nama_website\n        });\n      }\n    } catch (error) {\n      // Fallback to default values if API fails\n      setKostum({\n        logo: '',\n        title: 'React News Portal'\n      });\n    }\n  };\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // Fetch news detail (views already incremented from landing page)\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n    console.log('data-news.js - Processing image path:', imagePath);\n\n    // If it's base64 data (for logos), return as is\n    if (imagePath.startsWith('data:image/')) {\n      console.log('data-news.js - Using base64 data');\n      return imagePath;\n    }\n\n    // Jika sudah URL lengkap, gunakan langsung\n    if (imagePath.startsWith('http')) {\n      console.log('data-news.js - Using full URL:', imagePath);\n      return imagePath;\n    }\n\n    // If it's already the correct path, use it directly\n    if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n      const finalUrl = `http://localhost${imagePath}`;\n      console.log('data-news.js - Using direct path:', finalUrl);\n      return finalUrl;\n    }\n\n    // Extract filename from any path format\n    let filename = '';\n    if (imagePath.startsWith('/react-news/uploads/')) {\n      filename = imagePath.replace('/react-news/uploads/', '');\n    } else if (imagePath.startsWith('/uploads/')) {\n      filename = imagePath.replace('/uploads/', '');\n    } else if (imagePath.startsWith('assets/news/')) {\n      filename = imagePath.replace('assets/news/', '');\n    } else if (!imagePath.includes('/')) {\n      // Just filename\n      filename = imagePath;\n    } else {\n      // Extract filename from any other path\n      filename = imagePath.split('/').pop();\n    }\n\n    // Use consistent frontend/uploads path\n    const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n    console.log('data-news.js - Final URL:', finalUrl);\n    return finalUrl;\n  };\n\n  // Create custom theme based on admin settings\n  const customTheme = createTheme({\n    palette: {\n      primary: {\n        main: themeSettings.primary_color,\n        dark: themeSettings.secondary_color,\n        light: themeSettings.accent_color\n      },\n      secondary: {\n        main: themeSettings.secondary_color\n      }\n    }\n  });\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\",\n          style: {\n            borderBottomColor: themeSettings.primary_color\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color,\n            ':hover': {\n              backgroundColor: themeSettings.secondary_color\n            }\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: customTheme,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        bgcolor: 'gray.50',\n        width: '100vw',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AppBar, {\n        position: \"fixed\",\n        color: \"inherit\",\n        elevation: 1,\n        sx: {\n          bgcolor: '#fff',\n          borderBottom: 1,\n          borderColor: 'grey.200',\n          zIndex: 1301\n        },\n        children: /*#__PURE__*/_jsxDEV(Toolbar, {\n          sx: {\n            minHeight: {\n              xs: 70,\n              md: 80\n            },\n            px: {\n              xs: 2,\n              md: 6\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n              alt: \"Logo\",\n              sx: {\n                width: 48,\n                height: 48,\n                mr: 2\n              },\n              onError: e => {\n                e.target.src = '/logo192.png';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 700,\n                color: 'primary.main',\n                fontSize: {\n                  xs: 22,\n                  md: 28\n                }\n              },\n              children: kostum.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            color: \"primary\",\n            onClick: () => navigate('/'),\n            sx: {\n              mr: 1\n            },\n            title: \"Kembali ke Beranda\",\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            color: \"primary\",\n            onClick: () => navigate('/video'),\n            sx: {\n              mr: 1\n            },\n            title: \"Video\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-play-circle\",\n              style: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            color: \"primary\",\n            onClick: handleSidebar,\n            sx: {\n              ml: 1\n            },\n            title: \"Menu\",\n            children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        anchor: \"right\",\n        open: sidebarOpen,\n        onClose: handleSidebarClose,\n        ModalProps: {\n          keepMounted: true\n        },\n        sx: {\n          zIndex: 2000\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 260,\n            p: 3,\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleSidebarClose,\n            sx: {\n              position: 'absolute',\n              top: 8,\n              right: 8,\n              zIndex: 10\n            },\n            \"aria-label\": \"Tutup\",\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n              alt: \"Logo\",\n              sx: {\n                width: 32,\n                height: 32,\n                mr: 1\n              },\n              onError: e => {\n                e.target.src = '/logo192.png';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 700,\n                color: 'primary.main',\n                fontSize: 20\n              },\n              children: kostum.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: 'text.secondary',\n                mb: 1.5,\n                fontSize: 12,\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1.5,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"text\",\n                fullWidth: true,\n                sx: {\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                },\n                onClick: () => {\n                  handleSidebarClose();\n                  navigate('/');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-home\",\n                  style: {\n                    marginRight: 8,\n                    fontSize: 14\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 33\n                }, this), \"Beranda\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"text\",\n                fullWidth: true,\n                sx: {\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                },\n                onClick: () => {\n                  handleSidebarClose();\n                  navigate('/saved');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-bookmark\",\n                  style: {\n                    marginRight: 8,\n                    fontSize: 14\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 33\n                }, this), \"Berita Tersimpan\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"text\",\n                fullWidth: true,\n                sx: {\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                },\n                onClick: () => {\n                  handleSidebarClose();\n                  navigate('/video');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-play-circle\",\n                  style: {\n                    marginRight: 8,\n                    fontSize: 14\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 33\n                }, this), \"Video\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: 'text.secondary',\n                mb: 1.5,\n                fontSize: 12,\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: \"Navigation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 2,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"primary\",\n                fullWidth: true,\n                sx: {\n                  textTransform: 'none',\n                  borderColor: 'primary.main',\n                  color: 'primary.main',\n                  '&:hover': {\n                    borderColor: 'primary.main',\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                onClick: () => {\n                  handleSidebarClose();\n                  navigate('/');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-left\",\n                  style: {\n                    marginRight: 8,\n                    fontSize: 14\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 33\n                }, this), \"Kembali ke Beranda\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          bgcolor: 'white',\n          boxShadow: 1,\n          borderBottom: 1,\n          borderColor: 'grey.200',\n          mt: {\n            xs: '70px',\n            md: '80px'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxWidth: '1200px',\n            mx: 'auto',\n            px: 2,\n            py: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              fontSize: '14px',\n              color: 'text.secondary'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"button\",\n              onClick: handleBackToHome,\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                color: 'text.secondary',\n                '&:hover': {\n                  color: 'primary.main'\n                },\n                transition: 'color 0.2s',\n                border: 'none',\n                background: 'none',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-home\",\n                style: {\n                  marginRight: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 29\n              }, this), \"Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-right\",\n              style: {\n                fontSize: '12px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: 'text.primary'\n              },\n              children: \"Detail Berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 2,\n          py: 4,\n          pb: {\n            xs: 12,\n            md: 4\n          } // More padding on mobile for bottom nav\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'grid',\n            gridTemplateColumns: {\n              xs: '1fr',\n              lg: '2fr 1fr'\n            },\n            gap: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(\"article\", {\n              className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(news.image),\n                  alt: news.title,\n                  className: \"w-full h-64 md:h-80 object-cover\",\n                  onError: e => {\n                    e.target.src = 'https://picsum.photos/800/400?random=2';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 left-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white px-3 py-1 rounded-full text-sm font-medium\",\n                    style: {\n                      backgroundColor: themeSettings.primary_color\n                    },\n                    children: news.category_name || 'Berita'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 37\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 md:p-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                  children: news.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-calendar-alt mr-2\",\n                      style: {\n                        color: themeSettings.primary_color\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 41\n                    }, this), formatDate(news.created_at || news.date)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-clock mr-2\",\n                      style: {\n                        color: themeSettings.primary_color\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 41\n                    }, this), formatTime(news.created_at || news.date)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-eye mr-2\",\n                      style: {\n                        color: themeSettings.primary_color\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 41\n                    }, this), news.views || 0, \" views\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"prose prose-lg max-w-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                    dangerouslySetInnerHTML: {\n                      __html: news.content.replace(/\\n/g, '<br>')\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 37\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-8 pt-6 border-t\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Bagikan Artikel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                      style: {\n                        backgroundColor: themeSettings.primary_color\n                      },\n                      onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n                      onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fab fa-facebook-f mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 617,\n                        columnNumber: 45\n                      }, this), \"Facebook\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 41\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                      style: {\n                        backgroundColor: themeSettings.accent_color\n                      },\n                      onMouseEnter: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                      onMouseLeave: e => e.target.style.backgroundColor = themeSettings.accent_color,\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fab fa-twitter mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 45\n                      }, this), \"Twitter\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 41\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fab fa-whatsapp mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 45\n                      }, this), \"WhatsApp\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 41\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-newspaper mr-2\",\n                  style: {\n                    color: themeSettings.primary_color\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 37\n                }, this), \"Berita Terkait\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleRelatedNewsClick(item.id),\n                  className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getImageUrl(item.image),\n                    alt: item.title,\n                    className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                    onError: e => {\n                      e.target.src = 'https://picsum.photos/150/150?random=3';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                      children: item.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: formatDate(item.created_at || item.date)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 45\n                  }, this)]\n                }, item.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                bgcolor: 'white',\n                borderRadius: 2,\n                boxShadow: 2,\n                p: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                component: \"button\",\n                onClick: handleBackToHome,\n                sx: {\n                  width: '100%',\n                  bgcolor: 'primary.main',\n                  color: 'white',\n                  py: 1.5,\n                  px: 2,\n                  borderRadius: 2,\n                  border: 'none',\n                  cursor: 'pointer',\n                  fontWeight: 500,\n                  transition: 'background-color 0.2s',\n                  '&:hover': {\n                    bgcolor: 'primary.dark'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-left\",\n                  style: {\n                    marginRight: '8px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 33\n                }, this), \"Kembali ke Beranda\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: 1300,\n          display: {\n            xs: 'block',\n            md: 'none'\n          },\n          // Hide on desktop\n          backgroundColor: 'white',\n          borderTop: '1px solid #e0e0e0',\n          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-around',\n            alignItems: 'center',\n            height: 64,\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => navigate('/'),\n            className: \"bottom-nav-item active\",\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home bottom-nav-icon\",\n              style: {\n                color: themeSettings.primary_color\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: 'primary.main'\n              },\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => navigate('/'),\n            className: \"bottom-nav-item\",\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-search bottom-nav-icon text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: 'text.secondary'\n              },\n              children: \"Cari\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => navigate('/saved'),\n            className: \"bottom-nav-item\",\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: 'text.secondary'\n              },\n              children: \"Simpan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"sDau4pGRAX1/GyVsTHECO1kU39M=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "IconButton", "MenuIcon", "CloseIcon", "ArrowBackIcon", "useMediaQuery", "useTheme", "createTheme", "ThemeProvider", "Drawer", "<PERSON><PERSON>", "<PERSON><PERSON>", "Divider", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "theme", "isDesktop", "breakpoints", "up", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "themeSettings", "setThemeSettings", "primary_color", "secondary_color", "accent_color", "kostum", "setKostum", "logo", "title", "sidebarOpen", "setSidebarOpen", "fetchNewsDetail", "fetchKostumData", "loadThemeSettings", "response", "fetch", "ok", "Error", "status", "text", "console", "log", "data", "JSON", "parse", "e", "success", "warna_primary", "warna_sidebar", "warna_sidebar_header", "json", "settings", "logo_data", "logo_file_path", "nama_website", "newsId", "category_id", "fetchRelatedNews", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "finalUrl", "filename", "replace", "includes", "split", "pop", "customTheme", "palette", "primary", "main", "dark", "light", "secondary", "handleSidebar", "handleSidebarClose", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "style", "borderBottomColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backgroundColor", "onMouseEnter", "target", "onMouseLeave", "sx", "minHeight", "bgcolor", "width", "overflow", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "xs", "md", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "variant", "fontWeight", "fontSize", "edge", "ml", "anchor", "open", "onClose", "ModalProps", "keepMounted", "p", "top", "right", "mb", "textTransform", "letterSpacing", "spacing", "fullWidth", "justifyContent", "marginRight", "boxShadow", "mt", "max<PERSON><PERSON><PERSON>", "mx", "py", "gap", "component", "transition", "border", "background", "cursor", "pb", "gridTemplateColumns", "lg", "image", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "length", "map", "borderRadius", "left", "bottom", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme, createTheme, ThemeProvider } from '@mui/material/styles';\nimport Drawer from '@mui/material/Drawer';\nimport Button from '@mui/material/Button';\nimport Stack from '@mui/material/Stack';\nimport Divider from '@mui/material/Divider';\n\nconst DataNews = () => {\n    // Data News Component - Fixed Version\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const theme = useTheme();\n    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [themeSettings, setThemeSettings] = useState({\n        primary_color: '#3B82F6',\n        secondary_color: '#10B981',\n        accent_color: '#F59E0B'\n    });\n    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });\n    const [sidebarOpen, setSidebarOpen] = useState(false);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n        // Fetch kostum data\n        fetchKostumData();\n        // Load theme settings\n        loadThemeSettings();\n    }, [id]);\n\n    // Load theme settings from admin\n    const loadThemeSettings = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const text = await response.text();\n            console.log('Raw theme response:', text);\n\n            let data;\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                console.error('JSON parse error:', e);\n                console.error('Response text:', text);\n                throw new Error('Invalid JSON response');\n            }\n\n            if (data.success && data.data) {\n                setThemeSettings({\n                    primary_color: data.data.warna_primary || '#3B82F6',\n                    secondary_color: data.data.warna_sidebar || '#10B981',\n                    accent_color: data.data.warna_sidebar_header || '#F59E0B'\n                });\n                console.log('🎨 Theme settings loaded:', data.data);\n            }\n        } catch (error) {\n            console.error('❌ Error loading theme settings:', error);\n        }\n    };\n\n    const fetchKostumData = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                const settings = data.data;\n                setKostum({\n                    logo: settings.logo_data || settings.logo_file_path || '',\n                    title: settings.nama_website || 'React News Portal'\n                });\n                console.log('🎨 Kostum data loaded:', {\n                    logo: settings.logo_data ? 'Has logo data' : 'No logo data',\n                    title: settings.nama_website\n                });\n            }\n        } catch (error) {\n            // Fallback to default values if API fails\n            setKostum({\n                logo: '',\n                title: 'React News Portal'\n            });\n        }\n    };\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // Fetch news detail (views already incremented from landing page)\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n\n        console.log('data-news.js - Processing image path:', imagePath);\n\n        // If it's base64 data (for logos), return as is\n        if (imagePath.startsWith('data:image/')) {\n            console.log('data-news.js - Using base64 data');\n            return imagePath;\n        }\n\n        // Jika sudah URL lengkap, gunakan langsung\n        if (imagePath.startsWith('http')) {\n            console.log('data-news.js - Using full URL:', imagePath);\n            return imagePath;\n        }\n\n        // If it's already the correct path, use it directly\n        if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n            const finalUrl = `http://localhost${imagePath}`;\n            console.log('data-news.js - Using direct path:', finalUrl);\n            return finalUrl;\n        }\n\n        // Extract filename from any path format\n        let filename = '';\n\n        if (imagePath.startsWith('/react-news/uploads/')) {\n            filename = imagePath.replace('/react-news/uploads/', '');\n        } else if (imagePath.startsWith('/uploads/')) {\n            filename = imagePath.replace('/uploads/', '');\n        } else if (imagePath.startsWith('assets/news/')) {\n            filename = imagePath.replace('assets/news/', '');\n        } else if (!imagePath.includes('/')) {\n            // Just filename\n            filename = imagePath;\n        } else {\n            // Extract filename from any other path\n            filename = imagePath.split('/').pop();\n        }\n\n        // Use consistent frontend/uploads path\n        const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n        console.log('data-news.js - Final URL:', finalUrl);\n        return finalUrl;\n    };\n\n    // Create custom theme based on admin settings\n    const customTheme = createTheme({\n        palette: {\n            primary: {\n                main: themeSettings.primary_color,\n                dark: themeSettings.secondary_color,\n                light: themeSettings.accent_color,\n            },\n            secondary: {\n                main: themeSettings.secondary_color,\n            },\n        },\n    });\n\n    const handleSidebar = () => setSidebarOpen(true);\n    const handleSidebarClose = () => setSidebarOpen(false);\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div\n                        className=\"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\"\n                        style={{ borderBottomColor: themeSettings.primary_color }}\n                    ></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color,\n                            ':hover': { backgroundColor: themeSettings.secondary_color }\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"mt-4 text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <ThemeProvider theme={customTheme}>\n            <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>\n                {/* Responsive Navigation Bar */}\n            <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 48, height: 48, mr: 2 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/')}\n                        sx={{ mr: 1 }}\n                        title=\"Kembali ke Beranda\"\n                    >\n                        <ArrowBackIcon fontSize=\"large\" />\n                    </IconButton>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/video')}\n                        sx={{ mr: 1 }}\n                        title=\"Video\"\n                    >\n                        <i className=\"fas fa-play-circle\" style={{ fontSize: 24 }}></i>\n                    </IconButton>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={handleSidebar}\n                        sx={{ ml: 1 }}\n                        title=\"Menu\"\n                    >\n                        <MenuIcon fontSize=\"large\" />\n                    </IconButton>\n                </Toolbar>\n            </AppBar>\n\n            {/* Sidebar Drawer */}\n            <Drawer\n                anchor=\"right\"\n                open={sidebarOpen}\n                onClose={handleSidebarClose}\n                ModalProps={{ keepMounted: true }}\n                sx={{ zIndex: 2000 }}\n            >\n                <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n                    <IconButton\n                        onClick={handleSidebarClose}\n                        sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n                        aria-label=\"Tutup\"\n                    >\n                        <CloseIcon />\n                    </IconButton>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 32, height: 32, mr: 1 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <Divider sx={{ mb: 2 }} />\n\n                    {/* Quick Actions */}\n                    <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"subtitle2\" sx={{\n                            fontWeight: 600,\n                            color: 'text.secondary',\n                            mb: 1.5,\n                            fontSize: 12,\n                            textTransform: 'uppercase',\n                            letterSpacing: 0.5\n                        }}>\n                            Quick Actions\n                        </Typography>\n                        <Stack spacing={1.5}>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/');\n                                }}\n                            >\n                                <i className=\"fas fa-home\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Beranda\n                            </Button>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/saved');\n                                }}\n                            >\n                                <i className=\"fas fa-bookmark\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Berita Tersimpan\n                            </Button>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/video');\n                                }}\n                            >\n                                <i className=\"fas fa-play-circle\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Video\n                            </Button>\n                        </Stack>\n                    </Box>\n\n                    <Divider sx={{ mb: 2 }} />\n\n                    {/* Navigation */}\n                    <Box>\n                        <Typography variant=\"subtitle2\" sx={{\n                            fontWeight: 600,\n                            color: 'text.secondary',\n                            mb: 1.5,\n                            fontSize: 12,\n                            textTransform: 'uppercase',\n                            letterSpacing: 0.5\n                        }}>\n                            Navigation\n                        </Typography>\n                        <Stack spacing={2}>\n                            <Button\n                                variant=\"outlined\"\n                                color=\"primary\"\n                                fullWidth\n                                sx={{\n                                    textTransform: 'none',\n                                    borderColor: 'primary.main',\n                                    color: 'primary.main',\n                                    '&:hover': {\n                                        borderColor: 'primary.main',\n                                        backgroundColor: 'primary.main',\n                                        color: 'white'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/');\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Kembali ke Beranda\n                            </Button>\n                        </Stack>\n                    </Box>\n                </Box>\n            </Drawer>\n\n            {/* Header/Breadcrumb */}\n            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>\n                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>\n                        <Box\n                            component=\"button\"\n                            onClick={handleBackToHome}\n                            sx={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'text.secondary',\n                                '&:hover': { color: 'primary.main' },\n                                transition: 'color 0.2s',\n                                border: 'none',\n                                background: 'none',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            <i className=\"fas fa-home\" style={{ marginRight: '4px' }}></i>\n                            Beranda\n                        </Box>\n                        <i className=\"fas fa-chevron-right\" style={{ fontSize: '12px' }}></i>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n                            Detail Berita\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box sx={{\n                maxWidth: '1200px',\n                mx: 'auto',\n                px: 2,\n                py: 4,\n                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav\n            }}>\n                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>\n                    {/* Main Article */}\n                    <Box>\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://picsum.photos/800/400?random=2';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span\n                                        className=\"text-white px-3 py-1 rounded-full text-sm font-medium\"\n                                        style={{ backgroundColor: themeSettings.primary_color }}\n                                    >\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-calendar-alt mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-clock mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-eye mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.primary_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                        >\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.accent_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.accent_color}\n                                        >\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </Box>\n\n                    {/* Sidebar */}\n                    <Box>\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i\n                                        className=\"fas fa-newspaper mr-2\"\n                                        style={{ color: themeSettings.primary_color }}\n                                    ></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>\n                            <Box\n                                component=\"button\"\n                                onClick={handleBackToHome}\n                                sx={{\n                                    width: '100%',\n                                    bgcolor: 'primary.main',\n                                    color: 'white',\n                                    py: 1.5,\n                                    px: 2,\n                                    borderRadius: 2,\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontWeight: 500,\n                                    transition: 'background-color 0.2s',\n                                    '&:hover': {\n                                        bgcolor: 'primary.dark'\n                                    }\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: '8px' }}></i>\n                                Kembali ke Beranda\n                            </Box>\n                        </Box>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Custom Bottom Navigation - Mobile Only */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: { xs: 'block', md: 'none' }, // Hide on desktop\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i\n                            className=\"fas fa-home bottom-nav-icon\"\n                            style={{ color: themeSettings.primary_color }}\n                        ></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </Box>\n        </ThemeProvider>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,QAAQ,sBAAsB;AAC3E,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,OAAO,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB;EACA,MAAM;IAAEC;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC1B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB,MAAMc,SAAS,GAAGf,aAAa,CAACc,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC;IAC/CyC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC;IAAE8C,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAoB,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZ,IAAIwB,EAAE,EAAE;MACJyB,eAAe,CAACzB,EAAE,CAAC;IACvB;IACA;IACA0B,eAAe,CAAC,CAAC;IACjB;IACAC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC3B,EAAE,CAAC,CAAC;;EAER;EACA,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MAEzH,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC;MAExC,IAAIG,IAAI;MACR,IAAI;QACAA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOM,CAAC,EAAE;QACRL,OAAO,CAACtB,KAAK,CAAC,mBAAmB,EAAE2B,CAAC,CAAC;QACrCL,OAAO,CAACtB,KAAK,CAAC,gBAAgB,EAAEqB,IAAI,CAAC;QACrC,MAAM,IAAIF,KAAK,CAAC,uBAAuB,CAAC;MAC5C;MAEA,IAAIK,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3BrB,gBAAgB,CAAC;UACbC,aAAa,EAAEoB,IAAI,CAACA,IAAI,CAACK,aAAa,IAAI,SAAS;UACnDxB,eAAe,EAAEmB,IAAI,CAACA,IAAI,CAACM,aAAa,IAAI,SAAS;UACrDxB,YAAY,EAAEkB,IAAI,CAACA,IAAI,CAACO,oBAAoB,IAAI;QACpD,CAAC,CAAC;QACFT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,IAAI,CAACA,IAAI,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MACzH,MAAMO,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B,MAAMS,QAAQ,GAAGT,IAAI,CAACA,IAAI;QAC1BhB,SAAS,CAAC;UACNC,IAAI,EAAEwB,QAAQ,CAACC,SAAS,IAAID,QAAQ,CAACE,cAAc,IAAI,EAAE;UACzDzB,KAAK,EAAEuB,QAAQ,CAACG,YAAY,IAAI;QACpC,CAAC,CAAC;QACFd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;UAClCd,IAAI,EAAEwB,QAAQ,CAACC,SAAS,GAAG,eAAe,GAAG,cAAc;UAC3DxB,KAAK,EAAEuB,QAAQ,CAACG;QACpB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACZ;MACAQ,SAAS,CAAC;QACNC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMG,eAAe,GAAG,MAAOwB,MAAM,IAAK;IACtC,IAAI;MACAtC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGoB,MAAM,EAAE,CAAC;MAEtI,IAAI,CAACrB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B7B,OAAO,CAAC6B,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACc,WAAW,EAAE;UACvBC,gBAAgB,CAACf,IAAI,CAACA,IAAI,CAACc,WAAW,EAAED,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHpC,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwC,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMzB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGuB,UAAU,UAAU,CAAC;MAElJ,IAAI,CAACxB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIc,KAAK,CAACC,OAAO,CAACnB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMoB,QAAQ,GAAGpB,IAAI,CAACA,IAAI,CACrBqB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC1D,EAAE,KAAK2D,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChBnD,cAAc,CAAC+C,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACH/C,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMoD,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,2CAA2C;IAElE5B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2B,SAAS,CAAC;;IAE/D;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;MACrC7B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C,OAAO2B,SAAS;IACpB;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9B7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2B,SAAS,CAAC;MACxD,OAAOA,SAAS;IACpB;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,+BAA+B,CAAC,EAAE;MACvD,MAAMC,QAAQ,GAAG,mBAAmBF,SAAS,EAAE;MAC/C5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE6B,QAAQ,CAAC;MAC1D,OAAOA,QAAQ;IACnB;;IAEA;IACA,IAAIC,QAAQ,GAAG,EAAE;IAEjB,IAAIH,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;MAC9CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;IAC5D,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;MAC1CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACjD,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;MAC7CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACpD,CAAC,MAAM,IAAI,CAACJ,SAAS,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACAF,QAAQ,GAAGH,SAAS;IACxB,CAAC,MAAM;MACH;MACAG,QAAQ,GAAGH,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;IACzC;;IAEA;IACA,MAAML,QAAQ,GAAG,gDAAgDC,QAAQ,EAAE;IAC3E/B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,QAAQ,CAAC;IAClD,OAAOA,QAAQ;EACnB,CAAC;;EAED;EACA,MAAMM,WAAW,GAAGhF,WAAW,CAAC;IAC5BiF,OAAO,EAAE;MACLC,OAAO,EAAE;QACLC,IAAI,EAAE3D,aAAa,CAACE,aAAa;QACjC0D,IAAI,EAAE5D,aAAa,CAACG,eAAe;QACnC0D,KAAK,EAAE7D,aAAa,CAACI;MACzB,CAAC;MACD0D,SAAS,EAAE;QACPH,IAAI,EAAE3D,aAAa,CAACG;MACxB;IACJ;EACJ,CAAC,CAAC;EAEF,MAAM4D,aAAa,GAAGA,CAAA,KAAMrD,cAAc,CAAC,IAAI,CAAC;EAChD,MAAMsD,kBAAkB,GAAGA,CAAA,KAAMtD,cAAc,CAAC,KAAK,CAAC;EAEtD,MAAMuD,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAI3C,MAAM,IAAK;IACvChD,QAAQ,CAAC,cAAcgD,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC3B5F,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIS,OAAO,EAAE;IACT,oBACIb,OAAA;MAAKiG,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElG,OAAA;QAAKiG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlG,OAAA;UACIiG,SAAS,EAAC,6DAA6D;UACvEE,KAAK,EAAE;YAAEC,iBAAiB,EAAEnF,aAAa,CAACE;UAAc;QAAE;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACPxG,OAAA;UAAGiG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIzF,KAAK,EAAE;IACP,oBACIf,OAAA;MAAKiG,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElG,OAAA;QAAKiG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlG,OAAA;UAAKiG,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpFlG,OAAA;YAAGiG,SAAS,EAAC;UAAkC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnDzF,KAAK;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNxG,OAAA;UACIyG,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,mDAAmD;UAC7DE,KAAK,EAAE;YACHO,eAAe,EAAEzF,aAAa,CAACE,aAAa;YAC5C,QAAQ,EAAE;cAAEuF,eAAe,EAAEzF,aAAa,CAACG;YAAgB;UAC/D,CAAE;UACFuF,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACG,eAAgB;UACpFyF,YAAY,EAAGnE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACE,aAAc;UAAA+E,QAAA,gBAElFlG,OAAA;YAAGiG,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAC/F,IAAI,EAAE;IACP,oBACIT,OAAA;MAAKiG,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElG,OAAA;QAAKiG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlG,OAAA;UAAGiG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDxG,OAAA;UACIyG,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,wDAAwD;UAClEE,KAAK,EAAE;YACHO,eAAe,EAAEzF,aAAa,CAACE;UACnC,CAAE;UACFwF,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACG,eAAgB;UACpFyF,YAAY,EAAGnE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACE,aAAc;UAAA+E,QAAA,gBAElFlG,OAAA;YAAGiG,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIxG,OAAA,CAACN,aAAa;IAACW,KAAK,EAAEoE,WAAY;IAAAyB,QAAA,eAC9BlG,OAAA,CAAClB,GAAG;MAACgI,EAAE,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,OAAO,EAAE,SAAS;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAAhB,QAAA,gBAExFlG,OAAA,CAAChB,MAAM;QAACmI,QAAQ,EAAC,OAAO;QAACC,KAAK,EAAC,SAAS;QAACC,SAAS,EAAE,CAAE;QAACP,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,UAAU;UAAEC,MAAM,EAAE;QAAK,CAAE;QAAAtB,QAAA,eACnIlG,OAAA,CAACf,OAAO;UAAC6H,EAAE,EAAE;YAAEC,SAAS,EAAE;cAAEU,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YAAEC,EAAE,EAAE;cAAEF,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE;UAAE,CAAE;UAAAxB,QAAA,gBACjElG,OAAA,CAAClB,GAAG;YAACgI,EAAE,EAAE;cAAEc,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,QAAQ,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAC5DlG,OAAA,CAACd,MAAM;cACH6I,GAAG,EAAEzG,MAAM,CAACE,IAAI,GAAGwC,WAAW,CAAC1C,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;cAC7DwG,GAAG,EAAC,MAAM;cACVlB,EAAE,EAAE;gBAAEG,KAAK,EAAE,EAAE;gBAAEgB,MAAM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cACrCC,OAAO,EAAGzF,CAAC,IAAK;gBAAEA,CAAC,CAACkE,MAAM,CAACmB,GAAG,GAAG,cAAc;cAAE;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACFxG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,IAAI;cAACtB,EAAE,EAAE;gBAAEuB,UAAU,EAAE,GAAG;gBAAEjB,KAAK,EAAE,cAAc;gBAAEkB,QAAQ,EAAE;kBAAEb,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE,CAAE;cAAAxB,QAAA,EACjG5E,MAAM,CAACG;YAAK;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNxG,OAAA,CAACb,UAAU;YACPoJ,IAAI,EAAC,KAAK;YACVnB,KAAK,EAAC,SAAS;YACfX,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,GAAG,CAAE;YAC7B0G,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdzG,KAAK,EAAC,oBAAoB;YAAAyE,QAAA,eAE1BlG,OAAA,CAACV,aAAa;cAACgJ,QAAQ,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACbxG,OAAA,CAACb,UAAU;YACPoJ,IAAI,EAAC,KAAK;YACVnB,KAAK,EAAC,SAAS;YACfX,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,QAAQ,CAAE;YAClC0G,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdzG,KAAK,EAAC,OAAO;YAAAyE,QAAA,eAEblG,OAAA;cAAGiG,SAAS,EAAC,oBAAoB;cAACE,KAAK,EAAE;gBAAEmC,QAAQ,EAAE;cAAG;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACbxG,OAAA,CAACb,UAAU;YACPoJ,IAAI,EAAC,KAAK;YACVnB,KAAK,EAAC,SAAS;YACfX,OAAO,EAAEzB,aAAc;YACvB8B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YACd/G,KAAK,EAAC,MAAM;YAAAyE,QAAA,eAEZlG,OAAA,CAACZ,QAAQ;cAACkJ,QAAQ,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGTxG,OAAA,CAACL,MAAM;QACH8I,MAAM,EAAC,OAAO;QACdC,IAAI,EAAEhH,WAAY;QAClBiH,OAAO,EAAE1D,kBAAmB;QAC5B2D,UAAU,EAAE;UAAEC,WAAW,EAAE;QAAK,CAAE;QAClC/B,EAAE,EAAE;UAAEU,MAAM,EAAE;QAAK,CAAE;QAAAtB,QAAA,eAErBlG,OAAA,CAAClB,GAAG;UAACgI,EAAE,EAAE;YAAEG,KAAK,EAAE,GAAG;YAAE6B,CAAC,EAAE,CAAC;YAAE3B,QAAQ,EAAE;UAAW,CAAE;UAAAjB,QAAA,gBAChDlG,OAAA,CAACb,UAAU;YACPsH,OAAO,EAAExB,kBAAmB;YAC5B6B,EAAE,EAAE;cAAEK,QAAQ,EAAE,UAAU;cAAE4B,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE,CAAC;cAAExB,MAAM,EAAE;YAAG,CAAE;YAC3D,cAAW,OAAO;YAAAtB,QAAA,eAElBlG,OAAA,CAACX,SAAS;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEbxG,OAAA,CAAClB,GAAG;YAACgI,EAAE,EAAE;cAAEc,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBACtDlG,OAAA,CAACd,MAAM;cACH6I,GAAG,EAAEzG,MAAM,CAACE,IAAI,GAAGwC,WAAW,CAAC1C,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;cAC7DwG,GAAG,EAAC,MAAM;cACVlB,EAAE,EAAE;gBAAEG,KAAK,EAAE,EAAE;gBAAEgB,MAAM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cACrCC,OAAO,EAAGzF,CAAC,IAAK;gBAAEA,CAAC,CAACkE,MAAM,CAACmB,GAAG,GAAG,cAAc;cAAE;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACFxG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,IAAI;cAACtB,EAAE,EAAE;gBAAEuB,UAAU,EAAE,GAAG;gBAAEjB,KAAK,EAAE,cAAc;gBAAEkB,QAAQ,EAAE;cAAG,CAAE;cAAApC,QAAA,EACjF5E,MAAM,CAACG;YAAK;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNxG,OAAA,CAACF,OAAO;YAACgH,EAAE,EAAE;cAAEmC,EAAE,EAAE;YAAE;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG1BxG,OAAA,CAAClB,GAAG;YAACgI,EAAE,EAAE;cAAEmC,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBACflG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,WAAW;cAACtB,EAAE,EAAE;gBAChCuB,UAAU,EAAE,GAAG;gBACfjB,KAAK,EAAE,gBAAgB;gBACvB6B,EAAE,EAAE,GAAG;gBACPX,QAAQ,EAAE,EAAE;gBACZY,aAAa,EAAE,WAAW;gBAC1BC,aAAa,EAAE;cACnB,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxG,OAAA,CAACH,KAAK;cAACuJ,OAAO,EAAE,GAAI;cAAAlD,QAAA,gBAChBlG,OAAA,CAACJ,MAAM;gBACHwI,OAAO,EAAC,MAAM;gBACdiB,SAAS;gBACTvC,EAAE,EAAE;kBACAwC,cAAc,EAAE,YAAY;kBAC5BJ,aAAa,EAAE,MAAM;kBACrB9B,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACPV,eAAe,EAAE;kBACrB;gBACJ,CAAE;gBACFD,OAAO,EAAEA,CAAA,KAAM;kBACXxB,kBAAkB,CAAC,CAAC;kBACpB7E,QAAQ,CAAC,GAAG,CAAC;gBACjB,CAAE;gBAAA8F,QAAA,gBAEFlG,OAAA;kBAAGiG,SAAS,EAAC,aAAa;kBAACE,KAAK,EAAE;oBAAEoD,WAAW,EAAE,CAAC;oBAAEjB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,WAE5E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA,CAACJ,MAAM;gBACHwI,OAAO,EAAC,MAAM;gBACdiB,SAAS;gBACTvC,EAAE,EAAE;kBACAwC,cAAc,EAAE,YAAY;kBAC5BJ,aAAa,EAAE,MAAM;kBACrB9B,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACPV,eAAe,EAAE;kBACrB;gBACJ,CAAE;gBACFD,OAAO,EAAEA,CAAA,KAAM;kBACXxB,kBAAkB,CAAC,CAAC;kBACpB7E,QAAQ,CAAC,QAAQ,CAAC;gBACtB,CAAE;gBAAA8F,QAAA,gBAEFlG,OAAA;kBAAGiG,SAAS,EAAC,iBAAiB;kBAACE,KAAK,EAAE;oBAAEoD,WAAW,EAAE,CAAC;oBAAEjB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,oBAEhF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA,CAACJ,MAAM;gBACHwI,OAAO,EAAC,MAAM;gBACdiB,SAAS;gBACTvC,EAAE,EAAE;kBACAwC,cAAc,EAAE,YAAY;kBAC5BJ,aAAa,EAAE,MAAM;kBACrB9B,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACPV,eAAe,EAAE;kBACrB;gBACJ,CAAE;gBACFD,OAAO,EAAEA,CAAA,KAAM;kBACXxB,kBAAkB,CAAC,CAAC;kBACpB7E,QAAQ,CAAC,QAAQ,CAAC;gBACtB,CAAE;gBAAA8F,QAAA,gBAEFlG,OAAA;kBAAGiG,SAAS,EAAC,oBAAoB;kBAACE,KAAK,EAAE;oBAAEoD,WAAW,EAAE,CAAC;oBAAEjB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,SAEnF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAENxG,OAAA,CAACF,OAAO;YAACgH,EAAE,EAAE;cAAEmC,EAAE,EAAE;YAAE;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG1BxG,OAAA,CAAClB,GAAG;YAAAoH,QAAA,gBACAlG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,WAAW;cAACtB,EAAE,EAAE;gBAChCuB,UAAU,EAAE,GAAG;gBACfjB,KAAK,EAAE,gBAAgB;gBACvB6B,EAAE,EAAE,GAAG;gBACPX,QAAQ,EAAE,EAAE;gBACZY,aAAa,EAAE,WAAW;gBAC1BC,aAAa,EAAE;cACnB,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxG,OAAA,CAACH,KAAK;cAACuJ,OAAO,EAAE,CAAE;cAAAlD,QAAA,eACdlG,OAAA,CAACJ,MAAM;gBACHwI,OAAO,EAAC,UAAU;gBAClBhB,KAAK,EAAC,SAAS;gBACfiC,SAAS;gBACTvC,EAAE,EAAE;kBACAoC,aAAa,EAAE,MAAM;kBACrB3B,WAAW,EAAE,cAAc;kBAC3BH,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACPG,WAAW,EAAE,cAAc;oBAC3Bb,eAAe,EAAE,cAAc;oBAC/BU,KAAK,EAAE;kBACX;gBACJ,CAAE;gBACFX,OAAO,EAAEA,CAAA,KAAM;kBACXxB,kBAAkB,CAAC,CAAC;kBACpB7E,QAAQ,CAAC,GAAG,CAAC;gBACjB,CAAE;gBAAA8F,QAAA,gBAEFlG,OAAA;kBAAGiG,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAEoD,WAAW,EAAE,CAAC;oBAAEjB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sBAElF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGTxG,OAAA,CAAClB,GAAG;QAACgI,EAAE,EAAE;UAAEE,OAAO,EAAE,OAAO;UAAEwC,SAAS,EAAE,CAAC;UAAElC,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,UAAU;UAAEkC,EAAE,EAAE;YAAEhC,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO;QAAE,CAAE;QAAAxB,QAAA,eAClHlG,OAAA,CAAClB,GAAG;UAACgI,EAAE,EAAE;YAAE4C,QAAQ,EAAE,QAAQ;YAAEC,EAAE,EAAE,MAAM;YAAEhC,EAAE,EAAE,CAAC;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,eACtDlG,OAAA,CAAClB,GAAG;YAACgI,EAAE,EAAE;cAAEc,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEgC,GAAG,EAAE,CAAC;cAAEvB,QAAQ,EAAE,MAAM;cAAElB,KAAK,EAAE;YAAiB,CAAE;YAAAlB,QAAA,gBAClGlG,OAAA,CAAClB,GAAG;cACAgL,SAAS,EAAC,QAAQ;cAClBrD,OAAO,EAAET,gBAAiB;cAC1Bc,EAAE,EAAE;gBACAc,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBT,KAAK,EAAE,gBAAgB;gBACvB,SAAS,EAAE;kBAAEA,KAAK,EAAE;gBAAe,CAAC;gBACpC2C,UAAU,EAAE,YAAY;gBACxBC,MAAM,EAAE,MAAM;gBACdC,UAAU,EAAE,MAAM;gBAClBC,MAAM,EAAE;cACZ,CAAE;cAAAhE,QAAA,gBAEFlG,OAAA;gBAAGiG,SAAS,EAAC,aAAa;gBAACE,KAAK,EAAE;kBAAEoD,WAAW,EAAE;gBAAM;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAElE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxG,OAAA;cAAGiG,SAAS,EAAC,sBAAsB;cAACE,KAAK,EAAE;gBAAEmC,QAAQ,EAAE;cAAO;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrExG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,OAAO;cAACtB,EAAE,EAAE;gBAAEuB,UAAU,EAAE,GAAG;gBAAEjB,KAAK,EAAE;cAAe,CAAE;cAAAlB,QAAA,EAAC;YAE5E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxG,OAAA,CAAClB,GAAG;QAACgI,EAAE,EAAE;UACL4C,QAAQ,EAAE,QAAQ;UAClBC,EAAE,EAAE,MAAM;UACVhC,EAAE,EAAE,CAAC;UACLiC,EAAE,EAAE,CAAC;UACLO,EAAE,EAAE;YAAE1C,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAC,CAAC;QAC1B,CAAE;QAAAxB,QAAA,eACElG,OAAA,CAAClB,GAAG;UAACgI,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEwC,mBAAmB,EAAE;cAAE3C,EAAE,EAAE,KAAK;cAAE4C,EAAE,EAAE;YAAU,CAAC;YAAER,GAAG,EAAE;UAAE,CAAE;UAAA3D,QAAA,gBAEpFlG,OAAA,CAAClB,GAAG;YAAAoH,QAAA,eACAlG,OAAA;cAASiG,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAE9DlG,OAAA;gBAAKiG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBlG,OAAA;kBACI+H,GAAG,EAAE/D,WAAW,CAACvD,IAAI,CAAC6J,KAAK,CAAE;kBAC7BtC,GAAG,EAAEvH,IAAI,CAACgB,KAAM;kBAChBwE,SAAS,EAAC,kCAAkC;kBAC5CkC,OAAO,EAAGzF,CAAC,IAAK;oBACZA,CAAC,CAACkE,MAAM,CAACmB,GAAG,GAAG,wCAAwC;kBAC3D;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFxG,OAAA;kBAAKiG,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,eAClClG,OAAA;oBACIiG,SAAS,EAAC,uDAAuD;oBACjEE,KAAK,EAAE;sBAAEO,eAAe,EAAEzF,aAAa,CAACE;oBAAc,CAAE;oBAAA+E,QAAA,EAEvDzF,IAAI,CAAC8J,aAAa,IAAI;kBAAQ;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNxG,OAAA;gBAAKiG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBlG,OAAA;kBAAIiG,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAC1EzF,IAAI,CAACgB;gBAAK;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eAGLxG,OAAA;kBAAKiG,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,gBACvFlG,OAAA;oBAAKiG,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BlG,OAAA;sBACIiG,SAAS,EAAC,0BAA0B;sBACpCE,KAAK,EAAE;wBAAEiB,KAAK,EAAEnG,aAAa,CAACE;sBAAc;oBAAE;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,EACJtB,UAAU,CAACzE,IAAI,CAAC+J,UAAU,IAAI/J,IAAI,CAAC2E,IAAI,CAAC;kBAAA;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNxG,OAAA;oBAAKiG,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BlG,OAAA;sBACIiG,SAAS,EAAC,mBAAmB;sBAC7BE,KAAK,EAAE;wBAAEiB,KAAK,EAAEnG,aAAa,CAACE;sBAAc;oBAAE;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,EACJb,UAAU,CAAClF,IAAI,CAAC+J,UAAU,IAAI/J,IAAI,CAAC2E,IAAI,CAAC;kBAAA;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNxG,OAAA;oBAAKiG,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BlG,OAAA;sBACIiG,SAAS,EAAC,iBAAiB;sBAC3BE,KAAK,EAAE;wBAAEiB,KAAK,EAAEnG,aAAa,CAACE;sBAAc;oBAAE;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,EACJ/F,IAAI,CAACgK,KAAK,IAAI,CAAC,EAAC,QACrB;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGNxG,OAAA;kBAAKiG,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACtClG,OAAA;oBACIiG,SAAS,EAAC,mDAAmD;oBAC7DyE,uBAAuB,EAAE;sBACrBC,MAAM,EAAElK,IAAI,CAACmK,OAAO,CAACvG,OAAO,CAAC,KAAK,EAAE,MAAM;oBAC9C;kBAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGNxG,OAAA;kBAAKiG,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBAC/BlG,OAAA;oBAAIiG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7ExG,OAAA;oBAAKiG,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BlG,OAAA;sBACIiG,SAAS,EAAC,mDAAmD;sBAC7DE,KAAK,EAAE;wBAAEO,eAAe,EAAEzF,aAAa,CAACE;sBAAc,CAAE;sBACxDwF,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACG,eAAgB;sBACpFyF,YAAY,EAAGnE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACE,aAAc;sBAAA+E,QAAA,gBAElFlG,OAAA;wBAAGiG,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAE9C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxG,OAAA;sBACIiG,SAAS,EAAC,mDAAmD;sBAC7DE,KAAK,EAAE;wBAAEO,eAAe,EAAEzF,aAAa,CAACI;sBAAa,CAAE;sBACvDsF,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACE,aAAc;sBAClF0F,YAAY,EAAGnE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGzF,aAAa,CAACI,YAAa;sBAAA6E,QAAA,gBAEjFlG,OAAA;wBAAGiG,SAAS,EAAC;sBAAqB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxG,OAAA;sBAAQiG,SAAS,EAAC,mFAAmF;sBAAAC,QAAA,gBACjGlG,OAAA;wBAAGiG,SAAS,EAAC;sBAAsB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAE5C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGNxG,OAAA,CAAClB,GAAG;YAAAoH,QAAA,GAECvF,WAAW,CAACkK,MAAM,GAAG,CAAC,iBACnB7K,OAAA;cAAKiG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDlG,OAAA;gBAAIiG,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACpDlG,OAAA;kBACIiG,SAAS,EAAC,uBAAuB;kBACjCE,KAAK,EAAE;oBAAEiB,KAAK,EAAEnG,aAAa,CAACE;kBAAc;gBAAE;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,kBAET;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBAAKiG,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACrBvF,WAAW,CAACmK,GAAG,CAAEjH,IAAI,iBAClB7D,OAAA;kBAEIyG,OAAO,EAAEA,CAAA,KAAMV,sBAAsB,CAAClC,IAAI,CAAC1D,EAAE,CAAE;kBAC/C8F,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBAEjFlG,OAAA;oBACI+H,GAAG,EAAE/D,WAAW,CAACH,IAAI,CAACyG,KAAK,CAAE;oBAC7BtC,GAAG,EAAEnE,IAAI,CAACpC,KAAM;oBAChBwE,SAAS,EAAC,sDAAsD;oBAChEkC,OAAO,EAAGzF,CAAC,IAAK;sBACZA,CAAC,CAACkE,MAAM,CAACmB,GAAG,GAAG,wCAAwC;oBAC3D;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACFxG,OAAA;oBAAKiG,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BlG,OAAA;sBAAIiG,SAAS,EAAC,qDAAqD;sBAAAC,QAAA,EAC9DrC,IAAI,CAACpC;oBAAK;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACLxG,OAAA;sBAAGiG,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAC/BhB,UAAU,CAACrB,IAAI,CAAC2G,UAAU,IAAI3G,IAAI,CAACuB,IAAI;oBAAC;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAnBD3C,IAAI,CAAC1D,EAAE;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBX,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,eAGDxG,OAAA,CAAClB,GAAG;cAACgI,EAAE,EAAE;gBAAEE,OAAO,EAAE,OAAO;gBAAE+D,YAAY,EAAE,CAAC;gBAAEvB,SAAS,EAAE,CAAC;gBAAEV,CAAC,EAAE;cAAE,CAAE;cAAA5C,QAAA,eAC/DlG,OAAA,CAAClB,GAAG;gBACAgL,SAAS,EAAC,QAAQ;gBAClBrD,OAAO,EAAET,gBAAiB;gBAC1Bc,EAAE,EAAE;kBACAG,KAAK,EAAE,MAAM;kBACbD,OAAO,EAAE,cAAc;kBACvBI,KAAK,EAAE,OAAO;kBACdwC,EAAE,EAAE,GAAG;kBACPjC,EAAE,EAAE,CAAC;kBACLoD,YAAY,EAAE,CAAC;kBACff,MAAM,EAAE,MAAM;kBACdE,MAAM,EAAE,SAAS;kBACjB7B,UAAU,EAAE,GAAG;kBACf0B,UAAU,EAAE,uBAAuB;kBACnC,SAAS,EAAE;oBACP/C,OAAO,EAAE;kBACb;gBACJ,CAAE;gBAAAd,QAAA,gBAEFlG,OAAA;kBAAGiG,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAEoD,WAAW,EAAE;kBAAM;gBAAE;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sBAExE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxG,OAAA,CAAClB,GAAG;QAACgI,EAAE,EAAE;UACLK,QAAQ,EAAE,OAAO;UACjB6D,IAAI,EAAE,CAAC;UACPhC,KAAK,EAAE,CAAC;UACRiC,MAAM,EAAE,CAAC;UACTzD,MAAM,EAAE,IAAI;UACZI,OAAO,EAAE;YAAEH,EAAE,EAAE,OAAO;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAE;UACtChB,eAAe,EAAE,OAAO;UACxBwE,SAAS,EAAE,mBAAmB;UAC9B1B,SAAS,EAAE;QACf,CAAE;QAAAtD,QAAA,eACElG,OAAA,CAAClB,GAAG;UAACgI,EAAE,EAAE;YACLc,OAAO,EAAE,MAAM;YACf0B,cAAc,EAAE,cAAc;YAC9BzB,UAAU,EAAE,QAAQ;YACpBI,MAAM,EAAE,EAAE;YACVN,EAAE,EAAE;UACR,CAAE;UAAAzB,QAAA,gBACElG,OAAA,CAAClB,GAAG;YACA2H,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,GAAG,CAAE;YAC7B6F,SAAS,EAAC,wBAAwB;YAClCE,KAAK,EAAE;cAAE+D,MAAM,EAAE;YAAU,CAAE;YAAAhE,QAAA,gBAE7BlG,OAAA;cACIiG,SAAS,EAAC,6BAA6B;cACvCE,KAAK,EAAE;gBAAEiB,KAAK,EAAEnG,aAAa,CAACE;cAAc;YAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACLxG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,SAAS;cAACnC,SAAS,EAAC,kBAAkB;cAACa,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAe,CAAE;cAAAlB,QAAA,EAAC;YAE1F;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAENxG,OAAA,CAAClB,GAAG;YACA2H,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,GAAG,CAAE;YAC7B6F,SAAS,EAAC,iBAAiB;YAC3BE,KAAK,EAAE;cAAE+D,MAAM,EAAE;YAAU,CAAE;YAAAhE,QAAA,gBAE7BlG,OAAA;cAAGiG,SAAS,EAAC;YAA6C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DxG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,SAAS;cAACnC,SAAS,EAAC,kBAAkB;cAACa,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAiB,CAAE;cAAAlB,QAAA,EAAC;YAE5F;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAENxG,OAAA,CAAClB,GAAG;YACA2H,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,QAAQ,CAAE;YAClC6F,SAAS,EAAC,iBAAiB;YAC3BE,KAAK,EAAE;cAAE+D,MAAM,EAAE;YAAU,CAAE;YAAAhE,QAAA,gBAE7BlG,OAAA;cAAGiG,SAAS,EAAC;YAA+C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjExG,OAAA,CAACjB,UAAU;cAACqJ,OAAO,EAAC,SAAS;cAACnC,SAAS,EAAC,kBAAkB;cAACa,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAiB,CAAE;cAAAlB,QAAA,EAAC;YAE5F;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAExB,CAAC;AAACtG,EAAA,CA7uBID,QAAQ;EAAA,QAEKrB,SAAS,EACPC,WAAW,EACdW,QAAQ,EACJD,aAAa;AAAA;AAAA4L,EAAA,GAL7BlL,QAAQ;AA+uBd,eAAeA,QAAQ;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}