import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Box,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Button,
  TextField,
  InputAdornment,
  BottomNavigation,
  BottomNavigationAction,
  Paper,
  useMediaQuery,
  useTheme,
  Skeleton,
  Menu,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  Search as SearchIcon,
  Home as HomeIcon,
  Bookmark as BookmarkIcon,
  VideoLibrary as VideoIcon,
  Person as PersonIcon,
  Close as CloseIcon,
  Login as LoginIcon,
  PersonAdd as RegisterIcon,
  Logout as LogoutIcon,
  Article as NewsIcon,
  Share as ShareIcon,
  Favorite as FavoriteIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const DataNews = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();

  // States
  const [newsData, setNewsData] = useState([]);
  const [categoriesData, setCategoriesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [bottomNavValue, setBottomNavValue] = useState(1); // Set to News tab
  const [anchorEl, setAnchorEl] = useState(null);

  // Fetch news data
  useEffect(() => {
    fetchNewsData();
    fetchCategories();
  }, []);

  const fetchNewsData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/posts');
      const data = await response.json();
      
      if (data.success) {
        const publishedPosts = data.data.filter(item => item.status === 'published');
        setNewsData(publishedPosts);
      }
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/categories');
      const data = await response.json();
      
      if (data.success) {
        setCategoriesData([
          { id: 0, name: 'Semua', color: '#6B7280' },
          ...data.data
        ]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSearchToggle = () => {
    setSearchOpen(!searchOpen);
  };

  const handleBottomNavChange = (event, newValue) => {
    setBottomNavValue(newValue);
    switch (newValue) {
      case 0:
        navigate('/');
        break;
      case 1:
        // Already on data-news page
        break;
      case 2:
        navigate('/video');
        break;
      case 3:
        if (user) {
          navigate('/saved');
        } else {
          navigate('/auth/login');
        }
        break;
      case 4:
        if (user) {
          setAnchorEl(event.currentTarget);
        } else {
          navigate('/auth/login');
        }
        break;
    }
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    setAnchorEl(null);
    navigate('/');
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    });
  };

  const filteredNews = newsData.filter(news => {
    const matchesCategory = selectedCategory === 'Semua' || 
                           news.category === selectedCategory ||
                           news.category_name === selectedCategory;
    const matchesSearch = news.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         news.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  }).sort((a, b) => {
    // Sort by date (newest first)
    const dateA = new Date(a.published_at || a.date || a.created_at);
    const dateB = new Date(b.published_at || b.date || b.created_at);
    return dateB - dateA;
  });

  // Sidebar content
  const sidebarContent = (
    <Box sx={{ width: 280, pt: 2 }}>
      <Box sx={{ px: 3, pb: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          React News
        </Typography>
      </Box>
      <Divider />
      
      <List>
        <ListItem button onClick={() => navigate('/')}>
          <ListItemIcon><HomeIcon /></ListItemIcon>
          <ListItemText primary="Beranda" />
        </ListItem>
        
        <ListItem button>
          <ListItemIcon><NewsIcon color="primary" /></ListItemIcon>
          <ListItemText primary="Semua Berita" />
        </ListItem>
        
        <ListItem button onClick={() => navigate('/video')}>
          <ListItemIcon><VideoIcon /></ListItemIcon>
          <ListItemText primary="Video" />
        </ListItem>
        
        {user ? (
          <>
            <ListItem button onClick={() => navigate('/saved')}>
              <ListItemIcon><BookmarkIcon /></ListItemIcon>
              <ListItemText primary="Tersimpan" />
            </ListItem>
            <Divider sx={{ my: 1 }} />
            <ListItem button onClick={handleLogout}>
              <ListItemIcon><LogoutIcon /></ListItemIcon>
              <ListItemText primary="Logout" />
            </ListItem>
          </>
        ) : (
          <>
            <Divider sx={{ my: 1 }} />
            <ListItem button onClick={() => navigate('/auth/login')}>
              <ListItemIcon><LoginIcon /></ListItemIcon>
              <ListItemText primary="Login" />
            </ListItem>
            <ListItem button onClick={() => navigate('/auth/register')}>
              <ListItemIcon><RegisterIcon /></ListItemIcon>
              <ListItemText primary="Register" />
            </ListItem>
          </>
        )}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Top Navigation */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            Semua Berita
          </Typography>

          {/* Search Icon */}
          <IconButton color="inherit" onClick={handleSearchToggle}>
            <SearchIcon />
          </IconButton>

          {/* Video Button */}
          <IconButton color="inherit" onClick={() => navigate('/video')}>
            <VideoIcon />
          </IconButton>

          {/* Profile/Login */}
          {user ? (
            <IconButton color="inherit" onClick={(e) => setAnchorEl(e.currentTarget)}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                {user.name?.charAt(0).toUpperCase()}
              </Avatar>
            </IconButton>
          ) : (
            <Button color="inherit" onClick={() => navigate('/auth/login')}>
              Login
            </Button>
          )}
        </Toolbar>

        {/* Search Bar */}
        {searchOpen && (
          <Box sx={{ px: 2, pb: 2 }}>
            <TextField
              fullWidth
              placeholder="Cari berita..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleSearchToggle}>
                      <CloseIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'white',
                  borderRadius: 2,
                }
              }}
            />
          </Box>
        )}
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={isMobile ? mobileOpen : true}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 280,
            mt: { md: '64px' },
            height: { md: 'calc(100vh - 64px)' }
          },
        }}
      >
        {sidebarContent}
      </Drawer>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { navigate('/profile'); handleProfileMenuClose(); }}>
          <PersonIcon sx={{ mr: 1 }} /> Profile
        </MenuItem>
        <MenuItem onClick={() => { navigate('/saved'); handleProfileMenuClose(); }}>
          <BookmarkIcon sx={{ mr: 1 }} /> Tersimpan
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <LogoutIcon sx={{ mr: 1 }} /> Logout
        </MenuItem>
      </Menu>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          ml: { md: '280px' },
          mt: '64px',
          mb: { xs: '56px', md: 0 },
          p: 3
        }}
      >
        <Container maxWidth="xl">
          {/* Category Filter */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Kategori
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {categoriesData.map((category) => (
                <Chip
                  key={category.id}
                  label={category.name}
                  onClick={() => setSelectedCategory(category.name)}
                  color={selectedCategory === category.name ? 'primary' : 'default'}
                  variant={selectedCategory === category.name ? 'filled' : 'outlined'}
                  sx={{
                    backgroundColor: selectedCategory === category.name ? category.color : 'transparent',
                    borderColor: category.color,
                    color: selectedCategory === category.name ? 'white' : category.color,
                    '&:hover': {
                      backgroundColor: category.color,
                      color: 'white'
                    }
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* News Grid */}
          <Grid container spacing={3}>
            {loading ? (
              // Loading skeletons
              Array.from(new Array(6)).map((_, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Card>
                    <Skeleton variant="rectangular" height={200} />
                    <CardContent>
                      <Skeleton variant="text" height={32} />
                      <Skeleton variant="text" height={20} />
                      <Skeleton variant="text" height={20} width="60%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : filteredNews.length > 0 ? (
              filteredNews.map((news) => (
                <Grid item xs={12} sm={6} md={4} key={news.id}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      cursor: 'pointer',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4
                      }
                    }}
                    onClick={() => navigate(`/news/${news.id}`)}
                  >
                    <CardMedia
                      component="img"
                      height="200"
                      image={news.image || 'https://via.placeholder.com/400x200?text=No+Image'}
                      alt={news.title}
                      sx={{ objectFit: 'cover' }}
                    />
                    <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                      <Box sx={{ mb: 1 }}>
                        <Chip
                          label={news.category_name || news.category}
                          size="small"
                          sx={{
                            backgroundColor: news.category_color || '#3B82F6',
                            color: 'white',
                            fontSize: '0.75rem'
                          }}
                        />
                      </Box>

                      <Typography
                        variant="h6"
                        component="h2"
                        sx={{
                          mb: 1,
                          fontWeight: 'bold',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {news.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          flexGrow: 1,
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {news.description}
                      </Typography>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(news.published_at || news.date || news.created_at)}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <ViewIcon sx={{ fontSize: 16 }} />
                            <Typography variant="caption">{news.views || 0}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <FavoriteIcon sx={{ fontSize: 16 }} />
                            <Typography variant="caption">{news.likes || 0}</Typography>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <NewsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    {searchQuery ? 'Tidak ada berita yang ditemukan' : 'Belum ada berita'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchQuery ? 'Coba kata kunci lain' : 'Berita akan muncul di sini'}
                  </Typography>
                </Box>
              </Grid>
            )}
          </Grid>
        </Container>
      </Box>

      {/* Bottom Navigation (Mobile) */}
      {isMobile && (
        <Paper sx={{ position: 'fixed', bottom: 0, left: 0, right: 0, zIndex: 1000 }} elevation={3}>
          <BottomNavigation
            value={bottomNavValue}
            onChange={handleBottomNavChange}
            showLabels
          >
            <BottomNavigationAction label="Beranda" icon={<HomeIcon />} />
            <BottomNavigationAction label="Berita" icon={<NewsIcon />} />
            <BottomNavigationAction label="Video" icon={<VideoIcon />} />
            <BottomNavigationAction
              label="Simpan"
              icon={<BookmarkIcon />}
            />
            <BottomNavigationAction
              label={user ? "Profile" : "Login"}
              icon={user ? <Avatar sx={{ width: 24, height: 24 }}>{user.name?.charAt(0)}</Avatar> : <PersonIcon />}
            />
          </BottomNavigation>
        </Paper>
      )}
    </Box>
  );
};

export default DataNews;
