{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport LandingPage from './pages/user/LandingPage';\nimport VideoPage from './pages/user/VideoPage';\nimport VideoPlay from './pages/user/VideoPlay';\nimport DataNews from './pages/user/DataNews';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Register from './pages/admin/auth/Register';\nimport AdminLogin from './pages/admin/auth/AdminLogin';\n// User Auth Components\nimport UserLogin from './pages/user/auth/Login';\nimport UserRegister from './pages/user/auth/Register';\nimport ForgotPassword from './pages/user/auth/ForgotPassword';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/home\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/video\",\n          element: /*#__PURE__*/_jsxDEV(VideoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/video-play\",\n          element: /*#__PURE__*/_jsxDEV(VideoPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/data-news\",\n          element: /*#__PURE__*/_jsxDEV(DataNews, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/news\",\n          element: /*#__PURE__*/_jsxDEV(DataNews, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/saved\",\n          element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/bookmark\",\n          element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/login\",\n          element: /*#__PURE__*/_jsxDEV(UserLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/register\",\n          element: /*#__PURE__*/_jsxDEV(UserRegister, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/forgot-password\",\n          element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/login\",\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboardRedirect, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "LandingPage", "VideoPage", "VideoPlay", "DataNews", "Saved", "Register", "AdminLogin", "UserLogin", "UserRegister", "ForgotPassword", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "AdminDashboardRedirect", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport LandingPage from './pages/user/LandingPage';\nimport VideoPage from './pages/user/VideoPage';\nimport VideoPlay from './pages/user/VideoPlay';\nimport DataNews from './pages/user/DataNews';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Register from './pages/admin/auth/Register';\nimport AdminLogin from './pages/admin/auth/AdminLogin';\n// User Auth Components\nimport UserLogin from './pages/user/auth/Login';\nimport UserRegister from './pages/user/auth/Register';\nimport ForgotPassword from './pages/user/auth/ForgotPassword';\n\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n        {/* User Routes */}\n        <Route path=\"/\" element={<LandingPage />} />\n        <Route path=\"/home\" element={<LandingPage />} />\n        <Route path=\"/video\" element={<VideoPage />} />\n        <Route path=\"/video-play\" element={<VideoPlay />} />\n        <Route path=\"/data-news\" element={<DataNews />} />\n        <Route path=\"/news\" element={<DataNews />} />\n        <Route path=\"/saved\" element={<Saved />} />\n        <Route path=\"/bookmark\" element={<Saved />} />\n\n        {/* User Auth Routes */}\n        <Route path=\"/auth/login\" element={<UserLogin />} />\n        <Route path=\"/auth/register\" element={<UserRegister />} />\n        <Route path=\"/auth/forgot-password\" element={<ForgotPassword />} />\n\n        {/* Admin Routes */}\n        <Route path=\"/admin\" element={<AdminDashboardRedirect />} />\n        <Route path=\"/admin/login\" element={<AdminLogin />} />\n        <Route path=\"/admin/register\" element={<Register />} />\n\n        {/* Dashboard Routes - Clean URLs without .php */}\n        <Route path=\"/dashboard\" element={<AdminDashboardRedirect />} />\n        <Route path=\"/admin/dashboard\" element={<AdminDashboardRedirect />} />\n\n        {/* Fallback Route */}\n        <Route path=\"*\" element={<LandingPage />} />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAO,WAAW;AAClB,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,UAAU,MAAM,+BAA+B;AACtD;AACA,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACZ,YAAY;IAAAc,QAAA,eACXF,OAAA,CAACf,MAAM;MAAAiB,QAAA,eACLF,OAAA,CAACd,MAAM;QAAAgB,QAAA,gBAEPF,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACX,WAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEJ,OAAA,CAACX,WAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACV,SAAS;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEJ,OAAA,CAACT,SAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACR,QAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEJ,OAAA,CAACR,QAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACP,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACP,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG9CR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEJ,OAAA,CAACJ,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEJ,OAAA,CAACH,YAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEJ,OAAA,CAACF,cAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGnER,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEJ,OAAA,CAACL,UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAEJ,OAAA,CAACN,QAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGvDR,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChER,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAEJ,OAAA,CAACS,sBAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtER,OAAA,CAACb,KAAK;UAACgB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACX,WAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACE,EAAA,GAnCQT,GAAG;AAqCZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}