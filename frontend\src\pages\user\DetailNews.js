import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  IconButton,
  Avatar,
  Button,
  useMediaQuery,
  useTheme,
  Skeleton,
  Chip,
  Card,
  CardContent,
  CardMedia,
  Fab
} from '@mui/material';
import {
  Home as HomeIcon,
  Bookmark as BookmarkIcon,
  VideoLibrary as VideoIcon,
  Share as ShareIcon,
  Favorite as FavoriteIcon,
  Visibility as ViewIcon,
  ArrowBack as ArrowBackIcon,
  FavoriteBorder as FavoriteBorderIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const DetailNews = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  // States
  const [newsData, setNewsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);

  // Fetch news detail
  useEffect(() => {
    if (id) {
      fetchNewsDetail();
      incrementViews();
    }
  }, [id]);

  const fetchNewsDetail = async () => {
    try {
      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${id}`);
      const data = await response.json();

      if (data.success && data.data) {
        setNewsData(data.data);
        setLikeCount(data.data.likes || 0);
      } else {
        console.error('News not found');
        navigate('/');
      }
    } catch (error) {
      console.error('Error fetching news:', error);
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const incrementViews = async () => {
    if (!id) {
      console.error('No news ID available for increment views');
      return;
    }

    try {
      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'increment_views',
          id: id
        })
      });
    } catch (error) {
      console.log('Could not increment views:', error);
    }
  };

  const handleLike = async () => {
    if (!id) {
      console.error('No news ID available for like');
      return;
    }

    try {
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'toggle_like',
          id: id
        })
      });

      const data = await response.json();
      if (data.success) {
        setIsLiked(!isLiked);
        setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
      } else {
        console.error('Like error:', data.message);
      }
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const handleBookmark = async () => {
    if (!id) {
      console.error('No news ID available for bookmark');
      return;
    }

    try {
      const action = isBookmarked ? 'remove_saved_news' : 'add_saved_news';
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: action,
          id: id
        })
      });

      const data = await response.json();
      if (data.success) {
        setIsBookmarked(!isBookmarked);
        alert(isBookmarked ? 'Berita dihapus dari bookmark' : 'Berita ditambahkan ke bookmark');
      } else {
        console.error('Bookmark error:', data.message);
        alert('Gagal mengupdate bookmark: ' + data.message);
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      alert('Terjadi kesalahan saat mengupdate bookmark');
    }
  };

  const handleShare = () => {
    if (navigator.share && newsData) {
      navigator.share({
        title: newsData.title,
        text: newsData.description,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link berhasil disalin!');
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getImageUrl = (imagePath, imageBase64 = '') => {
    console.log('DetailNews - Processing image path:', imagePath);
    console.log('DetailNews - Has base64 data:', !!imageBase64);

    // If no image data at all
    if (!imagePath && !imageBase64) {
      return 'https://via.placeholder.com/400x200?text=No+Image';
    }

    // If it's already a data URL (base64), return as is
    if (imagePath && imagePath.startsWith('data:')) {
      console.log('DetailNews - Using data URL:', imagePath.substring(0, 50) + '...');
      return imagePath;
    }

    // If it's already a full HTTP URL, return as is
    if (imagePath && imagePath.startsWith('http')) {
      console.log('DetailNews - Using full URL:', imagePath);
      return imagePath;
    }

    // If we have a file path, try to use it
    if (imagePath) {
      // If it's already a full path starting with /react-news/frontend/uploads/, use it directly
      if (imagePath.startsWith('/react-news/frontend/uploads/')) {
        const url = `http://localhost${imagePath}`;
        console.log('DetailNews - Using direct path URL:', url);
        return url;
      }

      // Extract filename from various path formats
      let filename = '';
      if (imagePath.startsWith('/react-news/uploads/')) {
        filename = imagePath.replace('/react-news/uploads/', '');
      } else if (imagePath.startsWith('/uploads/')) {
        filename = imagePath.replace('/uploads/', '');
      } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {
        filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');
      } else if (imagePath.startsWith('uploads/')) {
        filename = imagePath.replace('uploads/', '');
      } else if (imagePath.startsWith('assets/news/')) {
        filename = imagePath.replace('assets/news/', '');
      } else if (!imagePath.includes('/')) {
        // Just filename
        filename = imagePath;
      } else {
        // Extract filename from any other path
        filename = imagePath.split('/').pop();
      }

      const url = `http://localhost/react-news/frontend/uploads/${filename}`;
      console.log('DetailNews - Using constructed URL:', url);
      return url;
    }

    // If no file path but we have base64, create data URL
    if (imageBase64) {
      const dataUrl = `data:image/jpeg;base64,${imageBase64}`;
      console.log('DetailNews - Using base64 data URL');
      return dataUrl;
    }

    return 'https://via.placeholder.com/400x200?text=No+Image'; // Final fallback
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <AppBar position="fixed">
          <Toolbar>
            <IconButton color="inherit" onClick={() => navigate('/')}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              Loading...
            </Typography>
          </Toolbar>
        </AppBar>
        
        <Container maxWidth="md" sx={{ mt: 10, mb: 4 }}>
          <Skeleton variant="rectangular" height={300} sx={{ mb: 2 }} />
          <Skeleton variant="text" height={40} sx={{ mb: 1 }} />
          <Skeleton variant="text" height={20} sx={{ mb: 2 }} />
          <Skeleton variant="text" height={200} />
        </Container>
      </Box>
    );
  }

  if (!newsData) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="h5" color="text.secondary">
          Berita tidak ditemukan
        </Typography>
        <Button onClick={() => navigate('/')} sx={{ mt: 2 }}>
          Kembali ke Beranda
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Top Navigation */}
      <AppBar position="fixed">
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
          
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            Detail Berita
          </Typography>

          {/* Video Button */}
          <IconButton color="inherit" onClick={() => navigate('/video')}>
            <VideoIcon />
          </IconButton>

          {/* Profile/Login */}
          {user ? (
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
              {user.name?.charAt(0).toUpperCase()}
            </Avatar>
          ) : (
            <Button color="inherit" onClick={() => navigate('/auth/login')}>
              Login
            </Button>
          )}
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="md" sx={{ mt: 10, mb: { xs: 8, md: 4 }, px: { xs: 2, md: 3 } }}>
        <Card sx={{ overflow: 'hidden', boxShadow: 3 }}>
          {/* Featured Image */}
          {newsData.image && (
            <CardMedia
              component="img"
              height="400"
              image={getImageUrl(newsData.image)}
              alt={newsData.title}
              sx={{ objectFit: 'cover' }}
            />
          )}
          
          <CardContent sx={{ p: { xs: 2, md: 4 } }}>
            {/* Category */}
            <Box sx={{ mb: 2 }}>
              <Chip
                label={newsData.category_name || newsData.category || 'Berita'}
                sx={{
                  backgroundColor: newsData.category_color || '#3B82F6',
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
            </Box>
            
            {/* Title */}
            <Typography 
              variant="h4" 
              component="h1" 
              sx={{ 
                fontWeight: 'bold', 
                mb: 2,
                fontSize: { xs: '1.5rem', md: '2rem' },
                lineHeight: 1.3
              }}
            >
              {newsData.title}
            </Typography>
            
            {/* Meta Info */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ViewIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  {newsData.views || 0} views
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <FavoriteIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  {likeCount} likes
                </Typography>
              </Box>
              
              <Typography variant="caption" color="text.secondary">
                {formatDate(newsData.published_at || newsData.date || newsData.created_at)}
              </Typography>
            </Box>
            
            {/* Description */}
            {newsData.description && (
              <Box sx={{ mb: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="body1" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                  {newsData.description}
                </Typography>
              </Box>
            )}
            
            {/* Content */}
            <Box 
              sx={{ 
                '& p': { mb: 2, lineHeight: 1.7 },
                '& h1, & h2, & h3': { mt: 3, mb: 2, fontWeight: 'bold' },
                '& img': { maxWidth: '100%', height: 'auto', borderRadius: 2, my: 2 },
                '& ul, & ol': { pl: 3, mb: 2 },
                '& li': { mb: 1 }
              }}
              dangerouslySetInnerHTML={{ __html: newsData.content }}
            />
          </CardContent>
        </Card>
      </Container>

      {/* Floating Action Buttons */}
      <Box sx={{ position: 'fixed', bottom: { xs: 80, md: 20 }, right: 20, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Fab
          color={isLiked ? "error" : "default"}
          size="medium"
          onClick={handleLike}
          sx={{ boxShadow: 3 }}
        >
          {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}
        </Fab>
        
        <Fab
          color={isBookmarked ? "success" : "primary"}
          size="medium"
          onClick={handleBookmark}
          sx={{ boxShadow: 3 }}
        >
          <BookmarkIcon />
        </Fab>
        
        <Fab
          color="secondary"
          size="medium"
          onClick={handleShare}
          sx={{ boxShadow: 3 }}
        >
          <ShareIcon />
        </Fab>
      </Box>
    </Box>
  );
};

export default DetailNews;
