{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { Box, Card, CardContent, TextField, Button, Typography, Alert, InputAdornment, IconButton, Avatar, Dialog, DialogContent, DialogActions, Snackbar } from '@mui/material';\nimport { Visibility, VisibilityOff, Email, Lock, Person, ArrowBack, ContentCopy, CheckCircle } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    register\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showTokenModal, setShowTokenModal] = useState(false);\n  const [jwtToken, setJwtToken] = useState('');\n  const [copySuccess, setCopySuccess] = useState(false);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password\n        })\n      });\n      const result = await response.json();\n      if (result.success) {\n        setJwtToken(result.token);\n        setShowTokenModal(true);\n        setSuccess('Registrasi berhasil! Silakan simpan JWT token untuk recovery password.');\n      } else {\n        setError(result.message || 'Registrasi gagal');\n      }\n    } catch (error) {\n      console.error('Register error:', error);\n      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');\n    }\n    setLoading(false);\n  };\n  const handleCopyToken = async () => {\n    try {\n      await navigator.clipboard.writeText(jwtToken);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy token:', err);\n    }\n  };\n  const handleCloseModal = () => {\n    setShowTokenModal(false);\n    navigate('/auth/login');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      backgroundColor: '#ffffff',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      p: {\n        xs: 2,\n        sm: 3,\n        md: 4\n      },\n      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: {\n          xs: '100%',\n          sm: 420,\n          md: 450\n        },\n        width: '100%',\n        borderRadius: {\n          xs: 2,\n          md: 4\n        },\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n        border: '1px solid rgba(0, 0, 0, 0.08)',\n        backgroundColor: '#ffffff'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: {\n            xs: 3,\n            sm: 4,\n            md: 5\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: {\n              xs: 3,\n              md: 4\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => navigate('/'),\n            sx: {\n              position: 'absolute',\n              top: {\n                xs: 12,\n                md: 16\n              },\n              left: {\n                xs: 12,\n                md: 16\n              },\n              backgroundColor: 'rgba(0, 0, 0, 0.04)',\n              '&:hover': {\n                backgroundColor: 'rgba(0, 0, 0, 0.08)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: {\n                xs: 56,\n                md: 72\n              },\n              height: {\n                xs: 56,\n                md: 72\n              },\n              mx: 'auto',\n              mb: {\n                xs: 2,\n                md: 3\n              },\n              bgcolor: 'secondary.main',\n              boxShadow: '0 8px 32px rgba(220, 0, 78, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Person, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"700\",\n            color: \"text.primary\",\n            gutterBottom: true,\n            sx: {\n              fontSize: {\n                xs: '1.75rem',\n                md: '2.125rem'\n              }\n            },\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            },\n            children: \"Join us today and stay updated with latest news\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3,\n            borderRadius: 2,\n            '& .MuiAlert-message': {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            }\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mb: 3,\n            borderRadius: 2,\n            '& .MuiAlert-message': {\n              fontSize: {\n                xs: '0.875rem',\n                md: '1rem'\n              }\n            }\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"name\",\n            label: \"Full Name\",\n            placeholder: \"Masukkan nama lengkap Anda\",\n            value: formData.name,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Person, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"email\",\n            type: \"email\",\n            label: \"Email Address\",\n            placeholder: \"Masukkan alamat email Anda\",\n            value: formData.email,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Email, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"password\",\n            type: showPassword ? 'text' : 'password',\n            label: \"Password\",\n            placeholder: \"Masukkan password Anda (min. 6 karakter)\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => setShowPassword(!showPassword),\n                    edge: \"end\",\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 41\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            name: \"confirmPassword\",\n            type: showConfirmPassword ? 'text' : 'password',\n            label: \"Confirm Password\",\n            placeholder: \"Konfirmasi password Anda\",\n            value: formData.confirmPassword,\n            onChange: handleChange,\n            required: true,\n            sx: {\n              mb: 3,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 2,\n                backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                },\n                '&.Mui-focused': {\n                  backgroundColor: '#ffffff'\n                }\n              }\n            },\n            slotProps: {\n              input: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Lock, {\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                    edge: \"end\",\n                    children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 48\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 68\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            disabled: loading,\n            sx: {\n              mb: 3,\n              py: {\n                xs: 1.5,\n                md: 2\n              },\n              borderRadius: 2,\n              textTransform: 'none',\n              fontSize: {\n                xs: 16,\n                md: 18\n              },\n              fontWeight: 600,\n              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n              '&:hover': {\n                boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                transform: 'translateY(-2px)'\n              },\n              '&:disabled': {\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n              },\n              transition: 'all 0.3s ease'\n            },\n            children: loading ? 'Creating Account...' : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/auth/login\",\n              style: {\n                textDecoration: 'none',\n                color: '#1976d2',\n                fontWeight: 600\n              },\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showTokenModal,\n      onClose: handleCloseModal,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          p: 3,\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          sx: {\n            color: 'success.main',\n            fontSize: 48,\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          fontWeight: \"bold\",\n          component: \"h2\",\n          children: \"Registration Successful!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Important:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), \" Save this JWT token securely. You'll need it to reset your password if you forget it.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"600\",\n          gutterBottom: true,\n          children: \"Your JWT Token:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: 'grey.100',\n            borderRadius: 2,\n            border: '1px solid',\n            borderColor: 'grey.300',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              wordBreak: 'break-all',\n              fontFamily: 'monospace',\n              fontSize: '0.875rem'\n            },\n            children: jwtToken\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ContentCopy, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 24\n          }, this),\n          onClick: handleCopyToken,\n          sx: {\n            mb: 2\n          },\n          children: copySuccess ? 'Token Copied!' : 'Copy Token'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          onClick: handleCloseModal,\n          sx: {\n            py: 1.5\n          },\n          children: \"Continue to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: copySuccess,\n      autoHideDuration: 2000,\n      onClose: () => setCopySuccess(false),\n      message: \"Token copied to clipboard!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"Yp6wTmu2Jo71g7rcFJRGzHo32Qo=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "useAuth", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "InputAdornment", "IconButton", "Avatar", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "Visibility", "VisibilityOff", "Email", "Lock", "Person", "ArrowBack", "ContentCopy", "CheckCircle", "jsxDEV", "_jsxDEV", "Register", "_s", "navigate", "register", "formData", "setFormData", "name", "email", "password", "confirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showTokenModal", "setShowTokenModal", "jwtToken", "setJwtToken", "copySuccess", "setCopySuccess", "handleChange", "e", "target", "value", "validateForm", "length", "handleSubmit", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "result", "json", "token", "message", "console", "handleCopyToken", "navigator", "clipboard", "writeText", "setTimeout", "err", "handleCloseModal", "sx", "minHeight", "backgroundColor", "display", "alignItems", "justifyContent", "p", "xs", "sm", "md", "backgroundImage", "children", "max<PERSON><PERSON><PERSON>", "width", "borderRadius", "boxShadow", "border", "textAlign", "mb", "onClick", "position", "top", "left", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "mx", "bgcolor", "fontSize", "variant", "fontWeight", "color", "gutterBottom", "severity", "onSubmit", "fullWidth", "label", "placeholder", "onChange", "required", "slotProps", "input", "startAdornment", "type", "endAdornment", "edge", "size", "disabled", "py", "textTransform", "transform", "transition", "to", "style", "textDecoration", "open", "onClose", "pb", "component", "borderColor", "wordBreak", "fontFamily", "startIcon", "pt", "autoHideDuration", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Alert,\n  InputAdornment,\n  IconButton,\n  Avatar,\n  Dialog,\n  DialogContent,\n  DialogActions,\n  Snackbar\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Email,\n  Lock,\n  Person,\n  ArrowBack,\n  ContentCopy,\n  CheckCircle\n} from '@mui/icons-material';\n\nconst Register = () => {\n  const navigate = useNavigate();\n  const { register } = useAuth();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showTokenModal, setShowTokenModal] = useState(false);\n  const [jwtToken, setJwtToken] = useState('');\n  const [copySuccess, setCopySuccess] = useState(false);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password\n        })\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setJwtToken(result.token);\n        setShowTokenModal(true);\n        setSuccess('Registrasi berhasil! Silakan simpan JWT token untuk recovery password.');\n      } else {\n        setError(result.message || 'Registrasi gagal');\n      }\n    } catch (error) {\n      console.error('Register error:', error);\n      setError('Koneksi bermasalah. Pastikan backend server berjalan di port 5000.');\n    }\n\n    setLoading(false);\n  };\n\n  const handleCopyToken = async () => {\n    try {\n      await navigator.clipboard.writeText(jwtToken);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy token:', err);\n    }\n  };\n\n  const handleCloseModal = () => {\n    setShowTokenModal(false);\n    navigate('/auth/login');\n  };\n\n  return (\n    <Box sx={{\n      minHeight: '100vh',\n      backgroundColor: '#ffffff',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      p: { xs: 2, sm: 3, md: 4 },\n      backgroundImage: 'radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%)'\n    }}>\n      <Card sx={{\n        maxWidth: { xs: '100%', sm: 420, md: 450 },\n        width: '100%',\n        borderRadius: { xs: 2, md: 4 },\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n        border: '1px solid rgba(0, 0, 0, 0.08)',\n        backgroundColor: '#ffffff'\n      }}>\n        <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>\n          {/* Header */}\n          <Box sx={{ textAlign: 'center', mb: { xs: 3, md: 4 } }}>\n            <IconButton\n              onClick={() => navigate('/')}\n              sx={{\n                position: 'absolute',\n                top: { xs: 12, md: 16 },\n                left: { xs: 12, md: 16 },\n                backgroundColor: 'rgba(0, 0, 0, 0.04)',\n                '&:hover': {\n                  backgroundColor: 'rgba(0, 0, 0, 0.08)'\n                }\n              }}\n            >\n              <ArrowBack />\n            </IconButton>\n\n            <Avatar sx={{\n              width: { xs: 56, md: 72 },\n              height: { xs: 56, md: 72 },\n              mx: 'auto',\n              mb: { xs: 2, md: 3 },\n              bgcolor: 'secondary.main',\n              boxShadow: '0 8px 32px rgba(220, 0, 78, 0.3)'\n            }}>\n              <Person fontSize=\"large\" />\n            </Avatar>\n\n            <Typography\n              variant=\"h4\"\n              fontWeight=\"700\"\n              color=\"text.primary\"\n              gutterBottom\n              sx={{ fontSize: { xs: '1.75rem', md: '2.125rem' } }}\n            >\n              Create Account\n            </Typography>\n            <Typography\n              variant=\"body1\"\n              color=\"text.secondary\"\n              sx={{ fontSize: { xs: '0.875rem', md: '1rem' } }}\n            >\n              Join us today and stay updated with latest news\n            </Typography>\n          </Box>\n\n          {/* Error/Success Alert */}\n          {error && (\n            <Alert\n              severity=\"error\"\n              sx={{\n                mb: 3,\n                borderRadius: 2,\n                '& .MuiAlert-message': {\n                  fontSize: { xs: '0.875rem', md: '1rem' }\n                }\n              }}\n            >\n              {error}\n            </Alert>\n          )}\n\n          {success && (\n            <Alert\n              severity=\"success\"\n              sx={{\n                mb: 3,\n                borderRadius: 2,\n                '& .MuiAlert-message': {\n                  fontSize: { xs: '0.875rem', md: '1rem' }\n                }\n              }}\n            >\n              {success}\n            </Alert>\n          )}\n\n          {/* Register Form */}\n          <form onSubmit={handleSubmit}>\n            <TextField\n              fullWidth\n              name=\"name\"\n              label=\"Full Name\"\n              placeholder=\"Masukkan nama lengkap Anda\"\n              value={formData.name}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Person color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n\n            <TextField\n              fullWidth\n              name=\"email\"\n              type=\"email\"\n              label=\"Email Address\"\n              placeholder=\"Masukkan alamat email Anda\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Email color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n\n            <TextField\n              fullWidth\n              name=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              label=\"Password\"\n              placeholder=\"Masukkan password Anda (min. 6 karakter)\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Lock color=\"action\" />\n                    </InputAdornment>\n                  ),\n                  endAdornment: (\n                    <InputAdornment position=\"end\">\n                      <IconButton\n                        onClick={() => setShowPassword(!showPassword)}\n                        edge=\"end\"\n                      >\n                        {showPassword ? <VisibilityOff /> : <Visibility />}\n                      </IconButton>\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n\n            <TextField\n              fullWidth\n              name=\"confirmPassword\"\n              type={showConfirmPassword ? 'text' : 'password'}\n              label=\"Confirm Password\"\n              placeholder=\"Konfirmasi password Anda\"\n              value={formData.confirmPassword}\n              onChange={handleChange}\n              required\n              sx={{\n                mb: 3,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                  },\n                  '&.Mui-focused': {\n                    backgroundColor: '#ffffff'\n                  }\n                }\n              }}\n              slotProps={{\n                input: {\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Lock color=\"action\" />\n                    </InputAdornment>\n                  ),\n                  endAdornment: (\n                    <InputAdornment position=\"end\">\n                      <IconButton\n                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                        edge=\"end\"\n                      >\n                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\n                      </IconButton>\n                    </InputAdornment>\n                  ),\n                }\n              }}\n            />\n\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mb: 3,\n                py: { xs: 1.5, md: 2 },\n                borderRadius: 2,\n                textTransform: 'none',\n                fontSize: { xs: 16, md: 18 },\n                fontWeight: 600,\n                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',\n                '&:hover': {\n                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',\n                  transform: 'translateY(-2px)'\n                },\n                '&:disabled': {\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'\n                },\n                transition: 'all 0.3s ease'\n              }}\n            >\n              {loading ? 'Creating Account...' : 'Create Account'}\n            </Button>\n          </form>\n\n          {/* Links */}\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Already have an account?{' '}\n              <Link \n                to=\"/auth/login\" \n                style={{ \n                  textDecoration: 'none',\n                  color: '#1976d2',\n                  fontWeight: 600\n                }}\n              >\n                Sign In\n              </Link>\n            </Typography>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* JWT Token Modal */}\n      <Dialog\n        open={showTokenModal}\n        onClose={handleCloseModal}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <Box sx={{ textAlign: 'center', p: 3, pb: 1 }}>\n          <CheckCircle sx={{ color: 'success.main', fontSize: 48, mb: 2 }} />\n          <Typography variant=\"h5\" fontWeight=\"bold\" component=\"h2\">\n            Registration Successful!\n          </Typography>\n        </Box>\n        <DialogContent>\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            <Typography variant=\"body2\">\n              <strong>Important:</strong> Save this JWT token securely. You'll need it to reset your password if you forget it.\n            </Typography>\n          </Alert>\n\n          <Typography variant=\"subtitle1\" fontWeight=\"600\" gutterBottom>\n            Your JWT Token:\n          </Typography>\n\n          <Box sx={{\n            p: 2,\n            bgcolor: 'grey.100',\n            borderRadius: 2,\n            border: '1px solid',\n            borderColor: 'grey.300',\n            mb: 2\n          }}>\n            <Typography\n              variant=\"body2\"\n              sx={{\n                wordBreak: 'break-all',\n                fontFamily: 'monospace',\n                fontSize: '0.875rem'\n              }}\n            >\n              {jwtToken}\n            </Typography>\n          </Box>\n\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<ContentCopy />}\n            onClick={handleCopyToken}\n            sx={{ mb: 2 }}\n          >\n            {copySuccess ? 'Token Copied!' : 'Copy Token'}\n          </Button>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 0 }}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            onClick={handleCloseModal}\n            sx={{ py: 1.5 }}\n          >\n            Continue to Login\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Copy Success Snackbar */}\n      <Snackbar\n        open={copySuccess}\n        autoHideDuration={2000}\n        onClose={() => setCopySuccess(false)}\n        message=\"Token copied to clipboard!\"\n      />\n    </Box>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,QAAQ,QACH,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,WAAW,QACN,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAS,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAC9B,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC;IACvCkC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMsD,YAAY,GAAIC,CAAC,IAAK;IAC1BtB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACuB,CAAC,CAACC,MAAM,CAACtB,IAAI,GAAGqB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;IACFZ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1B,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDQ,QAAQ,CAAC,wBAAwB,CAAC;MAClC,OAAO,KAAK;IACd;IACA,IAAIb,QAAQ,CAACI,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;MAChCd,QAAQ,CAAC,6CAA6C,CAAC;MACvD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMe,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;IAErBf,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,EAAE;QACtEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBlC,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMiC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAACvB,OAAO,EAAE;QAClBK,WAAW,CAACkB,MAAM,CAACE,KAAK,CAAC;QACzBtB,iBAAiB,CAAC,IAAI,CAAC;QACvBF,UAAU,CAAC,wEAAwE,CAAC;MACtF,CAAC,MAAM;QACLF,QAAQ,CAACwB,MAAM,CAACG,OAAO,IAAI,kBAAkB,CAAC;MAChD;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCC,QAAQ,CAAC,oEAAoE,CAAC;IAChF;IAEAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC3B,QAAQ,CAAC;MAC7CG,cAAc,CAAC,IAAI,CAAC;MACpByB,UAAU,CAAC,MAAMzB,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC/C,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZN,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,EAAEmC,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,iBAAiB,CAAC,KAAK,CAAC;IACxBnB,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,oBACEH,OAAA,CAACvB,GAAG;IAAC6E,EAAE,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,CAAC,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MAC1BC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBACAjE,OAAA,CAACtB,IAAI;MAAC4E,EAAE,EAAE;QACRY,QAAQ,EAAE;UAAEL,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAI,CAAC;QAC1CI,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAEE,EAAE,EAAE;QAAE,CAAC;QAC9BM,SAAS,EAAE,sEAAsE;QACjFC,MAAM,EAAE,+BAA+B;QACvCd,eAAe,EAAE;MACnB,CAAE;MAAAS,QAAA,eACAjE,OAAA,CAACrB,WAAW;QAAC2E,EAAE,EAAE;UAAEM,CAAC,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAAE,QAAA,gBAE9CjE,OAAA,CAACvB,GAAG;UAAC6E,EAAE,EAAE;YAAEiB,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;cAAEX,EAAE,EAAE,CAAC;cAAEE,EAAE,EAAE;YAAE;UAAE,CAAE;UAAAE,QAAA,gBACrDjE,OAAA,CAACf,UAAU;YACTwF,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,GAAG,CAAE;YAC7BmD,EAAE,EAAE;cACFoB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE;gBAAEd,EAAE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAG,CAAC;cACvBa,IAAI,EAAE;gBAAEf,EAAE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAG,CAAC;cACxBP,eAAe,EAAE,qBAAqB;cACtC,SAAS,EAAE;gBACTA,eAAe,EAAE;cACnB;YACF,CAAE;YAAAS,QAAA,eAEFjE,OAAA,CAACJ,SAAS;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEbhF,OAAA,CAACd,MAAM;YAACoE,EAAE,EAAE;cACVa,KAAK,EAAE;gBAAEN,EAAE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAG,CAAC;cACzBkB,MAAM,EAAE;gBAAEpB,EAAE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAG,CAAC;cAC1BmB,EAAE,EAAE,MAAM;cACVV,EAAE,EAAE;gBAAEX,EAAE,EAAE,CAAC;gBAAEE,EAAE,EAAE;cAAE,CAAC;cACpBoB,OAAO,EAAE,gBAAgB;cACzBd,SAAS,EAAE;YACb,CAAE;YAAAJ,QAAA,eACAjE,OAAA,CAACL,MAAM;cAACyF,QAAQ,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEThF,OAAA,CAAClB,UAAU;YACTuG,OAAO,EAAC,IAAI;YACZC,UAAU,EAAC,KAAK;YAChBC,KAAK,EAAC,cAAc;YACpBC,YAAY;YACZlC,EAAE,EAAE;cAAE8B,QAAQ,EAAE;gBAAEvB,EAAE,EAAE,SAAS;gBAAEE,EAAE,EAAE;cAAW;YAAE,CAAE;YAAAE,QAAA,EACrD;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhF,OAAA,CAAClB,UAAU;YACTuG,OAAO,EAAC,OAAO;YACfE,KAAK,EAAC,gBAAgB;YACtBjC,EAAE,EAAE;cAAE8B,QAAQ,EAAE;gBAAEvB,EAAE,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAO;YAAE,CAAE;YAAAE,QAAA,EAClD;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGL/D,KAAK,iBACJjB,OAAA,CAACjB,KAAK;UACJ0G,QAAQ,EAAC,OAAO;UAChBnC,EAAE,EAAE;YACFkB,EAAE,EAAE,CAAC;YACLJ,YAAY,EAAE,CAAC;YACf,qBAAqB,EAAE;cACrBgB,QAAQ,EAAE;gBAAEvB,EAAE,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAO;YACzC;UACF,CAAE;UAAAE,QAAA,EAEDhD;QAAK;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEA7D,OAAO,iBACNnB,OAAA,CAACjB,KAAK;UACJ0G,QAAQ,EAAC,SAAS;UAClBnC,EAAE,EAAE;YACFkB,EAAE,EAAE,CAAC;YACLJ,YAAY,EAAE,CAAC;YACf,qBAAqB,EAAE;cACrBgB,QAAQ,EAAE;gBAAEvB,EAAE,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAO;YACzC;UACF,CAAE;UAAAE,QAAA,EAED9C;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAGDhF,OAAA;UAAM0F,QAAQ,EAAEzD,YAAa;UAAAgC,QAAA,gBAC3BjE,OAAA,CAACpB,SAAS;YACR+G,SAAS;YACTpF,IAAI,EAAC,MAAM;YACXqF,KAAK,EAAC,WAAW;YACjBC,WAAW,EAAC,4BAA4B;YACxC/D,KAAK,EAAEzB,QAAQ,CAACE,IAAK;YACrBuF,QAAQ,EAAEnE,YAAa;YACvBoE,QAAQ;YACRzC,EAAE,EAAE;cACFkB,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BJ,YAAY,EAAE,CAAC;gBACfZ,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFwC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZlG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,OAAO;kBAAAT,QAAA,eAC9BjE,OAAA,CAACL,MAAM;oBAAC4F,KAAK,EAAC;kBAAQ;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAACpB,SAAS;YACR+G,SAAS;YACTpF,IAAI,EAAC,OAAO;YACZ4F,IAAI,EAAC,OAAO;YACZP,KAAK,EAAC,eAAe;YACrBC,WAAW,EAAC,4BAA4B;YACxC/D,KAAK,EAAEzB,QAAQ,CAACG,KAAM;YACtBsF,QAAQ,EAAEnE,YAAa;YACvBoE,QAAQ;YACRzC,EAAE,EAAE;cACFkB,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BJ,YAAY,EAAE,CAAC;gBACfZ,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFwC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZlG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,OAAO;kBAAAT,QAAA,eAC9BjE,OAAA,CAACP,KAAK;oBAAC8F,KAAK,EAAC;kBAAQ;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAACpB,SAAS;YACR+G,SAAS;YACTpF,IAAI,EAAC,UAAU;YACf4F,IAAI,EAAExF,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCiF,KAAK,EAAC,UAAU;YAChBC,WAAW,EAAC,0CAA0C;YACtD/D,KAAK,EAAEzB,QAAQ,CAACI,QAAS;YACzBqF,QAAQ,EAAEnE,YAAa;YACvBoE,QAAQ;YACRzC,EAAE,EAAE;cACFkB,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BJ,YAAY,EAAE,CAAC;gBACfZ,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFwC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZlG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,OAAO;kBAAAT,QAAA,eAC9BjE,OAAA,CAACN,IAAI;oBAAC6F,KAAK,EAAC;kBAAQ;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDoB,YAAY,eACVpG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,KAAK;kBAAAT,QAAA,eAC5BjE,OAAA,CAACf,UAAU;oBACTwF,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAAC,CAACD,YAAY,CAAE;oBAC9C0F,IAAI,EAAC,KAAK;oBAAApC,QAAA,EAETtD,YAAY,gBAAGX,OAAA,CAACR,aAAa;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGhF,OAAA,CAACT,UAAU;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAACpB,SAAS;YACR+G,SAAS;YACTpF,IAAI,EAAC,iBAAiB;YACtB4F,IAAI,EAAEtF,mBAAmB,GAAG,MAAM,GAAG,UAAW;YAChD+E,KAAK,EAAC,kBAAkB;YACxBC,WAAW,EAAC,0BAA0B;YACtC/D,KAAK,EAAEzB,QAAQ,CAACK,eAAgB;YAChCoF,QAAQ,EAAEnE,YAAa;YACvBoE,QAAQ;YACRzC,EAAE,EAAE;cACFkB,EAAE,EAAE,CAAC;cACL,0BAA0B,EAAE;gBAC1BJ,YAAY,EAAE,CAAC;gBACfZ,eAAe,EAAE,qBAAqB;gBACtC,SAAS,EAAE;kBACTA,eAAe,EAAE;gBACnB,CAAC;gBACD,eAAe,EAAE;kBACfA,eAAe,EAAE;gBACnB;cACF;YACF,CAAE;YACFwC,SAAS,EAAE;cACTC,KAAK,EAAE;gBACLC,cAAc,eACZlG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,OAAO;kBAAAT,QAAA,eAC9BjE,OAAA,CAACN,IAAI;oBAAC6F,KAAK,EAAC;kBAAQ;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACjB;gBACDoB,YAAY,eACVpG,OAAA,CAAChB,cAAc;kBAAC0F,QAAQ,EAAC,KAAK;kBAAAT,QAAA,eAC5BjE,OAAA,CAACf,UAAU;oBACTwF,OAAO,EAAEA,CAAA,KAAM3D,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;oBAC5DwF,IAAI,EAAC,KAAK;oBAAApC,QAAA,EAETpD,mBAAmB,gBAAGb,OAAA,CAACR,aAAa;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGhF,OAAA,CAACT,UAAU;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAEpB;YACF;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAACnB,MAAM;YACLsH,IAAI,EAAC,QAAQ;YACbR,SAAS;YACTN,OAAO,EAAC,WAAW;YACnBiB,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAExF,OAAQ;YAClBuC,EAAE,EAAE;cACFkB,EAAE,EAAE,CAAC;cACLgC,EAAE,EAAE;gBAAE3C,EAAE,EAAE,GAAG;gBAAEE,EAAE,EAAE;cAAE,CAAC;cACtBK,YAAY,EAAE,CAAC;cACfqC,aAAa,EAAE,MAAM;cACrBrB,QAAQ,EAAE;gBAAEvB,EAAE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAG,CAAC;cAC5BuB,UAAU,EAAE,GAAG;cACfjB,SAAS,EAAE,oCAAoC;cAC/C,SAAS,EAAE;gBACTA,SAAS,EAAE,qCAAqC;gBAChDqC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZrC,SAAS,EAAE;cACb,CAAC;cACDsC,UAAU,EAAE;YACd,CAAE;YAAA1C,QAAA,EAEDlD,OAAO,GAAG,qBAAqB,GAAG;UAAgB;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGPhF,OAAA,CAACvB,GAAG;UAAC6E,EAAE,EAAE;YAAEiB,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,eAC/BjE,OAAA,CAAClB,UAAU;YAACuG,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAtB,QAAA,GAAC,0BACzB,EAAC,GAAG,eAC5BjE,OAAA,CAACzB,IAAI;cACHqI,EAAE,EAAC,aAAa;cAChBC,KAAK,EAAE;gBACLC,cAAc,EAAE,MAAM;gBACtBvB,KAAK,EAAE,SAAS;gBAChBD,UAAU,EAAE;cACd,CAAE;cAAArB,QAAA,EACH;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhF,OAAA,CAACb,MAAM;MACL4H,IAAI,EAAE1F,cAAe;MACrB2F,OAAO,EAAE3D,gBAAiB;MAC1Ba,QAAQ,EAAC,IAAI;MACbyB,SAAS;MAAA1B,QAAA,gBAETjE,OAAA,CAACvB,GAAG;QAAC6E,EAAE,EAAE;UAAEiB,SAAS,EAAE,QAAQ;UAAEX,CAAC,EAAE,CAAC;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAAhD,QAAA,gBAC5CjE,OAAA,CAACF,WAAW;UAACwD,EAAE,EAAE;YAAEiC,KAAK,EAAE,cAAc;YAAEH,QAAQ,EAAE,EAAE;YAAEZ,EAAE,EAAE;UAAE;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEhF,OAAA,CAAClB,UAAU;UAACuG,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAAC4B,SAAS,EAAC,IAAI;UAAAjD,QAAA,EAAC;QAE1D;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNhF,OAAA,CAACZ,aAAa;QAAA6E,QAAA,gBACZjE,OAAA,CAACjB,KAAK;UAAC0G,QAAQ,EAAC,SAAS;UAACnC,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACtCjE,OAAA,CAAClB,UAAU;YAACuG,OAAO,EAAC,OAAO;YAAApB,QAAA,gBACzBjE,OAAA;cAAAiE,QAAA,EAAQ;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0FAC7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERhF,OAAA,CAAClB,UAAU;UAACuG,OAAO,EAAC,WAAW;UAACC,UAAU,EAAC,KAAK;UAACE,YAAY;UAAAvB,QAAA,EAAC;QAE9D;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhF,OAAA,CAACvB,GAAG;UAAC6E,EAAE,EAAE;YACPM,CAAC,EAAE,CAAC;YACJuB,OAAO,EAAE,UAAU;YACnBf,YAAY,EAAE,CAAC;YACfE,MAAM,EAAE,WAAW;YACnB6C,WAAW,EAAE,UAAU;YACvB3C,EAAE,EAAE;UACN,CAAE;UAAAP,QAAA,eACAjE,OAAA,CAAClB,UAAU;YACTuG,OAAO,EAAC,OAAO;YACf/B,EAAE,EAAE;cACF8D,SAAS,EAAE,WAAW;cACtBC,UAAU,EAAE,WAAW;cACvBjC,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EAED1C;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhF,OAAA,CAACnB,MAAM;UACL8G,SAAS;UACTN,OAAO,EAAC,UAAU;UAClBiC,SAAS,eAAEtH,OAAA,CAACH,WAAW;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BP,OAAO,EAAE1B,eAAgB;UACzBO,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,EAEbxC,WAAW,GAAG,eAAe,GAAG;QAAY;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAChBhF,OAAA,CAACX,aAAa;QAACiE,EAAE,EAAE;UAAEM,CAAC,EAAE,CAAC;UAAE2D,EAAE,EAAE;QAAE,CAAE;QAAAtD,QAAA,eACjCjE,OAAA,CAACnB,MAAM;UACL8G,SAAS;UACTN,OAAO,EAAC,WAAW;UACnBZ,OAAO,EAAEpB,gBAAiB;UAC1BC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAI,CAAE;UAAAvC,QAAA,EACjB;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThF,OAAA,CAACV,QAAQ;MACPyH,IAAI,EAAEtF,WAAY;MAClB+F,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAAC,KAAK,CAAE;MACrCmB,OAAO,EAAC;IAA4B;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA9cID,QAAQ;EAAA,QACK3B,WAAW,EACPE,OAAO;AAAA;AAAAiJ,EAAA,GAFxBxH,QAAQ;AAgdd,eAAeA,QAAQ;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}