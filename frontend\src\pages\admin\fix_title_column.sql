-- Fix title column length in posts table
-- Run this SQL to fix the "Data too long for column 'title'" error

USE react_news;

-- Check current column structure
DESCRIBE posts;

-- Increase title column length from VARCHAR(255) to VARCHAR(500)
ALTER TABLE posts MODIFY COLUMN title VARCHAR(500) NOT NULL;

-- Also increase slug column length for consistency
ALTER TABLE posts MODIFY COLUMN slug VARCHAR(500) NOT NULL;

-- Increase meta_title column length as well
ALTER TABLE posts MODIFY COLUMN meta_title VARCHAR(500) NULL;

-- Show updated structure
DESCRIBE posts;

-- Show success message
SELECT 'Posts table title column updated successfully! Now supports longer titles.' as message;
