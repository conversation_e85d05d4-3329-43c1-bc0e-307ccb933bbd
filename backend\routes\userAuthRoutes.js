const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/db');

// Register user
router.post('/register', async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validation
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nama, email, dan password harus diisi'
      });
    }

    if (!email.includes('@')) {
      return res.status(400).json({
        success: false,
        message: 'Format email tidak valid'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password minimal 6 karakter'
      });
    }

    // Check if email already exists
    db.query('SELECT id FROM users WHERE email = ?', [email], async (err, results) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({
          success: false,
          message: 'Database error'
        });
      }

      if (results.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Email sudah terdaftar'
        });
      }

      try {
        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Insert user
        db.query(
          'INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
          [name, email, hashedPassword, 'user', 'active'],
          (err, result) => {
            if (err) {
              console.error('Insert error:', err);
              return res.status(500).json({
                success: false,
                message: 'Gagal mendaftarkan user'
              });
            }

            // Generate JWT token for authentication
            const authToken = jwt.sign(
              {
                id: result.insertId,
                email: email,
                role: 'user'
              },
              process.env.JWT_SECRET || 'your-secret-key',
              { expiresIn: '24h' }
            );

            // Generate reset token for password recovery
            const resetToken = jwt.sign(
              {
                id: result.insertId,
                email: email,
                type: 'reset'
              },
              process.env.JWT_SECRET || 'your-secret-key',
              { expiresIn: '30d' }
            );

            res.status(201).json({
              success: true,
              message: 'Registrasi berhasil',
              token: resetToken, // Send reset token for password recovery
              authToken: authToken, // Send auth token for login
              user: {
                id: result.insertId,
                name: name,
                email: email,
                role: 'user'
              }
            });
          }
        );
      } catch (hashError) {
        console.error('Hash error:', hashError);
        return res.status(500).json({
          success: false,
          message: 'Error processing password'
        });
      }
    });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Login user
router.post('/login', (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email dan password harus diisi'
      });
    }

    // Find user
    db.query('SELECT * FROM users WHERE email = ? AND status = ?', [email, 'active'], async (err, results) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({
          success: false,
          message: 'Database error'
        });
      }

      if (results.length === 0) {
        return res.status(401).json({
          success: false,
          message: 'Email atau password salah'
        });
      }

      const user = results[0];

      try {
        // Compare password
        const isMatch = await bcrypt.compare(password, user.password);

        if (!isMatch) {
          return res.status(401).json({
            success: false,
            message: 'Email atau password salah'
          });
        }

        // Update last login
        db.query('UPDATE users SET last_login_at = NOW() WHERE id = ?', [user.id]);

        // Generate JWT token
        const token = jwt.sign(
          { 
            id: user.id, 
            email: user.email, 
            role: user.role 
          },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: '24h' }
        );

        res.json({
          success: true,
          message: 'Login berhasil',
          token: token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role
          }
        });
      } catch (compareError) {
        console.error('Password compare error:', compareError);
        return res.status(500).json({
          success: false,
          message: 'Error verifying password'
        });
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Verify token
router.post('/verify-token', (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token required'
      });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, decoded) => {
      if (err) {
        return res.status(401).json({
          success: false,
          message: 'Token tidak valid'
        });
      }

      // Get user data
      db.query('SELECT id, name, email, role FROM users WHERE id = ? AND status = ?', [decoded.id, 'active'], (err, results) => {
        if (err || results.length === 0) {
          return res.status(401).json({
            success: false,
            message: 'User tidak ditemukan'
          });
        }

        res.json({
          success: true,
          user: results[0]
        });
      });
    });
  } catch (error) {
    console.error('Verify token error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Forgot password
router.post('/forgot-password', (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email harus diisi'
      });
    }

    // Check if user exists
    db.query('SELECT id, name FROM users WHERE email = ?', [email], (err, results) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({
          success: false,
          message: 'Database error'
        });
      }

      if (results.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Email tidak ditemukan'
        });
      }

      const user = results[0];

      // Generate reset token (simple implementation)
      const resetToken = jwt.sign(
        { id: user.id, email: email, type: 'reset' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '1h' }
      );

      res.json({
        success: true,
        message: 'Token reset password berhasil dibuat',
        resetToken: resetToken,
        user: {
          name: user.name,
          email: email
        }
      });
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Reset password
router.post('/reset-password', (req, res) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Token dan password baru harus diisi'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password minimal 6 karakter'
      });
    }

    // Verify reset token
    jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', async (err, decoded) => {
      if (err || decoded.type !== 'reset') {
        return res.status(401).json({
          success: false,
          message: 'Token reset tidak valid atau sudah kadaluarsa'
        });
      }

      try {
        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update password
        db.query('UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?', [hashedPassword, decoded.id], (err) => {
          if (err) {
            console.error('Update password error:', err);
            return res.status(500).json({
              success: false,
              message: 'Gagal mengupdate password'
            });
          }

          res.json({
            success: true,
            message: 'Password berhasil direset'
          });
        });
      } catch (hashError) {
        console.error('Hash error:', hashError);
        return res.status(500).json({
          success: false,
          message: 'Error processing password'
        });
      }
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
