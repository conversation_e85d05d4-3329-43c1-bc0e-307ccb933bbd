<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    $stmt = $pdo->prepare('SELECT * FROM pengaturan WHERE id = 1');
    $stmt->execute();
    $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current pengaturan data:\n";
    echo json_encode($pengaturan, JSON_PRETTY_PRINT);
    
    if ($pengaturan && $pengaturan['logo_file_path']) {
        $logoPath = $pengaturan['logo_file_path'];
        $fullPath = __DIR__ . '/' . $logoPath;

        echo "\n\nLogo path from database: " . $logoPath . "\n";
        echo "Full file path: " . $fullPath . "\n";
        echo "File exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";

        if (file_exists($fullPath)) {
            echo "File size: " . filesize($fullPath) . " bytes\n";
        } else {
            // File doesn't exist, let's check for other logo files and update the database
            $uploadsDir = __DIR__ . '/uploads';
            $logoFiles = glob($uploadsDir . '/logo_*');

            if (!empty($logoFiles)) {
                $existingLogo = basename($logoFiles[0]);
                echo "Found existing logo file: " . $existingLogo . "\n";

                // Update database to use existing logo
                $stmt = $pdo->prepare('UPDATE pengaturan SET logo_file_path = ? WHERE id = 1');
                $stmt->execute(['uploads/' . $existingLogo]);
                echo "Updated database to use existing logo file.\n";
            } else {
                // No logo files found, use default
                echo "No logo files found, setting to default.\n";
                $stmt = $pdo->prepare('UPDATE pengaturan SET logo_file_path = ? WHERE id = 1');
                $stmt->execute(['uploads/default-logo.png']);
                echo "Updated database to use default logo.\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
