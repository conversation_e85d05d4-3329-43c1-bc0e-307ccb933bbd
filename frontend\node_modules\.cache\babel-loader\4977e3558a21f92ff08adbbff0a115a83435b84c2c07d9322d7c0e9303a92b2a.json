{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('authToken'));\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initAuth = async () => {\n      const storedToken = localStorage.getItem('authToken');\n      const storedUser = localStorage.getItem('userData');\n      if (storedToken && storedUser) {\n        try {\n          // Verify token with backend\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${storedToken}`\n            },\n            body: JSON.stringify({\n              action: 'verify_token',\n              token: storedToken\n            })\n          });\n          const data = await response.json();\n          if (data.success) {\n            setUser(JSON.parse(storedUser));\n            setToken(storedToken);\n          } else {\n            // Token is invalid, clear storage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('userData');\n            setUser(null);\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Auth verification failed:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    initAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: 'login',\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        setToken(data.token);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          message: data.message\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const register = async (name, email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: 'register',\n          name,\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n    setToken(null);\n  };\n  const forgotPassword = async email => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: 'forgot_password',\n          email\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const resetPassword = async (email, token, newPassword) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: 'reset_password',\n          email,\n          token,\n          newPassword\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    forgotPassword,\n    resetPassword,\n    isAuthenticated: !!user,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"OpfcLSUH+QvV1lR1/bshprk98VE=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "initAuth", "storedToken", "storedUser", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "action", "data", "json", "success", "parse", "removeItem", "error", "console", "login", "email", "password", "setItem", "message", "register", "name", "logout", "forgotPassword", "resetPassword", "newPassword", "value", "isAuthenticated", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('authToken'));\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initAuth = async () => {\n      const storedToken = localStorage.getItem('authToken');\n      const storedUser = localStorage.getItem('userData');\n\n      if (storedToken && storedUser) {\n        try {\n          // Verify token with backend\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${storedToken}`\n            },\n            body: JSON.stringify({\n              action: 'verify_token',\n              token: storedToken\n            })\n          });\n\n          const data = await response.json();\n\n          if (data.success) {\n            setUser(JSON.parse(storedUser));\n            setToken(storedToken);\n          } else {\n            // Token is invalid, clear storage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('userData');\n            setUser(null);\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Auth verification failed:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'login',\n          email,\n          password\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        setToken(data.token);\n        return { success: true, user: data.user };\n      } else {\n        return { success: false, message: data.message };\n      }\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const register = async (name, email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'register',\n          name,\n          email,\n          password\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n    setToken(null);\n  };\n\n  const forgotPassword = async (email) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'forgot_password',\n          email\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const resetPassword = async (email, token, newPassword) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'reset_password',\n          email,\n          token,\n          newPassword\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    forgotPassword,\n    resetPassword,\n    isAuthenticated: !!user,\n    isAdmin: user?.role === 'admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAACgB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;EACrE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMC,WAAW,GAAGL,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACrD,MAAMK,UAAU,GAAGN,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAEnD,IAAII,WAAW,IAAIC,UAAU,EAAE;QAC7B,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;YAClGC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUL,WAAW;YACxC,CAAC;YACDM,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBC,MAAM,EAAE,cAAc;cACtBhB,KAAK,EAAEO;YACT,CAAC;UACH,CAAC,CAAC;UAEF,MAAMU,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;UAElC,IAAID,IAAI,CAACE,OAAO,EAAE;YAChBpB,OAAO,CAACe,IAAI,CAACM,KAAK,CAACZ,UAAU,CAAC,CAAC;YAC/BP,QAAQ,CAACM,WAAW,CAAC;UACvB,CAAC,MAAM;YACL;YACAL,YAAY,CAACmB,UAAU,CAAC,WAAW,CAAC;YACpCnB,YAAY,CAACmB,UAAU,CAAC,UAAU,CAAC;YACnCtB,OAAO,CAAC,IAAI,CAAC;YACbE,QAAQ,CAAC,IAAI,CAAC;UAChB;QACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDpB,YAAY,CAACmB,UAAU,CAAC,WAAW,CAAC;UACpCnB,YAAY,CAACmB,UAAU,CAAC,UAAU,CAAC;UACnCtB,OAAO,CAAC,IAAI,CAAC;UACbE,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAClGC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE,OAAO;UACfS,KAAK;UACLC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMT,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBjB,YAAY,CAACyB,OAAO,CAAC,WAAW,EAAEV,IAAI,CAACjB,KAAK,CAAC;QAC7CE,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAEb,IAAI,CAACC,SAAS,CAACE,IAAI,CAACnB,IAAI,CAAC,CAAC;QAC3DC,OAAO,CAACkB,IAAI,CAACnB,IAAI,CAAC;QAClBG,QAAQ,CAACgB,IAAI,CAACjB,KAAK,CAAC;QACpB,OAAO;UAAEmB,OAAO,EAAE,IAAI;UAAErB,IAAI,EAAEmB,IAAI,CAACnB;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAEqB,OAAO,EAAE,KAAK;UAAES,OAAO,EAAEX,IAAI,CAACW;QAAQ,CAAC;MAClD;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAES,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAOC,IAAI,EAAEL,KAAK,EAAEC,QAAQ,KAAK;IAChD,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,8DAA8D,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE,UAAU;UAClBc,IAAI;UACJL,KAAK;UACLC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMT,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAES,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnB7B,YAAY,CAACmB,UAAU,CAAC,WAAW,CAAC;IACpCnB,YAAY,CAACmB,UAAU,CAAC,UAAU,CAAC;IACnCtB,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM+B,cAAc,GAAG,MAAOP,KAAK,IAAK;IACtC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAAC,8DAA8D,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE,iBAAiB;UACzBS;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMR,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAES,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMK,aAAa,GAAG,MAAAA,CAAOR,KAAK,EAAEzB,KAAK,EAAEkC,WAAW,KAAK;IACzD,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMC,KAAK,CAAC,8DAA8D,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE,gBAAgB;UACxBS,KAAK;UACLzB,KAAK;UACLkC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMjB,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAES,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMO,KAAK,GAAG;IACZrC,IAAI;IACJE,KAAK;IACLI,OAAO;IACPoB,KAAK;IACLK,QAAQ;IACRE,MAAM;IACNC,cAAc;IACdC,aAAa;IACbG,eAAe,EAAE,CAAC,CAACtC,IAAI;IACvBuC,OAAO,EAAE,CAAAvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK;EAC1B,CAAC;EAED,oBACEjD,OAAA,CAACC,WAAW,CAACiD,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC9C,GAAA,CA1KWF,YAAY;AAAAiD,EAAA,GAAZjD,YAAY;AA4KzB,eAAeL,WAAW;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}