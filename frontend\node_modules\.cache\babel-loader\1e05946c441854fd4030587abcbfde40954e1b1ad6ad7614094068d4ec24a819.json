{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\components\\\\Saved.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Box, Typography, Card, CardContent, CardMedia, IconButton, Chip, CircularProgress, AppBar, Toolbar, Avatar, Button, Drawer, Divider, Stack } from '@mui/material';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport ShareIcon from '@mui/icons-material/Share';\nimport HomeIcon from '@mui/icons-material/Home';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\n\n// Helper function to get correct image URL from database\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getImageUrl = imagePath => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/300x200/?news';\n  }\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    return imagePath;\n  }\n\n  // If it's already the correct path, use it directly\n  if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n    return `http://localhost${imagePath}`;\n  }\n\n  // Extract filename from any path format stored in database\n  let filename = '';\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    filename = imagePath.replace('/react-news/uploads/', '');\n  } else if (imagePath.startsWith('/uploads/')) {\n    filename = imagePath.replace('/uploads/', '');\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\n  } else if (imagePath.startsWith('admin/uploads/')) {\n    filename = imagePath.replace('admin/uploads/', '');\n  } else if (imagePath.startsWith('uploads/')) {\n    filename = imagePath.replace('uploads/', '');\n  } else if (imagePath.startsWith('assets/news/')) {\n    filename = imagePath.replace('assets/news/', '');\n  } else if (!imagePath.includes('/')) {\n    // Just filename from database\n    filename = imagePath;\n  } else {\n    // Extract filename from any other path\n    filename = imagePath.split('/').pop();\n  }\n\n  // Use consistent frontend/uploads path for all images\n  return `http://localhost/react-news/frontend/uploads/${filename}`;\n};\nconst Saved = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [savedNews, setSavedNews] = useState([]);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: ''\n  });\n  const [bottomValue, setBottomValue] = useState(2); // 2 = Simpan\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Load website settings from database (same as LandingPage)\n  useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan').then(res => res.json()).then(response => {\n      if (response.success && response.data) {\n        const data = response.data;\n        console.log('Website settings loaded:', data);\n\n        // Logo is now stored as base64 data in database\n        let logoPath = data.logo_file_path && data.logo_file_path.startsWith('data:') ? data.logo_file_path : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';\n        setKostum({\n          logo: logoPath,\n          title: data.nama_website || 'React News Portal',\n          description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'\n        });\n\n        // Update document title\n        document.title = `Berita Tersimpan - ${data.nama_website || 'React News Portal'}`;\n      } else {\n        throw new Error('Invalid API response');\n      }\n    }).catch(err => {\n      console.log('Database settings not available, using defaults:', err);\n      setKostum({\n        logo: '/logo192.png',\n        title: 'React News Portal',\n        description: 'Portal berita terkini dan terpercaya'\n      });\n      document.title = 'Berita Tersimpan - React News Portal';\n    });\n  }, []);\n\n  // Load saved news from database\n  useEffect(() => {\n    // Check if user is authenticated, if not show empty state but don't redirect\n    if (!isAuthenticated || !user) {\n      setLoading(false);\n      setSavedNews([]);\n      return;\n    }\n    setLoading(true);\n    fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news&user_id=${user.id}`).then(res => {\n      if (!res.ok) {\n        throw new Error(`HTTP error! status: ${res.status}`);\n      }\n      return res.text(); // Get as text first to debug\n    }).then(text => {\n      console.log('Raw response:', text);\n      try {\n        return JSON.parse(text);\n      } catch (e) {\n        console.error('JSON parse error:', e);\n        console.error('Response text:', text);\n        throw new Error('Invalid JSON response');\n      }\n    }).then(data => {\n      console.log('Saved news loaded:', data);\n      if (data.success && Array.isArray(data.data)) {\n        // Map the data to match expected format\n        const mappedData = data.data.map(item => {\n          console.log('📸 Image data from database:', {\n            id: item.id,\n            title: item.title,\n            originalImage: item.image,\n            processedImage: getImageUrl(item.image)\n          });\n          return {\n            id: item.id,\n            title: item.title,\n            description: item.description || item.content,\n            content: item.content,\n            image: item.image,\n            // Raw image path from database posts table\n            category: item.category_name || 'Umum',\n            date: item.created_at || item.date,\n            author: item.author_name || 'Admin',\n            slug: item.slug,\n            views: item.views || 0,\n            likes: item.likes || 0,\n            share: item.share || 0\n          };\n        });\n        setSavedNews(mappedData);\n      } else {\n        setSavedNews([]);\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error('Error loading saved news:', err);\n      setSavedNews([]);\n      setLoading(false);\n    });\n  }, [isAuthenticated, user, navigate]);\n  const handleBookmark = async news => {\n    if (!user) {\n      window.alert('Silakan login terlebih dahulu');\n      navigate('/auth/login');\n      return;\n    }\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: new URLSearchParams({\n          action: 'remove_saved_news',\n          id: news.id,\n          user_id: user.id\n        })\n      });\n      const result = await response.json();\n      if (result.success) {\n        setSavedNews(prev => prev.filter(n => n.id !== news.id));\n        window.alert('Berita dihapus dari bookmark');\n      } else {\n        window.alert('Gagal menghapus berita dari bookmark: ' + result.message);\n      }\n    } catch (error) {\n      console.error('Error removing bookmark:', error);\n      window.alert('Gagal menghapus berita dari bookmark');\n    }\n  };\n  const handleNewsClick = async newsId => {\n    // Increment views when clicking news card\n    try {\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: new URLSearchParams({\n          action: 'increment_views',\n          id: newsId\n        })\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/news/${newsId}`);\n  };\n  const handleShare = async news => {\n    try {\n      // Update share count in database\n      fetch(`http://localhost:5000/api/posts/${news.id}/share`, {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title.toLowerCase().replace(/[^a-z0-9\\s-]/g, '').replace(/\\s+/g, '-').replace(/-+/g, '-').trim();\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n      window.alert('Link berita berhasil disalin ke clipboard!');\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      window.alert('Gagal menyalin link berita');\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'blue.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 80,\n          px: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 28\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => setSidebarOpen(true),\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: () => setSidebarOpen(false),\n      sx: {\n        zIndex: 1400\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 300,\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setSidebarOpen(false),\n          sx: {\n            position: 'absolute',\n            right: 8,\n            top: 8\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 26\n            }, this),\n            onClick: () => {\n              navigate('/');\n              setSidebarOpen(false);\n            },\n            children: \"Beranda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 26\n            }, this),\n            children: \"Berita Tersimpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(AdminPanelSettingsIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 26\n            }, this),\n            onClick: () => {\n              window.open('/dashboard', '_blank');\n              setSidebarOpen(false);\n            },\n            children: \"Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 12,\n        px: 3,\n        pb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: '100%',\n          background: '#e1f5fe',\n          color: '#007bff',\n          textAlign: 'center',\n          fontWeight: 600,\n          fontSize: 16,\n          padding: '12px 0',\n          marginBottom: 3,\n          borderRadius: 2,\n          borderBottom: '1.5px solid #b3e5fc'\n        },\n        children: [\"Total Berita Disimpan: \", isAuthenticated && user ? savedNews.length : 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: 2,\n          paddingBottom: 10\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: 200\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this) : savedNews.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            padding: 5\n          },\n          children: [/*#__PURE__*/_jsxDEV(BookmarkIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), !isAuthenticated || !user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: 'grey.600',\n                mb: 1\n              },\n              children: \"Silakan login untuk melihat berita tersimpan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.500',\n                mb: 3\n              },\n              children: \"Login untuk menyimpan dan mengakses berita favorit Anda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              onClick: () => navigate('/auth/login'),\n              sx: {\n                borderRadius: 2,\n                mr: 2\n              },\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: () => navigate('/'),\n              sx: {\n                borderRadius: 2\n              },\n              children: \"Kembali ke Beranda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: 'grey.600',\n                mb: 1\n              },\n              children: \"Belum ada berita yang disimpan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.500',\n                mb: 3\n              },\n              children: \"Mulai simpan berita favorit Anda dengan menekan ikon bookmark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              onClick: () => navigate('/'),\n              sx: {\n                borderRadius: 2\n              },\n              children: \"Jelajahi Berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this) : savedNews.map((news, index) => {\n          var _news$description;\n          return /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              marginBottom: 2,\n              boxShadow: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: {\n                  xs: 'column',\n                  sm: 'row'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                onClick: () => handleNewsClick(news.id),\n                sx: {\n                  width: {\n                    xs: '100%',\n                    sm: 200\n                  },\n                  height: {\n                    xs: 200,\n                    sm: 150\n                  },\n                  objectFit: 'cover',\n                  cursor: 'pointer'\n                },\n                image: getImageUrl(news.image),\n                alt: news.title,\n                onError: e => {\n                  e.target.src = 'https://source.unsplash.com/300x200/?news';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                onClick: () => handleNewsClick(news.id),\n                sx: {\n                  flex: 1,\n                  padding: 2,\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h3\",\n                  sx: {\n                    marginBottom: 1,\n                    fontWeight: 600\n                  },\n                  children: news.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    marginBottom: 1,\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: news.category,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: 'grey.600',\n                      alignSelf: 'center'\n                    },\n                    children: [formatDate(news.date), \" \\u2022 \", news.views || 0, \" views\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'grey.700',\n                    marginBottom: 2\n                  },\n                  children: [(_news$description = news.description) === null || _news$description === void 0 ? void 0 : _news$description.substring(0, 150), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: e => {\n                      e.stopPropagation(); // Prevent card click\n                      handleBookmark(news);\n                    },\n                    color: \"primary\",\n                    title: \"Hapus dari simpan\",\n                    children: /*#__PURE__*/_jsxDEV(BookmarkAddedIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: e => {\n                      e.stopPropagation(); // Prevent card click\n                      handleShare(news);\n                    },\n                    color: \"primary\",\n                    title: \"Bagikan\",\n                    children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, `saved-${news.id}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: 1200,\n          display: 'block',\n          backgroundColor: 'white',\n          borderTop: '1px solid #e0e0e0',\n          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-around',\n            alignItems: 'center',\n            height: 64,\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => window.location.href = '/',\n            className: `bottom-nav-item ${bottomValue === 0 ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `fas fa-home bottom-nav-icon ${bottomValue === 0 ? 'text-blue-600' : 'text-gray-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: bottomValue === 0 ? 'primary.main' : 'text.secondary'\n              },\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => setBottomValue(1),\n            className: `bottom-nav-item ${bottomValue === 1 ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `fas fa-search bottom-nav-icon ${bottomValue === 1 ? 'text-blue-600' : 'text-gray-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: bottomValue === 1 ? 'primary.main' : 'text.secondary'\n              },\n              children: \"Cari\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"bottom-nav-item active\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bookmark bottom-nav-icon text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 600\n              },\n              children: \"Simpan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n_s(Saved, \"AbqQN+Z/gau6yidANDf1oH5xjPA=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Saved;\nexport default Saved;\nvar _c;\n$RefreshReg$(_c, \"Saved\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "IconButton", "Chip", "CircularProgress", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "<PERSON><PERSON>", "Drawer", "Divider", "<PERSON><PERSON>", "BookmarkAddedIcon", "ShareIcon", "HomeIcon", "BookmarkIcon", "AdminPanelSettingsIcon", "MenuIcon", "CloseIcon", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getImageUrl", "imagePath", "startsWith", "filename", "replace", "includes", "split", "pop", "Saved", "_s", "navigate", "user", "isAuthenticated", "savedNews", "setSavedNews", "kostum", "setKostum", "logo", "title", "bottomValue", "setBottomValue", "sidebarOpen", "setSidebarOpen", "loading", "setLoading", "fetch", "then", "res", "json", "response", "success", "data", "console", "log", "logoPath", "logo_file_path", "nama_website", "description", "deskripsi_website", "document", "Error", "catch", "err", "id", "ok", "status", "text", "JSON", "parse", "e", "error", "Array", "isArray", "mappedData", "map", "item", "originalImage", "image", "processedImage", "content", "category", "category_name", "date", "created_at", "author", "author_name", "slug", "views", "likes", "share", "handleBookmark", "news", "window", "alert", "method", "headers", "body", "URLSearchParams", "action", "user_id", "result", "prev", "filter", "n", "message", "handleNewsClick", "newsId", "handleShare", "urlTitle", "toLowerCase", "trim", "link", "location", "origin", "navigator", "clipboard", "writeText", "formatDate", "dateString", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "sx", "minHeight", "bgcolor", "width", "overflow", "children", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "fontSize", "edge", "onClick", "anchor", "open", "onClose", "p", "right", "top", "mb", "spacing", "fullWidth", "textTransform", "startIcon", "mt", "pb", "background", "textAlign", "padding", "marginBottom", "borderRadius", "length", "paddingBottom", "justifyContent", "index", "_news$description", "boxShadow", "flexDirection", "xs", "sm", "component", "objectFit", "cursor", "flex", "gap", "flexWrap", "label", "size", "alignSelf", "substring", "stopPropagation", "left", "bottom", "backgroundColor", "borderTop", "href", "className", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/components/Saved.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport {\r\n  Box, Typography, Card, CardContent, CardMedia, IconButton, Chip, CircularProgress,\r\n  AppBar, Toolbar, Avatar, But<PERSON>, <PERSON>er, Divider, Stack\r\n} from '@mui/material';\r\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\r\nimport ShareIcon from '@mui/icons-material/Share';\r\nimport HomeIcon from '@mui/icons-material/Home';\r\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\r\nimport AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';\r\nimport MenuIcon from '@mui/icons-material/Menu';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../../../contexts/AuthContext';\r\n\r\n// Helper function to get correct image URL from database\r\nconst getImageUrl = (imagePath) => {\r\n  if (!imagePath) {\r\n    return 'https://source.unsplash.com/300x200/?news';\r\n  }\r\n\r\n  // If it's already a full URL, return as is\r\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\r\n    return imagePath;\r\n  }\r\n\r\n  // If it's already the correct path, use it directly\r\n  if (imagePath.startsWith('/react-news/frontend/uploads/')) {\r\n    return `http://localhost${imagePath}`;\r\n  }\r\n\r\n  // Extract filename from any path format stored in database\r\n  let filename = '';\r\n\r\n  if (imagePath.startsWith('/react-news/uploads/')) {\r\n    filename = imagePath.replace('/react-news/uploads/', '');\r\n  } else if (imagePath.startsWith('/uploads/')) {\r\n    filename = imagePath.replace('/uploads/', '');\r\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\r\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\r\n  } else if (imagePath.startsWith('admin/uploads/')) {\r\n    filename = imagePath.replace('admin/uploads/', '');\r\n  } else if (imagePath.startsWith('uploads/')) {\r\n    filename = imagePath.replace('uploads/', '');\r\n  } else if (imagePath.startsWith('assets/news/')) {\r\n    filename = imagePath.replace('assets/news/', '');\r\n  } else if (!imagePath.includes('/')) {\r\n    // Just filename from database\r\n    filename = imagePath;\r\n  } else {\r\n    // Extract filename from any other path\r\n    filename = imagePath.split('/').pop();\r\n  }\r\n\r\n  // Use consistent frontend/uploads path for all images\r\n  return `http://localhost/react-news/frontend/uploads/${filename}`;\r\n};\r\n\r\nconst Saved = () => {\r\n  const navigate = useNavigate();\r\n  const { user, isAuthenticated } = useAuth();\r\n  const [savedNews, setSavedNews] = useState([]);\r\n  const [kostum, setKostum] = useState({ logo: '', title: '' });\r\n  const [bottomValue, setBottomValue] = useState(2); // 2 = Simpan\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Load website settings from database (same as LandingPage)\r\n  useEffect(() => {\r\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')\r\n      .then(res => res.json())\r\n      .then(response => {\r\n        if (response.success && response.data) {\r\n          const data = response.data;\r\n          console.log('Website settings loaded:', data);\r\n\r\n          // Logo is now stored as base64 data in database\r\n          let logoPath = data.logo_file_path && data.logo_file_path.startsWith('data:')\r\n            ? data.logo_file_path\r\n            : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';\r\n\r\n          setKostum({\r\n            logo: logoPath,\r\n            title: data.nama_website || 'React News Portal',\r\n            description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'\r\n          });\r\n\r\n          // Update document title\r\n          document.title = `Berita Tersimpan - ${data.nama_website || 'React News Portal'}`;\r\n        } else {\r\n          throw new Error('Invalid API response');\r\n        }\r\n      })\r\n      .catch(err => {\r\n        console.log('Database settings not available, using defaults:', err);\r\n        setKostum({\r\n          logo: '/logo192.png',\r\n          title: 'React News Portal',\r\n          description: 'Portal berita terkini dan terpercaya'\r\n        });\r\n        document.title = 'Berita Tersimpan - React News Portal';\r\n      });\r\n  }, []);\r\n\r\n  // Load saved news from database\r\n  useEffect(() => {\r\n    // Check if user is authenticated, if not show empty state but don't redirect\r\n    if (!isAuthenticated || !user) {\r\n      setLoading(false);\r\n      setSavedNews([]);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news&user_id=${user.id}`)\r\n      .then(res => {\r\n        if (!res.ok) {\r\n          throw new Error(`HTTP error! status: ${res.status}`);\r\n        }\r\n        return res.text(); // Get as text first to debug\r\n      })\r\n      .then(text => {\r\n        console.log('Raw response:', text);\r\n        try {\r\n          return JSON.parse(text);\r\n        } catch (e) {\r\n          console.error('JSON parse error:', e);\r\n          console.error('Response text:', text);\r\n          throw new Error('Invalid JSON response');\r\n        }\r\n      })\r\n      .then(data => {\r\n        console.log('Saved news loaded:', data);\r\n        if (data.success && Array.isArray(data.data)) {\r\n          // Map the data to match expected format\r\n          const mappedData = data.data.map(item => {\r\n            console.log('📸 Image data from database:', {\r\n              id: item.id,\r\n              title: item.title,\r\n              originalImage: item.image,\r\n              processedImage: getImageUrl(item.image)\r\n            });\r\n\r\n            return {\r\n              id: item.id,\r\n              title: item.title,\r\n              description: item.description || item.content,\r\n              content: item.content,\r\n              image: item.image, // Raw image path from database posts table\r\n              category: item.category_name || 'Umum',\r\n              date: item.created_at || item.date,\r\n              author: item.author_name || 'Admin',\r\n              slug: item.slug,\r\n              views: item.views || 0,\r\n              likes: item.likes || 0,\r\n              share: item.share || 0\r\n            };\r\n          });\r\n          setSavedNews(mappedData);\r\n        } else {\r\n          setSavedNews([]);\r\n        }\r\n        setLoading(false);\r\n      })\r\n      .catch(err => {\r\n        console.error('Error loading saved news:', err);\r\n        setSavedNews([]);\r\n        setLoading(false);\r\n      });\r\n  }, [isAuthenticated, user, navigate]);\r\n\r\n  const handleBookmark = async (news) => {\r\n    if (!user) {\r\n      window.alert('Silakan login terlebih dahulu');\r\n      navigate('/auth/login');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/x-www-form-urlencoded',\r\n        },\r\n        body: new URLSearchParams({\r\n          action: 'remove_saved_news',\r\n          id: news.id,\r\n          user_id: user.id\r\n        })\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        setSavedNews(prev => prev.filter(n => n.id !== news.id));\r\n        window.alert('Berita dihapus dari bookmark');\r\n      } else {\r\n        window.alert('Gagal menghapus berita dari bookmark: ' + result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing bookmark:', error);\r\n      window.alert('Gagal menghapus berita dari bookmark');\r\n    }\r\n  };\r\n\r\n  const handleNewsClick = async (newsId) => {\r\n    // Increment views when clicking news card\r\n    try {\r\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/x-www-form-urlencoded',\r\n        },\r\n        body: new URLSearchParams({\r\n          action: 'increment_views',\r\n          id: newsId\r\n        })\r\n      });\r\n    } catch (error) {\r\n      console.log('Could not increment views:', error);\r\n    }\r\n\r\n    // Navigate to news detail\r\n    navigate(`/news/${newsId}`);\r\n  };\r\n\r\n  const handleShare = async (news) => {\r\n    try {\r\n      // Update share count in database\r\n      fetch(`http://localhost:5000/api/posts/${news.id}/share`, {\r\n        method: 'POST'\r\n      }).catch(err => console.error('Error updating share count:', err));\r\n\r\n      // Generate URL-friendly title for the link\r\n      const urlTitle = news.title\r\n        .toLowerCase()\r\n        .replace(/[^a-z0-9\\s-]/g, '')\r\n        .replace(/\\s+/g, '-')\r\n        .replace(/-+/g, '-')\r\n        .trim();\r\n\r\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\r\n\r\n      // Copy to clipboard\r\n      await navigator.clipboard.writeText(link);\r\n      window.alert('Link berita berhasil disalin ke clipboard!');\r\n\r\n    } catch (error) {\r\n      console.error('Failed to copy link:', error);\r\n      window.alert('Gagal menyalin link berita');\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('id-ID', {\r\n      day: 'numeric',\r\n      month: 'long',\r\n      year: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ minHeight: '100vh', bgcolor: 'blue.50', width: '100vw', overflow: 'hidden' }}>\r\n      {/* Navbar - Simple with sidebar icon only */}\r\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\r\n        <Toolbar sx={{ minHeight: 80, px: 6 }}>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n            <Avatar\r\n              src={kostum.logo}\r\n              alt=\"Logo\"\r\n              sx={{ width: 48, height: 48, mr: 2 }}\r\n              onError={(e) => { e.target.src = '/logo192.png'; }}\r\n            />\r\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>\r\n              {kostum.title}\r\n            </Typography>\r\n          </Box>\r\n          <IconButton\r\n            edge=\"end\"\r\n            color=\"primary\"\r\n            onClick={() => setSidebarOpen(true)}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            <MenuIcon fontSize=\"large\" />\r\n          </IconButton>\r\n        </Toolbar>\r\n      </AppBar>\r\n\r\n      {/* Sidebar Drawer */}\r\n      <Drawer\r\n        anchor=\"right\"\r\n        open={sidebarOpen}\r\n        onClose={() => setSidebarOpen(false)}\r\n        sx={{ zIndex: 1400 }}\r\n      >\r\n        <Box sx={{ width: 300, p: 3 }}>\r\n          <IconButton\r\n            onClick={() => setSidebarOpen(false)}\r\n            sx={{ position: 'absolute', right: 8, top: 8 }}\r\n            aria-label=\"Tutup\"\r\n          >\r\n            <CloseIcon />\r\n          </IconButton>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\r\n            <Avatar\r\n              src={kostum.logo}\r\n              alt=\"Logo\"\r\n              sx={{ width: 32, height: 32, mr: 1 }}\r\n              onError={(e) => { e.target.src = '/logo192.png'; }}\r\n            />\r\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>\r\n              {kostum.title}\r\n            </Typography>\r\n          </Box>\r\n          <Divider sx={{ mb: 2 }} />\r\n          <Stack spacing={2}>\r\n            <Button\r\n              variant=\"outlined\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<HomeIcon />}\r\n              onClick={() => {\r\n                navigate('/');\r\n                setSidebarOpen(false);\r\n              }}\r\n            >\r\n              Beranda\r\n            </Button>\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<BookmarkIcon />}\r\n            >\r\n              Berita Tersimpan\r\n            </Button>\r\n            <Button\r\n              variant=\"outlined\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<AdminPanelSettingsIcon />}\r\n              onClick={() => {\r\n                window.open('/dashboard', '_blank');\r\n                setSidebarOpen(false);\r\n              }}\r\n            >\r\n              Admin Dashboard\r\n            </Button>\r\n          </Stack>\r\n        </Box>\r\n      </Drawer>\r\n      {/* Content Area with proper spacing from navbar */}\r\n      <Box sx={{ mt: 12, px: 3, pb: 3 }}>\r\n        {/* Total Saved Alert/Table */}\r\n        <Box sx={{\r\n          width: '100%',\r\n          background: '#e1f5fe',\r\n          color: '#007bff',\r\n          textAlign: 'center',\r\n          fontWeight: 600,\r\n          fontSize: 16,\r\n          padding: '12px 0',\r\n          marginBottom: 3,\r\n          borderRadius: 2,\r\n          borderBottom: '1.5px solid #b3e5fc'\r\n        }}>\r\n          Total Berita Disimpan: {isAuthenticated && user ? savedNews.length : 0}\r\n        </Box>\r\n\r\n      <Box sx={{ padding: 2, paddingBottom: 10 }}>\r\n        {loading ? (\r\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>\r\n            <CircularProgress />\r\n          </Box>\r\n        ) : savedNews.length === 0 ? (\r\n          <Box sx={{ textAlign: 'center', padding: 5 }}>\r\n            <BookmarkIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />\r\n            {!isAuthenticated || !user ? (\r\n              <>\r\n                <Typography variant=\"h6\" sx={{ color: 'grey.600', mb: 1 }}>\r\n                  Silakan login untuk melihat berita tersimpan\r\n                </Typography>\r\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', mb: 3 }}>\r\n                  Login untuk menyimpan dan mengakses berita favorit Anda\r\n                </Typography>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  onClick={() => navigate('/auth/login')}\r\n                  sx={{ borderRadius: 2, mr: 2 }}\r\n                >\r\n                  Login\r\n                </Button>\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => navigate('/')}\r\n                  sx={{ borderRadius: 2 }}\r\n                >\r\n                  Kembali ke Beranda\r\n                </Button>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Typography variant=\"h6\" sx={{ color: 'grey.600', mb: 1 }}>\r\n                  Belum ada berita yang disimpan\r\n                </Typography>\r\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', mb: 3 }}>\r\n                  Mulai simpan berita favorit Anda dengan menekan ikon bookmark\r\n                </Typography>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  onClick={() => navigate('/')}\r\n                  sx={{ borderRadius: 2 }}\r\n                >\r\n                  Jelajahi Berita\r\n                </Button>\r\n              </>\r\n            )}\r\n          </Box>\r\n        ) : (\r\n          savedNews.map((news, index) => (\r\n            <Card key={`saved-${news.id}-${index}`} sx={{ marginBottom: 2, boxShadow: 2 }}>\r\n              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' } }}>\r\n                {/* Clickable Image Area */}\r\n                <CardMedia\r\n                  component=\"img\"\r\n                  onClick={() => handleNewsClick(news.id)}\r\n                  sx={{\r\n                    width: { xs: '100%', sm: 200 },\r\n                    height: { xs: 200, sm: 150 },\r\n                    objectFit: 'cover',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  image={getImageUrl(news.image)}\r\n                  alt={news.title}\r\n                  onError={(e) => { e.target.src = 'https://source.unsplash.com/300x200/?news'; }}\r\n                />\r\n                {/* Clickable Content Area */}\r\n                <CardContent\r\n                  onClick={() => handleNewsClick(news.id)}\r\n                  sx={{\r\n                    flex: 1,\r\n                    padding: 2,\r\n                    cursor: 'pointer'\r\n                  }}\r\n                >\r\n                  <Typography variant=\"h6\" component=\"h3\" sx={{ marginBottom: 1, fontWeight: 600 }}>\r\n                    {news.title}\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', gap: 1, marginBottom: 1, flexWrap: 'wrap' }}>\r\n                    <Chip\r\n                      label={news.category}\r\n                      size=\"small\"\r\n                      color=\"primary\"\r\n                      variant=\"outlined\"\r\n                    />\r\n                    <Typography variant=\"caption\" sx={{ color: 'grey.600', alignSelf: 'center' }}>\r\n                      {formatDate(news.date)} • {news.views || 0} views\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  <Typography variant=\"body2\" sx={{ color: 'grey.700', marginBottom: 2 }}>\r\n                    {news.description?.substring(0, 150)}...\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', gap: 1 }}>\r\n                    <IconButton\r\n                      onClick={(e) => {\r\n                        e.stopPropagation(); // Prevent card click\r\n                        handleBookmark(news);\r\n                      }}\r\n                      color=\"primary\"\r\n                      title=\"Hapus dari simpan\"\r\n                    >\r\n                      <BookmarkAddedIcon />\r\n                    </IconButton>\r\n                    <IconButton\r\n                      onClick={(e) => {\r\n                        e.stopPropagation(); // Prevent card click\r\n                        handleShare(news);\r\n                      }}\r\n                      color=\"primary\"\r\n                      title=\"Bagikan\"\r\n                    >\r\n                      <ShareIcon />\r\n                    </IconButton>\r\n                  </Box>\r\n                </CardContent>\r\n              </Box>\r\n            </Card>\r\n          ))\r\n        )}\r\n      </Box>\r\n      {/* Custom Bottom Navigation */}\r\n      <Box sx={{\r\n        position: 'fixed',\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        zIndex: 1200,\r\n        display: 'block',\r\n        backgroundColor: 'white',\r\n        borderTop: '1px solid #e0e0e0',\r\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\r\n      }}>\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-around',\r\n          alignItems: 'center',\r\n          height: 64,\r\n          px: 1\r\n        }}>\r\n          <Box\r\n            onClick={() => window.location.href = '/'}\r\n            className={`bottom-nav-item ${bottomValue === 0 ? 'active' : ''}`}\r\n          >\r\n            <i className={`fas fa-home bottom-nav-icon ${bottomValue === 0 ? 'text-blue-600' : 'text-gray-500'}`}></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomValue === 0 ? 'primary.main' : 'text.secondary' }}>\r\n              Home\r\n            </Typography>\r\n          </Box>\r\n\r\n          <Box\r\n            onClick={() => setBottomValue(1)}\r\n            className={`bottom-nav-item ${bottomValue === 1 ? 'active' : ''}`}\r\n          >\r\n            <i className={`fas fa-search bottom-nav-icon ${bottomValue === 1 ? 'text-blue-600' : 'text-gray-500'}`}></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomValue === 1 ? 'primary.main' : 'text.secondary' }}>\r\n              Cari\r\n            </Typography>\r\n          </Box>\r\n\r\n          <Box className=\"bottom-nav-item active\">\r\n            <i className=\"fas fa-bookmark bottom-nav-icon text-blue-600\"></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main', fontWeight: 600 }}>\r\n              Simpan\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Saved;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,gBAAgB,EACjFC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,QAClD,eAAe;AACtB,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,+BAA+B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;EACjC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,2CAA2C;EACpD;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACvE,OAAOD,SAAS;EAClB;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,+BAA+B,CAAC,EAAE;IACzD,OAAO,mBAAmBD,SAAS,EAAE;EACvC;;EAEA;EACA,IAAIE,QAAQ,GAAG,EAAE;EAEjB,IAAIF,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;IAChDC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;EAC1D,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;IAC5CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC/C,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,mCAAmC,CAAC,EAAE;IACpEC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;EACvE,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,gBAAgB,CAAC,EAAE;IACjDC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;EACpD,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC3CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC9C,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;IAC/CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAClD,CAAC,MAAM,IAAI,CAACH,SAAS,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACnC;IACAF,QAAQ,GAAGF,SAAS;EACtB,CAAC,MAAM;IACL;IACAE,QAAQ,GAAGF,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;EACvC;;EAEA;EACA,OAAO,gDAAgDJ,QAAQ,EAAE;AACnE,CAAC;AAED,MAAMK,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAgB,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC;IAAE8C,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC;EAC7D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAD,SAAS,CAAC,MAAM;IACduD,KAAK,CAAC,2FAA2F,CAAC,CAC/FC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,QAAQ,IAAI;MAChB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC,MAAMA,IAAI,GAAGF,QAAQ,CAACE,IAAI;QAC1BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,IAAI,CAAC;;QAE7C;QACA,IAAIG,QAAQ,GAAGH,IAAI,CAACI,cAAc,IAAIJ,IAAI,CAACI,cAAc,CAACjC,UAAU,CAAC,OAAO,CAAC,GACzE6B,IAAI,CAACI,cAAc,GACnB,oZAAoZ;QAExZnB,SAAS,CAAC;UACRC,IAAI,EAAEiB,QAAQ;UACdhB,KAAK,EAAEa,IAAI,CAACK,YAAY,IAAI,mBAAmB;UAC/CC,WAAW,EAAEN,IAAI,CAACO,iBAAiB,IAAI;QACzC,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACrB,KAAK,GAAG,sBAAsBa,IAAI,CAACK,YAAY,IAAI,mBAAmB,EAAE;MACnF,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,CAAC,CACDC,KAAK,CAACC,GAAG,IAAI;MACZV,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAES,GAAG,CAAC;MACpE1B,SAAS,CAAC;QACRC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,mBAAmB;QAC1BmB,WAAW,EAAE;MACf,CAAC,CAAC;MACFE,QAAQ,CAACrB,KAAK,GAAG,sCAAsC;IACzD,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhD,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC0C,eAAe,IAAI,CAACD,IAAI,EAAE;MAC7Ba,UAAU,CAAC,KAAK,CAAC;MACjBV,YAAY,CAAC,EAAE,CAAC;MAChB;IACF;IAEAU,UAAU,CAAC,IAAI,CAAC;IAChBC,KAAK,CAAC,qGAAqGd,IAAI,CAACgC,EAAE,EAAE,CAAC,CAClHjB,IAAI,CAACC,GAAG,IAAI;MACX,IAAI,CAACA,GAAG,CAACiB,EAAE,EAAE;QACX,MAAM,IAAIJ,KAAK,CAAC,uBAAuBb,GAAG,CAACkB,MAAM,EAAE,CAAC;MACtD;MACA,OAAOlB,GAAG,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CACDpB,IAAI,CAACoB,IAAI,IAAI;MACZd,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEa,IAAI,CAAC;MAClC,IAAI;QACF,OAAOC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVjB,OAAO,CAACkB,KAAK,CAAC,mBAAmB,EAAED,CAAC,CAAC;QACrCjB,OAAO,CAACkB,KAAK,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;QACrC,MAAM,IAAIN,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,CACDd,IAAI,CAACK,IAAI,IAAI;MACZC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;MACvC,IAAIA,IAAI,CAACD,OAAO,IAAIqB,KAAK,CAACC,OAAO,CAACrB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAMsB,UAAU,GAAGtB,IAAI,CAACA,IAAI,CAACuB,GAAG,CAACC,IAAI,IAAI;UACvCvB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;YAC1CU,EAAE,EAAEY,IAAI,CAACZ,EAAE;YACXzB,KAAK,EAAEqC,IAAI,CAACrC,KAAK;YACjBsC,aAAa,EAAED,IAAI,CAACE,KAAK;YACzBC,cAAc,EAAE1D,WAAW,CAACuD,IAAI,CAACE,KAAK;UACxC,CAAC,CAAC;UAEF,OAAO;YACLd,EAAE,EAAEY,IAAI,CAACZ,EAAE;YACXzB,KAAK,EAAEqC,IAAI,CAACrC,KAAK;YACjBmB,WAAW,EAAEkB,IAAI,CAAClB,WAAW,IAAIkB,IAAI,CAACI,OAAO;YAC7CA,OAAO,EAAEJ,IAAI,CAACI,OAAO;YACrBF,KAAK,EAAEF,IAAI,CAACE,KAAK;YAAE;YACnBG,QAAQ,EAAEL,IAAI,CAACM,aAAa,IAAI,MAAM;YACtCC,IAAI,EAAEP,IAAI,CAACQ,UAAU,IAAIR,IAAI,CAACO,IAAI;YAClCE,MAAM,EAAET,IAAI,CAACU,WAAW,IAAI,OAAO;YACnCC,IAAI,EAAEX,IAAI,CAACW,IAAI;YACfC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,CAAC;YACtBC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,CAAC;YACtBC,KAAK,EAAEd,IAAI,CAACc,KAAK,IAAI;UACvB,CAAC;QACH,CAAC,CAAC;QACFvD,YAAY,CAACuC,UAAU,CAAC;MAC1B,CAAC,MAAM;QACLvC,YAAY,CAAC,EAAE,CAAC;MAClB;MACAU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDiB,KAAK,CAACC,GAAG,IAAI;MACZV,OAAO,CAACkB,KAAK,CAAC,2BAA2B,EAAER,GAAG,CAAC;MAC/C5B,YAAY,CAAC,EAAE,CAAC;MAChBU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,CAACZ,eAAe,EAAED,IAAI,EAAED,QAAQ,CAAC,CAAC;EAErC,MAAM4D,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI,CAAC5D,IAAI,EAAE;MACT6D,MAAM,CAACC,KAAK,CAAC,+BAA+B,CAAC;MAC7C/D,QAAQ,CAAC,aAAa,CAAC;MACvB;IACF;IAEA,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMJ,KAAK,CAAC,qEAAqE,EAAE;QAClGiD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;UACxBC,MAAM,EAAE,mBAAmB;UAC3BnC,EAAE,EAAE4B,IAAI,CAAC5B,EAAE;UACXoC,OAAO,EAAEpE,IAAI,CAACgC;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMqC,MAAM,GAAG,MAAMnD,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpC,IAAIoD,MAAM,CAAClD,OAAO,EAAE;QAClBhB,YAAY,CAACmE,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAK4B,IAAI,CAAC5B,EAAE,CAAC,CAAC;QACxD6B,MAAM,CAACC,KAAK,CAAC,8BAA8B,CAAC;MAC9C,CAAC,MAAM;QACLD,MAAM,CAACC,KAAK,CAAC,wCAAwC,GAAGO,MAAM,CAACI,OAAO,CAAC;MACzE;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDsB,MAAM,CAACC,KAAK,CAAC,sCAAsC,CAAC;IACtD;EACF,CAAC;EAED,MAAMY,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC;IACA,IAAI;MACF,MAAM7D,KAAK,CAAC,qEAAqE,EAAE;QACjFiD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;UACxBC,MAAM,EAAE,iBAAiB;UACzBnC,EAAE,EAAE2C;QACN,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiB,KAAK,CAAC;IAClD;;IAEA;IACAxC,QAAQ,CAAC,SAAS4E,MAAM,EAAE,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOhB,IAAI,IAAK;IAClC,IAAI;MACF;MACA9C,KAAK,CAAC,mCAAmC8C,IAAI,CAAC5B,EAAE,QAAQ,EAAE;QACxD+B,MAAM,EAAE;MACV,CAAC,CAAC,CAACjC,KAAK,CAACC,GAAG,IAAIV,OAAO,CAACkB,KAAK,CAAC,6BAA6B,EAAER,GAAG,CAAC,CAAC;;MAElE;MACA,MAAM8C,QAAQ,GAAGjB,IAAI,CAACrD,KAAK,CACxBuE,WAAW,CAAC,CAAC,CACbrF,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBsF,IAAI,CAAC,CAAC;MAET,MAAMC,IAAI,GAAG,GAAGnB,MAAM,CAACoB,QAAQ,CAACC,MAAM,YAAYtB,IAAI,CAAC5B,EAAE,UAAU6C,QAAQ,aAAajB,IAAI,CAACX,QAAQ,EAAE;;MAEvG;MACA,MAAMkC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,IAAI,CAAC;MACzCnB,MAAM,CAACC,KAAK,CAAC,4CAA4C,CAAC;IAE5D,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CsB,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAC;IAC5C;EACF,CAAC;EAED,MAAMwB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMpC,IAAI,GAAG,IAAIqC,IAAI,CAACD,UAAU,CAAC;IACjC,OAAOpC,IAAI,CAACsC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5G,OAAA,CAACzB,GAAG;IAACsI,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEtFlH,OAAA,CAACjB,MAAM;MAACoI,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACR,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEO,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAN,QAAA,eACrIlH,OAAA,CAAChB,OAAO;QAAC6H,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACpClH,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9DlH,OAAA,CAACf,MAAM;YACL4I,GAAG,EAAE3G,MAAM,CAACE,IAAK;YACjB0G,GAAG,EAAC,MAAM;YACVjB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEe,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAG7E,CAAC,IAAK;cAAEA,CAAC,CAAC8E,MAAM,CAACL,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFtI,OAAA,CAACxB,UAAU;YAAC+J,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEpB,KAAK,EAAE,cAAc;cAAEqB,QAAQ,EAAE;YAAG,CAAE;YAAAvB,QAAA,EACnFhG,MAAM,CAACG;UAAK;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtI,OAAA,CAACpB,UAAU;UACT8J,IAAI,EAAC,KAAK;UACVtB,KAAK,EAAC,SAAS;UACfuB,OAAO,EAAEA,CAAA,KAAMlH,cAAc,CAAC,IAAI,CAAE;UACpCoF,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eAEdlH,OAAA,CAACL,QAAQ;YAAC8I,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGTtI,OAAA,CAACb,MAAM;MACLyJ,MAAM,EAAC,OAAO;MACdC,IAAI,EAAErH,WAAY;MAClBsH,OAAO,EAAEA,CAAA,KAAMrH,cAAc,CAAC,KAAK,CAAE;MACrCoF,EAAE,EAAE;QAAEW,MAAM,EAAE;MAAK,CAAE;MAAAN,QAAA,eAErBlH,OAAA,CAACzB,GAAG;QAACsI,EAAE,EAAE;UAAEG,KAAK,EAAE,GAAG;UAAE+B,CAAC,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBAC5BlH,OAAA,CAACpB,UAAU;UACT+J,OAAO,EAAEA,CAAA,KAAMlH,cAAc,CAAC,KAAK,CAAE;UACrCoF,EAAE,EAAE;YAAEM,QAAQ,EAAE,UAAU;YAAE6B,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAE;UAC/C,cAAW,OAAO;UAAA/B,QAAA,eAElBlH,OAAA,CAACJ,SAAS;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbtI,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEuB,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBACxDlH,OAAA,CAACf,MAAM;YACL4I,GAAG,EAAE3G,MAAM,CAACE,IAAK;YACjB0G,GAAG,EAAC,MAAM;YACVjB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEe,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAG7E,CAAC,IAAK;cAAEA,CAAC,CAAC8E,MAAM,CAACL,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFtI,OAAA,CAACxB,UAAU;YAAC+J,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEpB,KAAK,EAAE,cAAc;cAAEqB,QAAQ,EAAE;YAAG,CAAE;YAAAvB,QAAA,EACnFhG,MAAM,CAACG;UAAK;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtI,OAAA,CAACZ,OAAO;UAACyH,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BtI,OAAA,CAACX,KAAK;UAAC8J,OAAO,EAAE,CAAE;UAAAjC,QAAA,gBAChBlH,OAAA,CAACd,MAAM;YACLqJ,OAAO,EAAC,UAAU;YAClBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAEtJ,OAAA,CAACR,QAAQ;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBK,OAAO,EAAEA,CAAA,KAAM;cACb9H,QAAQ,CAAC,GAAG,CAAC;cACbY,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YAAAyF,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtI,OAAA,CAACd,MAAM;YACLqJ,OAAO,EAAC,WAAW;YACnBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAEtJ,OAAA,CAACP,YAAY;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAApB,QAAA,EAC7B;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtI,OAAA,CAACd,MAAM;YACLqJ,OAAO,EAAC,UAAU;YAClBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAEtJ,OAAA,CAACN,sBAAsB;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtCK,OAAO,EAAEA,CAAA,KAAM;cACbhE,MAAM,CAACkE,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC;cACnCpH,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YAAAyF,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETtI,OAAA,CAACzB,GAAG;MAACsI,EAAE,EAAE;QAAE0C,EAAE,EAAE,EAAE;QAAE9B,EAAE,EAAE,CAAC;QAAE+B,EAAE,EAAE;MAAE,CAAE;MAAAtC,QAAA,gBAEhClH,OAAA,CAACzB,GAAG;QAACsI,EAAE,EAAE;UACPG,KAAK,EAAE,MAAM;UACbyC,UAAU,EAAE,SAAS;UACrBrC,KAAK,EAAE,SAAS;UAChBsC,SAAS,EAAE,QAAQ;UACnBlB,UAAU,EAAE,GAAG;UACfC,QAAQ,EAAE,EAAE;UACZkB,OAAO,EAAE,QAAQ;UACjBC,YAAY,EAAE,CAAC;UACfC,YAAY,EAAE,CAAC;UACfvC,YAAY,EAAE;QAChB,CAAE;QAAAJ,QAAA,GAAC,yBACsB,EAACnG,eAAe,IAAID,IAAI,GAAGE,SAAS,CAAC8I,MAAM,GAAG,CAAC;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAERtI,OAAA,CAACzB,GAAG;QAACsI,EAAE,EAAE;UAAE8C,OAAO,EAAE,CAAC;UAAEI,aAAa,EAAE;QAAG,CAAE;QAAA7C,QAAA,EACxCxF,OAAO,gBACN1B,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEsC,cAAc,EAAE,QAAQ;YAAErC,UAAU,EAAE,QAAQ;YAAEb,SAAS,EAAE;UAAI,CAAE;UAAAI,QAAA,eAC3FlH,OAAA,CAAClB,gBAAgB;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJtH,SAAS,CAAC8I,MAAM,KAAK,CAAC,gBACxB9J,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE;YAAE6C,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBAC3ClH,OAAA,CAACP,YAAY;YAACoH,EAAE,EAAE;cAAE4B,QAAQ,EAAE,EAAE;cAAErB,KAAK,EAAE,UAAU;cAAE8B,EAAE,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC/D,CAACvH,eAAe,IAAI,CAACD,IAAI,gBACxBd,OAAA,CAAAE,SAAA;YAAAgH,QAAA,gBACElH,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAEO,KAAK,EAAE,UAAU;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAE3D;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtI,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEO,KAAK,EAAE,UAAU;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAE9D;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtI,OAAA,CAACd,MAAM;cACLqJ,OAAO,EAAC,WAAW;cACnBI,OAAO,EAAEA,CAAA,KAAM9H,QAAQ,CAAC,aAAa,CAAE;cACvCgG,EAAE,EAAE;gBAAEgD,YAAY,EAAE,CAAC;gBAAE7B,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,EAChC;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtI,OAAA,CAACd,MAAM;cACLqJ,OAAO,EAAC,UAAU;cAClBI,OAAO,EAAEA,CAAA,KAAM9H,QAAQ,CAAC,GAAG,CAAE;cAC7BgG,EAAE,EAAE;gBAAEgD,YAAY,EAAE;cAAE,CAAE;cAAA3C,QAAA,EACzB;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHtI,OAAA,CAAAE,SAAA;YAAAgH,QAAA,gBACElH,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAEO,KAAK,EAAE,UAAU;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAE3D;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtI,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEO,KAAK,EAAE,UAAU;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EAAC;YAE9D;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtI,OAAA,CAACd,MAAM;cACLqJ,OAAO,EAAC,WAAW;cACnBI,OAAO,EAAEA,CAAA,KAAM9H,QAAQ,CAAC,GAAG,CAAE;cAC7BgG,EAAE,EAAE;gBAAEgD,YAAY,EAAE;cAAE,CAAE;cAAA3C,QAAA,EACzB;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,GAENtH,SAAS,CAACyC,GAAG,CAAC,CAACiB,IAAI,EAAEuF,KAAK;UAAA,IAAAC,iBAAA;UAAA,oBACxBlK,OAAA,CAACvB,IAAI;YAAmCoI,EAAE,EAAE;cAAE+C,YAAY,EAAE,CAAC;cAAEO,SAAS,EAAE;YAAE,CAAE;YAAAjD,QAAA,eAC5ElH,OAAA,CAACzB,GAAG;cAACsI,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAE0C,aAAa,EAAE;kBAAEC,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAM;cAAE,CAAE;cAAApD,QAAA,gBAEvElH,OAAA,CAACrB,SAAS;gBACR4L,SAAS,EAAC,KAAK;gBACf5B,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACd,IAAI,CAAC5B,EAAE,CAAE;gBACxC+D,EAAE,EAAE;kBACFG,KAAK,EAAE;oBAAEqD,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE;kBAAI,CAAC;kBAC9BvC,MAAM,EAAE;oBAAEsC,EAAE,EAAE,GAAG;oBAAEC,EAAE,EAAE;kBAAI,CAAC;kBAC5BE,SAAS,EAAE,OAAO;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACF7G,KAAK,EAAEzD,WAAW,CAACuE,IAAI,CAACd,KAAK,CAAE;gBAC/BkE,GAAG,EAAEpD,IAAI,CAACrD,KAAM;gBAChB4G,OAAO,EAAG7E,CAAC,IAAK;kBAAEA,CAAC,CAAC8E,MAAM,CAACL,GAAG,GAAG,2CAA2C;gBAAE;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eAEFtI,OAAA,CAACtB,WAAW;gBACViK,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACd,IAAI,CAAC5B,EAAE,CAAE;gBACxC+D,EAAE,EAAE;kBACF6D,IAAI,EAAE,CAAC;kBACPf,OAAO,EAAE,CAAC;kBACVc,MAAM,EAAE;gBACV,CAAE;gBAAAvD,QAAA,gBAEFlH,OAAA,CAACxB,UAAU;kBAAC+J,OAAO,EAAC,IAAI;kBAACgC,SAAS,EAAC,IAAI;kBAAC1D,EAAE,EAAE;oBAAE+C,YAAY,EAAE,CAAC;oBAAEpB,UAAU,EAAE;kBAAI,CAAE;kBAAAtB,QAAA,EAC9ExC,IAAI,CAACrD;gBAAK;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEbtI,OAAA,CAACzB,GAAG;kBAACsI,EAAE,EAAE;oBAAEa,OAAO,EAAE,MAAM;oBAAEiD,GAAG,EAAE,CAAC;oBAAEf,YAAY,EAAE,CAAC;oBAAEgB,QAAQ,EAAE;kBAAO,CAAE;kBAAA1D,QAAA,gBACtElH,OAAA,CAACnB,IAAI;oBACHgM,KAAK,EAAEnG,IAAI,CAACX,QAAS;oBACrB+G,IAAI,EAAC,OAAO;oBACZ1D,KAAK,EAAC,SAAS;oBACfmB,OAAO,EAAC;kBAAU;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFtI,OAAA,CAACxB,UAAU;oBAAC+J,OAAO,EAAC,SAAS;oBAAC1B,EAAE,EAAE;sBAAEO,KAAK,EAAE,UAAU;sBAAE2D,SAAS,EAAE;oBAAS,CAAE;oBAAA7D,QAAA,GAC1Ed,UAAU,CAAC1B,IAAI,CAACT,IAAI,CAAC,EAAC,UAAG,EAACS,IAAI,CAACJ,KAAK,IAAI,CAAC,EAAC,QAC7C;kBAAA;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENtI,OAAA,CAACxB,UAAU;kBAAC+J,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAEO,KAAK,EAAE,UAAU;oBAAEwC,YAAY,EAAE;kBAAE,CAAE;kBAAA1C,QAAA,IAAAgD,iBAAA,GACpExF,IAAI,CAAClC,WAAW,cAAA0H,iBAAA,uBAAhBA,iBAAA,CAAkBc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACvC;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEbtI,OAAA,CAACzB,GAAG;kBAACsI,EAAE,EAAE;oBAAEa,OAAO,EAAE,MAAM;oBAAEiD,GAAG,EAAE;kBAAE,CAAE;kBAAAzD,QAAA,gBACnClH,OAAA,CAACpB,UAAU;oBACT+J,OAAO,EAAGvF,CAAC,IAAK;sBACdA,CAAC,CAAC6H,eAAe,CAAC,CAAC,CAAC,CAAC;sBACrBxG,cAAc,CAACC,IAAI,CAAC;oBACtB,CAAE;oBACF0C,KAAK,EAAC,SAAS;oBACf/F,KAAK,EAAC,mBAAmB;oBAAA6F,QAAA,eAEzBlH,OAAA,CAACV,iBAAiB;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACbtI,OAAA,CAACpB,UAAU;oBACT+J,OAAO,EAAGvF,CAAC,IAAK;sBACdA,CAAC,CAAC6H,eAAe,CAAC,CAAC,CAAC,CAAC;sBACrBvF,WAAW,CAAChB,IAAI,CAAC;oBACnB,CAAE;oBACF0C,KAAK,EAAC,SAAS;oBACf/F,KAAK,EAAC,SAAS;oBAAA6F,QAAA,eAEflH,OAAA,CAACT,SAAS;sBAAA4I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC,GApEG,SAAS5D,IAAI,CAAC5B,EAAE,IAAImH,KAAK,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqEhC,CAAC;QAAA,CACR;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtI,OAAA,CAACzB,GAAG;QAACsI,EAAE,EAAE;UACPM,QAAQ,EAAE,OAAO;UACjB+D,IAAI,EAAE,CAAC;UACPlC,KAAK,EAAE,CAAC;UACRmC,MAAM,EAAE,CAAC;UACT3D,MAAM,EAAE,IAAI;UACZE,OAAO,EAAE,OAAO;UAChB0D,eAAe,EAAE,OAAO;UACxBC,SAAS,EAAE,mBAAmB;UAC9BlB,SAAS,EAAE;QACb,CAAE;QAAAjD,QAAA,eACAlH,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE;YACPa,OAAO,EAAE,MAAM;YACfsC,cAAc,EAAE,cAAc;YAC9BrC,UAAU,EAAE,QAAQ;YACpBI,MAAM,EAAE,EAAE;YACVN,EAAE,EAAE;UACN,CAAE;UAAAP,QAAA,gBACAlH,OAAA,CAACzB,GAAG;YACFoK,OAAO,EAAEA,CAAA,KAAMhE,MAAM,CAACoB,QAAQ,CAACuF,IAAI,GAAG,GAAI;YAC1CC,SAAS,EAAE,mBAAmBjK,WAAW,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA4F,QAAA,gBAElElH,OAAA;cAAGuL,SAAS,EAAE,+BAA+BjK,WAAW,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;YAAG;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1GtI,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE9F,WAAW,KAAK,CAAC,GAAG,cAAc,GAAG;cAAiB,CAAE;cAAA4F,QAAA,EAAC;YAEjI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENtI,OAAA,CAACzB,GAAG;YACFoK,OAAO,EAAEA,CAAA,KAAMpH,cAAc,CAAC,CAAC,CAAE;YACjCgK,SAAS,EAAE,mBAAmBjK,WAAW,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA4F,QAAA,gBAElElH,OAAA;cAAGuL,SAAS,EAAE,iCAAiCjK,WAAW,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;YAAG;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GtI,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE9F,WAAW,KAAK,CAAC,GAAG,cAAc,GAAG;cAAiB,CAAE;cAAA4F,QAAA,EAAC;YAEjI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENtI,OAAA,CAACzB,GAAG;YAACgN,SAAS,EAAC,wBAAwB;YAAArE,QAAA,gBACrClH,OAAA;cAAGuL,SAAS,EAAC;YAA+C;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEtI,OAAA,CAACxB,UAAU;cAAC+J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE,cAAc;gBAAEoB,UAAU,EAAE;cAAI,CAAE;cAAAtB,QAAA,EAAC;YAE3G;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1H,EAAA,CA3eID,KAAK;EAAA,QACQd,WAAW,EACMC,OAAO;AAAA;AAAA0L,EAAA,GAFrC7K,KAAK;AA6eX,eAAeA,KAAK;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}