<?php
echo "<h2>Test Upload Path</h2>";

// Test current directory
echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";

// Test upload directory path
$uploadsDir = dirname(__DIR__) . '/uploads';
echo "<p><strong>Upload directory path:</strong> " . $uploadsDir . "</p>";
echo "<p><strong>Upload directory exists:</strong> " . (is_dir($uploadsDir) ? 'YES' : 'NO') . "</p>";

// Create directory if not exists
if (!is_dir($uploadsDir)) {
    if (mkdir($uploadsDir, 0755, true)) {
        echo "<p><strong>Directory created successfully!</strong></p>";
    } else {
        echo "<p><strong>Failed to create directory!</strong></p>";
    }
}

// List files in upload directory
if (is_dir($uploadsDir)) {
    $files = scandir($uploadsDir);
    echo "<p><strong>Files in upload directory:</strong></p>";
    echo "<ul>";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "<li>$file</li>";
        }
    }
    echo "</ul>";
}

// Test web path
$webPath = '/react-news/frontend/uploads/';
echo "<p><strong>Web path:</strong> " . $webPath . "</p>";

// Test full URL
$fullUrl = 'http://localhost' . $webPath;
echo "<p><strong>Full URL:</strong> " . $fullUrl . "</p>";

// Test if we can access existing logo files
$logoFiles = glob($uploadsDir . '/logo_*.webp');
if (!empty($logoFiles)) {
    echo "<p><strong>Testing logo file access:</strong></p>";
    foreach ($logoFiles as $logoFile) {
        $filename = basename($logoFile);
        $testUrl = $fullUrl . $filename;
        echo "<p>File: $filename</p>";
        echo "<p>URL: <a href='$testUrl' target='_blank'>$testUrl</a></p>";
        echo "<img src='$testUrl' style='max-width: 100px; max-height: 100px;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
        echo "<span style='display:none; color:red;'>❌ Image failed to load</span><br><br>";
    }
}
?>
