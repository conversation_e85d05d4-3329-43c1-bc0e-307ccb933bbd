import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  createTheme,
  ThemeProvider,
  Button,
  Container
} from '@mui/material';
import VideoFeed from '../../components/VideoFeed';

// Create custom theme function (same as LandingPage)
const createCustomTheme = (kostum) => {
  return createTheme({
    palette: {
      primary: {
        main: kostum.primary_color || '#1976d2',
      },
      secondary: {
        main: kostum.secondary_color || '#dc004e',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    },
  });
};

// Mobile Video Layout Component
function MobileVideoLayout({ kostum, bottomNav, setBottomNav, handleBottomNavChange, navigate, user, isAuthenticated, handleLogout }) {
  return (
    <Box sx={{ 
      minHeight: '100vh', 
      bgcolor: '#000', // Black background like TikTok
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Video Feed Component */}
      <VideoFeed />

      {/* Bottom Navigation */}
      <Box sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(10px)',
        borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        zIndex: 1000
      }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          height: 64,
          px: 1
        }}>
          <Box
            onClick={() => handleBottomNavChange(0)}
            className={`bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
              py: 1,
              px: 2,
              borderRadius: 1,
              transition: 'all 0.3s ease'
            }}
          >
            <i className="fas fa-home bottom-nav-icon" style={{ 
              color: bottomNav === 0 ? kostum.primary_color : '#fff',
              fontSize: '20px',
              marginBottom: '4px'
            }}></i>
            <Typography variant="caption" sx={{ 
              color: bottomNav === 0 ? kostum.primary_color : '#fff',
              fontSize: '10px'
            }}>
              Home
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(1)}
            className={`bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
              py: 1,
              px: 2,
              borderRadius: 1,
              transition: 'all 0.3s ease'
            }}
          >
            <i className="fas fa-play-circle bottom-nav-icon" style={{ 
              color: bottomNav === 1 ? kostum.primary_color : '#fff',
              fontSize: '20px',
              marginBottom: '4px'
            }}></i>
            <Typography variant="caption" sx={{ 
              color: bottomNav === 1 ? kostum.primary_color : '#fff',
              fontSize: '10px'
            }}>
              Video
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(2)}
            className={`bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
              py: 1,
              px: 2,
              borderRadius: 1,
              transition: 'all 0.3s ease'
            }}
          >
            <i className="fas fa-search bottom-nav-icon" style={{ 
              color: bottomNav === 2 ? kostum.primary_color : '#fff',
              fontSize: '20px',
              marginBottom: '4px'
            }}></i>
            <Typography variant="caption" sx={{ 
              color: bottomNav === 2 ? kostum.primary_color : '#fff',
              fontSize: '10px'
            }}>
              Cari
            </Typography>
          </Box>

          <Box
            onClick={() => handleBottomNavChange(3)}
            className={`bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
              py: 1,
              px: 2,
              borderRadius: 1,
              transition: 'all 0.3s ease'
            }}
          >
            <i className="fas fa-bookmark bottom-nav-icon" style={{ 
              color: bottomNav === 3 ? kostum.primary_color : '#fff',
              fontSize: '20px',
              marginBottom: '4px'
            }}></i>
            <Typography variant="caption" sx={{ 
              color: bottomNav === 3 ? kostum.primary_color : '#fff',
              fontSize: '10px'
            }}>
              Simpan
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

// Desktop Video Layout Component
function DesktopVideoLayout({ kostum, navigate, user, isAuthenticated, handleLogout }) {
  return (
    <Box sx={{ 
      minHeight: '100vh', 
      bgcolor: '#000',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Top Navigation Bar */}
      <Box sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bgcolor: 'rgba(0, 0, 0, 0.9)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        zIndex: 1000,
        height: 64
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '100%',
          px: 3
        }}>
          {/* Logo */}
          <Box 
            onClick={() => navigate('/')}
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              cursor: 'pointer',
              '&:hover': { opacity: 0.8 }
            }}
          >
            <Typography variant="h6" sx={{ color: '#fff', fontWeight: 'bold' }}>
              Video Portal
            </Typography>
          </Box>

          {/* Navigation Menu */}
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Typography 
              onClick={() => navigate('/')}
              sx={{ 
                color: '#fff', 
                cursor: 'pointer',
                '&:hover': { color: kostum.primary_color }
              }}
            >
              Home
            </Typography>
            <Typography 
              sx={{ 
                color: kostum.primary_color, 
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              Video
            </Typography>
          </Box>

          {/* User Menu */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isAuthenticated ? (
              <>
                <Typography sx={{ color: '#fff' }}>
                  Hi, {user?.name || 'User'}
                </Typography>
                <Typography 
                  onClick={handleLogout}
                  sx={{ 
                    color: '#fff', 
                    cursor: 'pointer',
                    '&:hover': { color: kostum.secondary_color }
                  }}
                >
                  Logout
                </Typography>
              </>
            ) : (
              <Typography 
                onClick={() => navigate('/auth/login')}
                sx={{ 
                  color: '#fff', 
                  cursor: 'pointer',
                  '&:hover': { color: kostum.primary_color }
                }}
              >
                Login
              </Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Video Feed with top margin */}
      <Box sx={{ mt: 8 }}>
        <VideoFeed />
      </Box>
    </Box>
  );
}

export default function VideoPage() {
  const navigate = useNavigate();
  const { user, logout, isAuthenticated } = useAuth();
  const [bottomNav, setBottomNav] = useState(1); // Set to Video tab
  const [kostum, setKostum] = useState({
    logo: '',
    title: '',
    primary_color: '#1976d2',
    secondary_color: '#dc004e',
    description: ''
  });

  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));

  // Load settings from database (same as LandingPage)
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings');
        const data = await response.json();

        if (data.success) {
          let logoPath = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzE5NzZkMiIvPgo8cGF0aCBkPSJNMTIgMTJIMjhWMjhIMTJWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K';

          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {
            const filename = data.website_logo.split('/').pop();
            logoPath = `http://localhost/react-news/uploads/${filename}`;
          }

          setKostum(prev => ({
            ...prev,
            logo: logoPath,
            title: data.website_name || 'Video Portal',
            primary_color: data.primary_color || '#1976d2',
            secondary_color: data.secondary_color || '#dc004e',
            description: data.website_description || 'Portal video terkini'
          }));

          document.title = (data.website_name || 'Video Portal') + ' - Video';
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };

    loadSettings();
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Handler untuk bottom nav (same logic as LandingPage)
  const handleBottomNavChange = (newValue) => {
    if (newValue === 0) {
      navigate('/'); // Go to home
    } else if (newValue === 1) {
      setBottomNav(1);
      // Already on video page
    } else if (newValue === 2) {
      // Search functionality
      console.log('Search clicked from video page');
    } else if (newValue === 3) {
      // Saved videos
      if (isAuthenticated) {
        console.log('Navigate to saved videos');
      } else {
        navigate('/auth/login');
      }
    }
  };

  const customTheme = createCustomTheme(kostum);

  return (
    <ThemeProvider theme={customTheme}>
      <Container maxWidth="lg" sx={{ py: 2 }}>
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => navigate('/video-play')}
            sx={{
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
              borderRadius: 3,
              boxShadow: 3,
              '&:hover': {
                boxShadow: 6,
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease'
            }}
          >
            🎬 Full Screen Video Player
          </Button>
          <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
            Experience videos in full screen TikTok-style player
          </Typography>
        </Box>
      </Container>

      {isDesktop ? (
        <DesktopVideoLayout
          kostum={kostum}
          navigate={navigate}
          user={user}
          isAuthenticated={isAuthenticated}
          handleLogout={handleLogout}
        />
      ) : (
        <MobileVideoLayout
          kostum={kostum}
          bottomNav={bottomNav}
          setBottomNav={setBottomNav}
          handleBottomNavChange={handleBottomNavChange}
          navigate={navigate}
          user={user}
          isAuthenticated={isAuthenticated}
          handleLogout={handleLogout}
        />
      )}
    </ThemeProvider>
  );
}
