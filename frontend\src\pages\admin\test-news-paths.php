<?php
echo "<h2>Test News Image Paths</h2>";

// Test upload directory path
$uploadsDir = dirname(__DIR__) . '/uploads';
echo "<p><strong>Upload directory:</strong> " . $uploadsDir . "</p>";
echo "<p><strong>Directory exists:</strong> " . (is_dir($uploadsDir) ? 'YES' : 'NO') . "</p>";

// Create directory if not exists
if (!is_dir($uploadsDir)) {
    if (mkdir($uploadsDir, 0755, true)) {
        echo "<p><strong>Directory created successfully!</strong></p>";
    } else {
        echo "<p><strong>Failed to create directory!</strong></p>";
    }
}

// List news images in upload directory
if (is_dir($uploadsDir)) {
    $newsFiles = glob($uploadsDir . '/news_*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
    echo "<p><strong>News images found:</strong> " . count($newsFiles) . "</p>";
    
    if (!empty($newsFiles)) {
        echo "<h3>Testing Image Access:</h3>";
        foreach (array_slice($newsFiles, 0, 5) as $file) { // Test first 5 files
            $filename = basename($file);
            $dbPath = '/react-news/frontend/uploads/' . $filename;
            $webUrl = 'http://localhost' . $dbPath;
            
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<p><strong>File:</strong> $filename</p>";
            echo "<p><strong>Database path:</strong> $dbPath</p>";
            echo "<p><strong>Web URL:</strong> <a href='$webUrl' target='_blank'>$webUrl</a></p>";
            echo "<p><strong>File exists:</strong> " . (file_exists($file) ? 'YES' : 'NO') . "</p>";
            echo "<img src='$webUrl' style='max-width: 100px; max-height: 100px; border: 1px solid #ddd;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
            echo "<span style='display:none; color:red;'>❌ Image failed to load</span>";
            echo "</div>";
        }
    }
}

// Test database connection and sample news data
try {
    $pdo = new PDO("mysql:host=localhost;dbname=react_news;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>Database News Images:</h3>";
    $stmt = $pdo->prepare("SELECT id, title, image FROM posts WHERE image IS NOT NULL AND image != '' LIMIT 5");
    $stmt->execute();
    $newsItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($newsItems)) {
        foreach ($newsItems as $news) {
            $webUrl = 'http://localhost' . $news['image'];
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<p><strong>ID:</strong> {$news['id']}</p>";
            echo "<p><strong>Title:</strong> {$news['title']}</p>";
            echo "<p><strong>DB Image Path:</strong> {$news['image']}</p>";
            echo "<p><strong>Web URL:</strong> <a href='$webUrl' target='_blank'>$webUrl</a></p>";
            echo "<img src='$webUrl' style='max-width: 100px; max-height: 100px; border: 1px solid #ddd;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
            echo "<span style='display:none; color:red;'>❌ Image failed to load</span>";
            echo "</div>";
        }
    } else {
        echo "<p>No news with images found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>Database error:</strong> " . $e->getMessage() . "</p>";
}

echo "<h3>Path Summary:</h3>";
echo "<ul>";
echo "<li><strong>Physical storage:</strong> /react-news/frontend/src/pages/admin/uploads/</li>";
echo "<li><strong>Database path:</strong> /react-news/frontend/uploads/filename.ext</li>";
echo "<li><strong>Web access URL:</strong> http://localhost/react-news/frontend/uploads/filename.ext</li>";
echo "</ul>";
?>
