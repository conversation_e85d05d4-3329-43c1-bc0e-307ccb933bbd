<!-- Pengaturan Website -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-800 flex items-center">
            <i class="fas fa-cog mr-3 text-blue-600"></i>
            Pengaturan Website
        </h2>
        <p class="text-gray-600 mt-1">Kelola pengaturan dasar website Anda</p>
    </div>

    <div class="p-6">
        <form id="pengaturanForm" class="space-y-6">
            <!-- Informasi Website -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                    Informasi Website
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- <PERSON><PERSON> Website -->
                    <div>
                        <label for="nama_website" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Website
                        </label>
                        <input type="text" id="nama_website" name="nama_website" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Masukkan nama website">
                    </div>

                    <!-- Deskripsi Website -->
                    <div>
                        <label for="deskripsi_website" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Website
                        </label>
                        <textarea id="deskripsi_website" name="deskripsi_website" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="Masukkan deskripsi website"></textarea>
                    </div>
                </div>
            </div>

            <!-- Logo Website -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-image mr-2 text-blue-600"></i>
                    Logo Website
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Current Logo -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Logo Saat Ini</label>
                        <div id="current-logo" class="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white">
                            <div class="flex flex-col items-center justify-center h-full">
                                <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500 text-sm text-center">Belum ada logo</p>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Logo -->
                    <div>
                        <label for="logo_upload" class="block text-sm font-medium text-gray-700 mb-2">Upload Logo Baru</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                            <input type="file" id="logo_upload" name="logo" accept="image/*" class="hidden">
                            <div id="upload-area" class="cursor-pointer" onclick="document.getElementById('logo_upload').click()">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600">Klik untuk upload logo</p>
                                <p class="text-xs text-gray-500 mt-1">Semua format gambar didukung (Max: 5MB)</p>
                            </div>
                            <div id="preview-area" class="hidden">
                                <img id="preview-image" class="max-w-full h-32 mx-auto rounded">
                                <p id="preview-name" class="text-sm text-gray-600 mt-2"></p>
                                <button type="button" onclick="clearPreview()" class="text-red-500 text-sm mt-1 hover:underline">Hapus</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warna Admin Sidebar -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-palette mr-2 text-blue-600"></i>
                    Warna Admin Sidebar
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Warna Sidebar -->
                    <div>
                        <label for="warna_sidebar" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Sidebar
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="warna_sidebar" name="warna_sidebar" 
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                            <input type="text" id="warna_sidebar_text" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="#2563EB">
                        </div>
                    </div>

                    <!-- Warna Header Sidebar -->
                    <div>
                        <label for="warna_sidebar_header" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Header Sidebar
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="warna_sidebar_header" name="warna_sidebar_header" 
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                            <input type="text" id="warna_sidebar_header_text" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="#1D4ED8">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warna Landing Page -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-paint-brush mr-2 text-blue-600"></i>
                    Warna Landing Page
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Warna Primary -->
                    <div>
                        <label for="warna_primary" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Primary
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="warna_primary" name="warna_primary" 
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                            <input type="text" id="warna_primary_text" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="#3B82F6">
                        </div>
                    </div>

                    <!-- Warna Secondary -->
                    <div>
                        <label for="warna_secondary" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Secondary
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="warna_secondary" name="warna_secondary" 
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                            <input type="text" id="warna_secondary_text" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="#10B981">
                        </div>
                    </div>

                    <!-- Warna Accent -->
                    <div>
                        <label for="warna_accent" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Accent
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="warna_accent" name="warna_accent" 
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                            <input type="text" id="warna_accent_text" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="#F59E0B">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button type="button" onclick="resetForm()" 
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-undo mr-2"></i>Reset
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>Simpan Pengaturan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Check if API_BASE is already defined
if (typeof API_BASE === 'undefined') {
    const API_BASE = '../api.php';
}

// Load pengaturan saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
    loadPengaturan();
    setupColorInputs();
    setupFileUpload();
});

// Setup color inputs sync
function setupColorInputs() {
    const colorPairs = [
        ['warna_sidebar', 'warna_sidebar_text'],
        ['warna_sidebar_header', 'warna_sidebar_header_text'],
        ['warna_primary', 'warna_primary_text'],
        ['warna_secondary', 'warna_secondary_text'],
        ['warna_accent', 'warna_accent_text']
    ];

    colorPairs.forEach(([colorId, textId]) => {
        const colorInput = document.getElementById(colorId);
        const textInput = document.getElementById(textId);

        colorInput.addEventListener('input', () => {
            textInput.value = colorInput.value;
        });

        textInput.addEventListener('input', () => {
            if (textInput.value.match(/^#[0-9A-Fa-f]{6}$/)) {
                colorInput.value = textInput.value;
            }
        });
    });
}

// Setup file upload
function setupFileUpload() {
    const fileInput = document.getElementById('logo_upload');
    fileInput.addEventListener('change', handleFileSelect);
}

// Handle file selection
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file - accept all common image formats
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'image/svg+xml', 'image/bmp', 'image/tiff', 'image/tif', 'image/ico',
        'image/icon', 'image/x-icon', 'image/vnd.microsoft.icon'
    ];

    const fileExtension = file.name.split('.').pop().toLowerCase();
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'tif', 'ico'];

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        showNotification('Format file tidak didukung. Gunakan format gambar yang valid', 'error');
        return;
    }

    if (file.size > 5 * 1024 * 1024) {
        showNotification('Ukuran file maksimal 5MB', 'error');
        return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        document.getElementById('upload-area').classList.add('hidden');
        document.getElementById('preview-area').classList.remove('hidden');
        document.getElementById('preview-image').src = e.target.result;
        document.getElementById('preview-name').textContent = file.name;
    };
    reader.readAsDataURL(file);
}

// Clear preview
function clearPreview() {
    document.getElementById('logo_upload').value = '';
    document.getElementById('upload-area').classList.remove('hidden');
    document.getElementById('preview-area').classList.add('hidden');
}

// Load pengaturan dari database
async function loadPengaturan() {
    try {
        const response = await fetch('/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');
        const data = await response.json();

        if (data.success && data.data) {
            const pengaturan = data.data;

            // Fill form
            document.getElementById('nama_website').value = pengaturan.nama_website || '';
            document.getElementById('deskripsi_website').value = pengaturan.deskripsi_website || '';

            // Set colors
            setColorValue('warna_sidebar', pengaturan.warna_sidebar || '#2563EB');
            setColorValue('warna_sidebar_header', pengaturan.warna_sidebar_header || '#1D4ED8');
            setColorValue('warna_primary', pengaturan.warna_primary || '#3B82F6');
            setColorValue('warna_secondary', pengaturan.warna_secondary || '#10B981');
            setColorValue('warna_accent', pengaturan.warna_accent || '#F59E0B');

            // Show current logo
            if (pengaturan.logo_file_path) {
                showCurrentLogo(pengaturan.logo_file_path);
            }
        }
    } catch (error) {
        console.error('Error loading pengaturan:', error);
        showNotification('Error memuat pengaturan', 'error');
    }
}

// Set color value
function setColorValue(name, value) {
    document.getElementById(name).value = value;
    document.getElementById(name + '_text').value = value;
}

// Show current logo
function showCurrentLogo(logoPath) {
    let logoUrl = logoPath;

    if (logoPath.startsWith('uploads/')) {
        logoUrl = `http://localhost/react-news/${logoPath}`;
    }

    const logoElement = document.getElementById('current-logo');
    logoElement.innerHTML = `<img src="${logoUrl}" alt="Current Logo" class="w-full h-full object-contain rounded" onerror="this.parentElement.innerHTML='<div class=\\'flex flex-col items-center justify-center h-full\\'><i class=\\'fas fa-image text-4xl text-gray-400 mb-2\\'></i><p class=\\'text-gray-500 text-sm text-center\\'>Logo tidak ditemukan</p></div>'">`;
}

// Handle form submission
document.getElementById('pengaturanForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData();
    formData.append('action', 'update_pengaturan');
    formData.append('nama_website', document.getElementById('nama_website').value);
    formData.append('deskripsi_website', document.getElementById('deskripsi_website').value);
    formData.append('warna_sidebar', document.getElementById('warna_sidebar').value);
    formData.append('warna_sidebar_header', document.getElementById('warna_sidebar_header').value);
    formData.append('warna_primary', document.getElementById('warna_primary').value);
    formData.append('warna_secondary', document.getElementById('warna_secondary').value);
    formData.append('warna_accent', document.getElementById('warna_accent').value);

    // Add logo file if selected
    const logoFile = document.getElementById('logo_upload').files[0];
    if (logoFile) {
        formData.append('logo', logoFile);
    }

    try {
        const response = await fetch('/react-news/frontend/src/pages/admin/simple_api.php', {
            method: 'POST',
            body: formData
        });

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Get response text first to debug
        const responseText = await response.text();
        console.log('API Response:', responseText);

        // Try to parse as JSON
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (jsonError) {
            console.error('JSON Parse Error:', jsonError);
            console.error('Response was:', responseText);
            throw new Error('Server returned invalid JSON response');
        }

        if (result.success) {
            showNotification('Pengaturan berhasil disimpan', 'success');
            loadPengaturan(); // Reload data
            clearPreview(); // Clear upload preview
        } else {
            showNotification(result.message || 'Gagal menyimpan pengaturan', 'error');
        }
    } catch (error) {
        console.error('Error saving pengaturan:', error);

        if (error.message.includes('JSON')) {
            showNotification('Server error: Invalid response format', 'error');
        } else {
            showNotification('Error menyimpan pengaturan: ' + error.message, 'error');
        }
    }
});

// Reset form
function resetForm() {
    if (confirm('Yakin ingin mereset form? Semua perubahan akan hilang.')) {
        loadPengaturan();
        clearPreview();
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Use SweetAlert if available, otherwise use alert
    if (typeof Swal !== 'undefined') {
        if (type === 'success') {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        } else if (type === 'error') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: message
            });
        } else {
            Swal.fire({
                icon: 'info',
                title: 'Info',
                text: message
            });
        }
    } else {
        // Fallback to alert
        alert(`${type.toUpperCase()}: ${message}`);
    }

    // Also log to console
    console.log(`${type.toUpperCase()}: ${message}`);
}
</script>
