<?php
$userId = $_GET['id'] ?? null;

// Debug info
error_log("Edit User - Received ID: " . ($userId ?: 'NULL'));
error_log("Edit User - GET params: " . print_r($_GET, true));

if (!$userId) {
    error_log("Edit User - No user ID provided, redirecting to users page");
    echo "<script>alert('ID pengguna tidak ditemukan'); window.location.href = '?page=users';</script>";
    exit;
}

// Get user data with JWT token
function getUserById($id) {
    try {
        $conn = getConnection();

        // First, try simple query to check if user exists
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            error_log("User not found with ID: " . $id);
            return null;
        }

        // Then try to get JWT token info (if table exists)
        try {
            $stmt = $conn->prepare("
                SELECT u.*,
                       ut.token as jwt_token,
                       ut.expires_at as token_expires,
                       ut.created_at as token_created,
                       CASE
                           WHEN ut.expires_at > NOW() THEN 'active'
                           WHEN ut.expires_at IS NOT NULL THEN 'expired'
                           ELSE 'none'
                       END as token_status
                FROM users u
                LEFT JOIN user_tokens ut ON u.id = ut.user_id
                WHERE u.id = ?
                ORDER BY ut.created_at DESC
                LIMIT 1
            ");
            $stmt->execute([$id]);
            $userWithToken = $stmt->fetch(PDO::FETCH_ASSOC);

            return $userWithToken ?: $user; // Return user with token if available, otherwise basic user

        } catch (PDOException $tokenError) {
            // If user_tokens table doesn't exist, just return basic user info
            error_log("Token table error (this is OK if table doesn't exist): " . $tokenError->getMessage());
            $user['jwt_token'] = null;
            $user['token_expires'] = null;
            $user['token_created'] = null;
            $user['token_status'] = 'none';
            return $user;
        }

    } catch (PDOException $e) {
        error_log("Error getting user: " . $e->getMessage());
        return null;
    }
}

$user = getUserById($userId);

if (!$user) {
    error_log("Edit User - User not found with ID: " . $userId);
    echo '<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">';
    echo '<div class="flex items-center">';
    echo '<i class="fas fa-exclamation-triangle mr-2"></i>';
    echo '<div>';
    echo '<p class="font-medium">Pengguna tidak ditemukan</p>';
    echo '<p class="text-sm">ID pengguna: ' . htmlspecialchars($userId) . '</p>';
    echo '</div>';
    echo '</div>';
    echo '<div class="mt-3">';
    echo '<a href="?page=users" class="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">';
    echo '<i class="fas fa-arrow-left mr-2"></i>Kembali ke Daftar Pengguna';
    echo '</a>';
    echo '</div>';
    echo '</div>';
    return;
}

error_log("Edit User - User found: " . $user['name'] . " (ID: " . $user['id'] . ")");
?>

<div class="space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="?page=users" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-users mr-2"></i>
                    Pengguna
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-sm font-medium text-gray-500">Edit Pengguna</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- User Info Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900"><?php echo htmlspecialchars($user['name']); ?></h2>
                <p class="text-gray-600"><?php echo htmlspecialchars($user['email']); ?></p>
                <p class="text-sm text-gray-500">ID: #<?php echo $user['id']; ?> • Bergabung: <?php echo date('d M Y', strtotime($user['created_at'])); ?></p>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Edit Informasi Pengguna</h3>
        </div>
        
        <form id="editUserForm" class="p-6 space-y-6">
            <input type="hidden" id="userId" value="<?php echo $user['id']; ?>">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="userName" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama Lengkap <span class="text-red-500">*</span>
                    </label>
                    <input 
                        type="text" 
                        id="userName" 
                        name="name"
                        value="<?php echo htmlspecialchars($user['name']); ?>"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                    >
                </div>

                <!-- Email -->
                <div>
                    <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-2">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <input 
                        type="email" 
                        id="userEmail" 
                        name="email"
                        value="<?php echo htmlspecialchars($user['email']); ?>"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                    >
                </div>

                <!-- Role -->
                <div>
                    <label for="userRole" class="block text-sm font-medium text-gray-700 mb-2">
                        Role <span class="text-red-500">*</span>
                    </label>
                    <select
                        id="userRole"
                        name="role"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                    >
                        <option value="user" <?php echo $user['role'] === 'user' ? 'selected' : ''; ?>>
                            👤 User - Pengguna biasa
                        </option>
                        <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>
                            👑 Admin - Administrator sistem
                        </option>
                        <option value="moderator" <?php echo $user['role'] === 'moderator' ? 'selected' : ''; ?>>
                            🛡️ Moderator - Moderator konten
                        </option>
                    </select>
                </div>

                <!-- Status -->
                <div>
                    <label for="userStatus" class="block text-sm font-medium text-gray-700 mb-2">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <select
                        id="userStatus"
                        name="status"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                    >
                        <option value="active" <?php echo ($user['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>
                            🟢 Aktif - Pengguna dapat mengakses semua fitur
                        </option>
                        <option value="inactive" <?php echo ($user['status'] ?? 'active') === 'inactive' ? 'selected' : ''; ?>>
                            🟡 Tidak Aktif - Akses terbatas
                        </option>
                        <option value="suspended" <?php echo ($user['status'] ?? 'active') === 'suspended' ? 'selected' : ''; ?>>
                            🔴 Ditangguhkan - Akses diblokir sementara
                        </option>
                        <option value="banned" <?php echo ($user['status'] ?? 'active') === 'banned' ? 'selected' : ''; ?>>
                            ⛔ Diblokir - Akses diblokir permanen
                        </option>
                    </select>
                </div>
            </div>

            <!-- Password Section -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4">Ubah Password (Opsional)</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            Password Baru
                        </label>
                        <input 
                            type="password" 
                            id="newPassword" 
                            name="new_password"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="Kosongkan jika tidak ingin mengubah"
                        >
                    </div>

                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            Konfirmasi Password
                        </label>
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirm_password"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="Konfirmasi password baru"
                        >
                    </div>
                </div>
            </div>

            <!-- JWT Token Section -->
            <?php if ($user['jwt_token']): ?>
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4">JWT Token</h4>
                <div class="bg-gray-50 p-4 rounded-lg border">
                    <div class="flex items-center justify-between">
                        <code class="text-xs font-mono text-gray-800 break-all flex-1 mr-4"><?php echo htmlspecialchars($user['jwt_token']); ?></code>
                        <button type="button" onclick="copyToken('<?php echo htmlspecialchars($user['jwt_token']); ?>')" 
                                class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                            <i class="fas fa-copy mr-1"></i> Copy
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="?page=users" class="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Kembali
                </a>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="resetForm()" class="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-undo mr-2"></i>Reset
                    </button>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>Simpan Perubahan
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Copy JWT Token function
function copyToken() {
    const tokenField = document.getElementById('jwtTokenField');
    if (tokenField) {
        tokenField.select();
        tokenField.setSelectionRange(0, 99999); // For mobile devices

        navigator.clipboard.writeText(tokenField.value).then(function() {
            showNotification('Token JWT berhasil disalin!', 'success');
        }).catch(function() {
            // Fallback for older browsers
            document.execCommand('copy');
            showNotification('Token JWT berhasil disalin!', 'success');
        });
    }
}

// Generate JWT Token
function generateJWTToken(userId) {
    Swal.fire({
        title: 'Generate JWT Token?',
        text: 'Token baru akan dibuat untuk pengguna ini',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Ya, Generate',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('action', 'generate_jwt_token');
            formData.append('user_id', userId);

            fetch('../api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('JWT Token berhasil dibuat', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message || 'Gagal membuat token', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Terjadi kesalahan saat membuat token', 'error');
            });
        }
    });
}

// Refresh JWT Token
function refreshJWTToken(userId) {
    Swal.fire({
        title: 'Refresh JWT Token?',
        text: 'Token lama akan diganti dengan token baru',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3b82f6',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Ya, Refresh',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('action', 'refresh_jwt_token');
            formData.append('user_id', userId);

            fetch('../api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('JWT Token berhasil di-refresh', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message || 'Gagal refresh token', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Terjadi kesalahan saat refresh token', 'error');
            });
        }
    });
}

// Revoke JWT Token
function revokeJWTToken(userId) {
    Swal.fire({
        title: 'Revoke JWT Token?',
        text: 'Token akan dihapus dan pengguna harus login ulang',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Ya, Revoke',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('action', 'revoke_jwt_token');
            formData.append('user_id', userId);

            fetch('../api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('JWT Token berhasil di-revoke', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message || 'Gagal revoke token', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Terjadi kesalahan saat revoke token', 'error');
            });
        }
    });
}

// Reset form
function resetForm() {
    if (confirm('Apakah Anda yakin ingin mereset form? Semua perubahan akan hilang.')) {
        document.getElementById('editUserForm').reset();
        // Reset to original values
        document.getElementById('userName').value = '<?php echo htmlspecialchars($user['name']); ?>';
        document.getElementById('userEmail').value = '<?php echo htmlspecialchars($user['email']); ?>';
        document.getElementById('userRole').value = '<?php echo $user['role']; ?>';
        document.getElementById('userStatus').value = '<?php echo $user['status'] ?? 'active'; ?>';
    }
}

// Form submission
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = new URLSearchParams();
    data.append('action', 'update_user');
    data.append('id', document.getElementById('userId').value);
    data.append('name', formData.get('name'));
    data.append('email', formData.get('email'));
    data.append('role', formData.get('role'));
    data.append('status', formData.get('status'));

    // Add password if provided
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');

    if (newPassword) {
        if (newPassword !== confirmPassword) {
            showNotification('Password dan konfirmasi password tidak cocok', 'error');
            return;
        }
        if (newPassword.length < 6) {
            showNotification('Password minimal 6 karakter', 'error');
            return;
        }
        data.append('new_password', newPassword);
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
    submitBtn.disabled = true;
    
    fetch('simple_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: data
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('Data pengguna berhasil diperbarui', 'success');
            setTimeout(() => {
                window.location.href = '?page=users';
            }, 1500);
        } else {
            showNotification(result.message || 'Gagal memperbarui data pengguna', 'error');
        }
    })
    .catch(error => {
        showNotification('Terjadi kesalahan saat menyimpan data', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
