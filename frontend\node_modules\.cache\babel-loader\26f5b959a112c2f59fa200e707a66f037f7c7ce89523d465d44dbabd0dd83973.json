{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\VideoPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlay = () => {\n  _s();\n  useEffect(() => {\n    // Redirect to video-play.php\n    window.location.href = '/react-news/frontend/src/pages/user/video-play.php';\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100vh',\n      backgroundColor: '#000',\n      color: '#fff',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '50px',\n          height: '50px',\n          border: '3px solid #f3f3f3',\n          borderTop: '3px solid #3498db',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite',\n          margin: '0 auto 20px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Loading Video Player...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Redirecting to video player...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlay, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = VideoPlay;\nexport default VideoPlay;\nvar _c;\n$RefreshReg$(_c, \"VideoPlay\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "VideoPlay", "_s", "window", "location", "href", "style", "display", "justifyContent", "alignItems", "height", "backgroundColor", "color", "fontFamily", "children", "textAlign", "width", "border", "borderTop", "borderRadius", "animation", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/VideoPlay.js"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nconst VideoPlay = () => {\n  useEffect(() => {\n    // Redirect to video-play.php\n    window.location.href = '/react-news/frontend/src/pages/user/video-play.php';\n  }, []);\n\n  return (\n    <div style={{ \n      display: 'flex', \n      justifyContent: 'center', \n      alignItems: 'center', \n      height: '100vh',\n      backgroundColor: '#000',\n      color: '#fff',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <div style={{ textAlign: 'center' }}>\n        <div style={{ \n          width: '50px', \n          height: '50px', \n          border: '3px solid #f3f3f3',\n          borderTop: '3px solid #3498db',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite',\n          margin: '0 auto 20px'\n        }}></div>\n        <h2>Loading Video Player...</h2>\n        <p>Redirecting to video player...</p>\n        <style>\n          {`\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          `}\n        </style>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtBJ,SAAS,CAAC,MAAM;IACd;IACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,oDAAoD;EAC7E,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA;IAAKM,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,MAAM;MACvBC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eACAd,OAAA;MAAKM,KAAK,EAAE;QAAES,SAAS,EAAE;MAAS,CAAE;MAAAD,QAAA,gBAClCd,OAAA;QAAKM,KAAK,EAAE;UACVU,KAAK,EAAE,MAAM;UACbN,MAAM,EAAE,MAAM;UACdO,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE,yBAAyB;UACpCC,MAAM,EAAE;QACV;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACTzB,OAAA;QAAAc,QAAA,EAAI;MAAuB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCzB,OAAA;QAAAc,QAAA,EAAG;MAA8B;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrCzB,OAAA;QAAAc,QAAA,EACG;AACX;AACA;AACA;AACA;AACA;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAvCID,SAAS;AAAAyB,EAAA,GAATzB,SAAS;AAyCf,eAAeA,SAAS;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}