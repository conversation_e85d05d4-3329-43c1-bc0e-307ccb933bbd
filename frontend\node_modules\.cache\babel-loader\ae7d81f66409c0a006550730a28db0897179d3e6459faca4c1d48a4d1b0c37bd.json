{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport Drawer from '@mui/material/Drawer';\nimport Button from '@mui/material/Button';\nimport Stack from '@mui/material/Stack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  // Data News Component - Fixed Version\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [themeSettings, setThemeSettings] = useState({\n    primary_color: '#3B82F6',\n    secondary_color: '#10B981',\n    accent_color: '#F59E0B'\n  });\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: 'React News Portal'\n  });\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n    // Fetch kostum data\n    fetchKostumData();\n    // Load theme settings\n    loadThemeSettings();\n  }, [id]);\n\n  // Load theme settings from admin\n  const loadThemeSettings = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const text = await response.text();\n      console.log('Raw theme response:', text);\n      let data;\n      try {\n        data = JSON.parse(text);\n      } catch (e) {\n        console.error('JSON parse error:', e);\n        console.error('Response text:', text);\n        throw new Error('Invalid JSON response');\n      }\n      if (data.success && data.data) {\n        setThemeSettings({\n          primary_color: data.data.warna_primary || '#3B82F6',\n          secondary_color: data.data.warna_sidebar || '#10B981',\n          accent_color: data.data.warna_sidebar_header || '#F59E0B'\n        });\n        console.log('🎨 Theme settings loaded:', data.data);\n      }\n    } catch (error) {\n      console.error('❌ Error loading theme settings:', error);\n    }\n  };\n  const fetchKostumData = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n      const data = await response.json();\n      if (data.success && data.data) {\n        const settings = data.data;\n        setKostum({\n          logo: settings.logo_data || settings.logo_file_path || '',\n          title: settings.nama_website || 'React News Portal'\n        });\n        console.log('🎨 Kostum data loaded:', {\n          logo: settings.logo_data ? 'Has logo data' : 'No logo data',\n          title: settings.nama_website\n        });\n      }\n    } catch (error) {\n      // Fallback to default values if API fails\n      setKostum({\n        logo: '',\n        title: 'React News Portal'\n      });\n    }\n  };\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // Fetch news detail (views already incremented from landing page)\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n    console.log('data-news.js - Processing image path:', imagePath);\n\n    // If it's base64 data (for logos), return as is\n    if (imagePath.startsWith('data:image/')) {\n      console.log('data-news.js - Using base64 data');\n      return imagePath;\n    }\n\n    // Jika sudah URL lengkap, gunakan langsung\n    if (imagePath.startsWith('http')) {\n      console.log('data-news.js - Using full URL:', imagePath);\n      return imagePath;\n    }\n\n    // If it's already the correct path, use it directly\n    if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n      const finalUrl = `http://localhost${imagePath}`;\n      console.log('data-news.js - Using direct path:', finalUrl);\n      return finalUrl;\n    }\n\n    // Extract filename from any path format\n    let filename = '';\n    if (imagePath.startsWith('/react-news/uploads/')) {\n      filename = imagePath.replace('/react-news/uploads/', '');\n    } else if (imagePath.startsWith('/uploads/')) {\n      filename = imagePath.replace('/uploads/', '');\n    } else if (imagePath.startsWith('assets/news/')) {\n      filename = imagePath.replace('assets/news/', '');\n    } else if (!imagePath.includes('/')) {\n      // Just filename\n      filename = imagePath;\n    } else {\n      // Extract filename from any other path\n      filename = imagePath.split('/').pop();\n    }\n\n    // Use consistent frontend/uploads path\n    const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n    console.log('data-news.js - Final URL:', finalUrl);\n    return finalUrl;\n  };\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\",\n          style: {\n            borderBottomColor: themeSettings.primary_color\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color,\n            ':hover': {\n              backgroundColor: themeSettings.secondary_color\n            }\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 text-white px-6 py-2 rounded-lg transition-colors\",\n          style: {\n            backgroundColor: themeSettings.primary_color\n          },\n          onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n          onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'gray.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: {\n            xs: 70,\n            md: 80\n          },\n          px: {\n            xs: 2,\n            md: 6\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: {\n                xs: 22,\n                md: 28\n              }\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/'),\n          sx: {\n            mr: 1\n          },\n          title: \"Kembali ke Beranda\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/video'),\n          sx: {\n            mr: 1\n          },\n          title: \"Video\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle\",\n            style: {\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: handleSidebar,\n          sx: {\n            ml: 1\n          },\n          title: \"Menu\",\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: handleSidebarClose,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        zIndex: 2000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 260,\n          p: 3,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSidebarClose,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 1.5,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                navigate('/');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-home\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 33\n              }, this), \"Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                navigate('/saved');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bookmark\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 33\n              }, this), \"Berita Tersimpan\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                navigate('/video');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-play-circle\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 33\n              }, this), \"Video\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Navigation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"primary\",\n              fullWidth: true,\n              sx: {\n                textTransform: 'none',\n                borderColor: 'primary.main',\n                color: 'primary.main',\n                '&:hover': {\n                  borderColor: 'primary.main',\n                  backgroundColor: 'primary.main',\n                  color: 'white'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                navigate('/');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'white',\n        boxShadow: 1,\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        mt: {\n          xs: '70px',\n          md: '80px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 2,\n          py: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            fontSize: '14px',\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"button\",\n            onClick: handleBackToHome,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main'\n              },\n              transition: 'color 0.2s',\n              border: 'none',\n              background: 'none',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home\",\n              style: {\n                marginRight: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right\",\n            style: {\n              fontSize: '12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: '1200px',\n        mx: 'auto',\n        px: 2,\n        py: 4,\n        pb: {\n          xs: 12,\n          md: 4\n        } // More padding on mobile for bottom nav\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            lg: '2fr 1fr'\n          },\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://picsum.photos/800/400?random=2';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  style: {\n                    backgroundColor: themeSettings.primary_color\n                  },\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2\",\n                    style: {\n                      color: themeSettings.primary_color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                    style: {\n                      backgroundColor: themeSettings.primary_color\n                    },\n                    onMouseEnter: e => e.target.style.backgroundColor = themeSettings.secondary_color,\n                    onMouseLeave: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white px-4 py-2 rounded-lg transition-colors\",\n                    style: {\n                      backgroundColor: themeSettings.accent_color\n                    },\n                    onMouseEnter: e => e.target.style.backgroundColor = themeSettings.primary_color,\n                    onMouseLeave: e => e.target.style.backgroundColor = themeSettings.accent_color,\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2\",\n                style: {\n                  color: themeSettings.primary_color\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: 'white',\n              borderRadius: 2,\n              boxShadow: 2,\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"button\",\n              onClick: handleBackToHome,\n              sx: {\n                width: '100%',\n                bgcolor: 'primary.main',\n                color: 'white',\n                py: 1.5,\n                px: 2,\n                borderRadius: 2,\n                border: 'none',\n                cursor: 'pointer',\n                fontWeight: 500,\n                transition: 'background-color 0.2s',\n                '&:hover': {\n                  bgcolor: 'primary.dark'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        // Hide on desktop\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"sDau4pGRAX1/GyVsTHECO1kU39M=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "IconButton", "MenuIcon", "CloseIcon", "ArrowBackIcon", "useMediaQuery", "useTheme", "Drawer", "<PERSON><PERSON>", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "theme", "isDesktop", "breakpoints", "up", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "themeSettings", "setThemeSettings", "primary_color", "secondary_color", "accent_color", "kostum", "setKostum", "logo", "title", "sidebarOpen", "setSidebarOpen", "fetchNewsDetail", "fetchKostumData", "loadThemeSettings", "response", "fetch", "ok", "Error", "status", "text", "console", "log", "data", "JSON", "parse", "e", "success", "warna_primary", "warna_sidebar", "warna_sidebar_header", "json", "settings", "logo_data", "logo_file_path", "nama_website", "newsId", "category_id", "fetchRelatedNews", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "finalUrl", "filename", "replace", "includes", "split", "pop", "handleSidebar", "handleSidebarClose", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "style", "borderBottomColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backgroundColor", "onMouseEnter", "target", "onMouseLeave", "sx", "minHeight", "bgcolor", "width", "overflow", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "xs", "md", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "variant", "fontWeight", "fontSize", "edge", "ml", "anchor", "open", "onClose", "ModalProps", "keepMounted", "p", "top", "right", "mb", "Divider", "textTransform", "letterSpacing", "spacing", "fullWidth", "justifyContent", "marginRight", "boxShadow", "mt", "max<PERSON><PERSON><PERSON>", "mx", "py", "gap", "component", "transition", "border", "background", "cursor", "pb", "gridTemplateColumns", "lg", "image", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "length", "map", "borderRadius", "left", "bottom", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport Drawer from '@mui/material/Drawer';\nimport Button from '@mui/material/Button';\nimport Stack from '@mui/material/Stack';\n\nconst DataNews = () => {\n    // Data News Component - Fixed Version\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const theme = useTheme();\n    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [themeSettings, setThemeSettings] = useState({\n        primary_color: '#3B82F6',\n        secondary_color: '#10B981',\n        accent_color: '#F59E0B'\n    });\n    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });\n    const [sidebarOpen, setSidebarOpen] = useState(false);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n        // Fetch kostum data\n        fetchKostumData();\n        // Load theme settings\n        loadThemeSettings();\n    }, [id]);\n\n    // Load theme settings from admin\n    const loadThemeSettings = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const text = await response.text();\n            console.log('Raw theme response:', text);\n\n            let data;\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                console.error('JSON parse error:', e);\n                console.error('Response text:', text);\n                throw new Error('Invalid JSON response');\n            }\n\n            if (data.success && data.data) {\n                setThemeSettings({\n                    primary_color: data.data.warna_primary || '#3B82F6',\n                    secondary_color: data.data.warna_sidebar || '#10B981',\n                    accent_color: data.data.warna_sidebar_header || '#F59E0B'\n                });\n                console.log('🎨 Theme settings loaded:', data.data);\n            }\n        } catch (error) {\n            console.error('❌ Error loading theme settings:', error);\n        }\n    };\n\n    const fetchKostumData = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan');\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                const settings = data.data;\n                setKostum({\n                    logo: settings.logo_data || settings.logo_file_path || '',\n                    title: settings.nama_website || 'React News Portal'\n                });\n                console.log('🎨 Kostum data loaded:', {\n                    logo: settings.logo_data ? 'Has logo data' : 'No logo data',\n                    title: settings.nama_website\n                });\n            }\n        } catch (error) {\n            // Fallback to default values if API fails\n            setKostum({\n                logo: '',\n                title: 'React News Portal'\n            });\n        }\n    };\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // Fetch news detail (views already incremented from landing page)\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n\n        console.log('data-news.js - Processing image path:', imagePath);\n\n        // If it's base64 data (for logos), return as is\n        if (imagePath.startsWith('data:image/')) {\n            console.log('data-news.js - Using base64 data');\n            return imagePath;\n        }\n\n        // Jika sudah URL lengkap, gunakan langsung\n        if (imagePath.startsWith('http')) {\n            console.log('data-news.js - Using full URL:', imagePath);\n            return imagePath;\n        }\n\n        // If it's already the correct path, use it directly\n        if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n            const finalUrl = `http://localhost${imagePath}`;\n            console.log('data-news.js - Using direct path:', finalUrl);\n            return finalUrl;\n        }\n\n        // Extract filename from any path format\n        let filename = '';\n\n        if (imagePath.startsWith('/react-news/uploads/')) {\n            filename = imagePath.replace('/react-news/uploads/', '');\n        } else if (imagePath.startsWith('/uploads/')) {\n            filename = imagePath.replace('/uploads/', '');\n        } else if (imagePath.startsWith('assets/news/')) {\n            filename = imagePath.replace('assets/news/', '');\n        } else if (!imagePath.includes('/')) {\n            // Just filename\n            filename = imagePath;\n        } else {\n            // Extract filename from any other path\n            filename = imagePath.split('/').pop();\n        }\n\n        // Use consistent frontend/uploads path\n        const finalUrl = `http://localhost/react-news/frontend/uploads/${filename}`;\n        console.log('data-news.js - Final URL:', finalUrl);\n        return finalUrl;\n    };\n\n    const handleSidebar = () => setSidebarOpen(true);\n    const handleSidebarClose = () => setSidebarOpen(false);\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div\n                        className=\"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4\"\n                        style={{ borderBottomColor: themeSettings.primary_color }}\n                    ></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color,\n                            ':hover': { backgroundColor: themeSettings.secondary_color }\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button\n                        onClick={handleBackToHome}\n                        className=\"mt-4 text-white px-6 py-2 rounded-lg transition-colors\"\n                        style={{\n                            backgroundColor: themeSettings.primary_color\n                        }}\n                        onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                        onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>\n            {/* Responsive Navigation Bar */}\n            <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 48, height: 48, mr: 2 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/')}\n                        sx={{ mr: 1 }}\n                        title=\"Kembali ke Beranda\"\n                    >\n                        <ArrowBackIcon fontSize=\"large\" />\n                    </IconButton>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/video')}\n                        sx={{ mr: 1 }}\n                        title=\"Video\"\n                    >\n                        <i className=\"fas fa-play-circle\" style={{ fontSize: 24 }}></i>\n                    </IconButton>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={handleSidebar}\n                        sx={{ ml: 1 }}\n                        title=\"Menu\"\n                    >\n                        <MenuIcon fontSize=\"large\" />\n                    </IconButton>\n                </Toolbar>\n            </AppBar>\n\n            {/* Sidebar Drawer */}\n            <Drawer\n                anchor=\"right\"\n                open={sidebarOpen}\n                onClose={handleSidebarClose}\n                ModalProps={{ keepMounted: true }}\n                sx={{ zIndex: 2000 }}\n            >\n                <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n                    <IconButton\n                        onClick={handleSidebarClose}\n                        sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n                        aria-label=\"Tutup\"\n                    >\n                        <CloseIcon />\n                    </IconButton>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 32, height: 32, mr: 1 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <Divider sx={{ mb: 2 }} />\n\n                    {/* Quick Actions */}\n                    <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"subtitle2\" sx={{\n                            fontWeight: 600,\n                            color: 'text.secondary',\n                            mb: 1.5,\n                            fontSize: 12,\n                            textTransform: 'uppercase',\n                            letterSpacing: 0.5\n                        }}>\n                            Quick Actions\n                        </Typography>\n                        <Stack spacing={1.5}>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/');\n                                }}\n                            >\n                                <i className=\"fas fa-home\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Beranda\n                            </Button>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/saved');\n                                }}\n                            >\n                                <i className=\"fas fa-bookmark\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Berita Tersimpan\n                            </Button>\n                            <Button\n                                variant=\"text\"\n                                fullWidth\n                                sx={{\n                                    justifyContent: 'flex-start',\n                                    textTransform: 'none',\n                                    color: 'text.primary',\n                                    '&:hover': {\n                                        backgroundColor: 'action.hover'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/video');\n                                }}\n                            >\n                                <i className=\"fas fa-play-circle\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Video\n                            </Button>\n                        </Stack>\n                    </Box>\n\n                    <Divider sx={{ mb: 2 }} />\n\n                    {/* Navigation */}\n                    <Box>\n                        <Typography variant=\"subtitle2\" sx={{\n                            fontWeight: 600,\n                            color: 'text.secondary',\n                            mb: 1.5,\n                            fontSize: 12,\n                            textTransform: 'uppercase',\n                            letterSpacing: 0.5\n                        }}>\n                            Navigation\n                        </Typography>\n                        <Stack spacing={2}>\n                            <Button\n                                variant=\"outlined\"\n                                color=\"primary\"\n                                fullWidth\n                                sx={{\n                                    textTransform: 'none',\n                                    borderColor: 'primary.main',\n                                    color: 'primary.main',\n                                    '&:hover': {\n                                        borderColor: 'primary.main',\n                                        backgroundColor: 'primary.main',\n                                        color: 'white'\n                                    }\n                                }}\n                                onClick={() => {\n                                    handleSidebarClose();\n                                    navigate('/');\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                                Kembali ke Beranda\n                            </Button>\n                        </Stack>\n                    </Box>\n                </Box>\n            </Drawer>\n\n            {/* Header/Breadcrumb */}\n            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>\n                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>\n                        <Box\n                            component=\"button\"\n                            onClick={handleBackToHome}\n                            sx={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'text.secondary',\n                                '&:hover': { color: 'primary.main' },\n                                transition: 'color 0.2s',\n                                border: 'none',\n                                background: 'none',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            <i className=\"fas fa-home\" style={{ marginRight: '4px' }}></i>\n                            Beranda\n                        </Box>\n                        <i className=\"fas fa-chevron-right\" style={{ fontSize: '12px' }}></i>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n                            Detail Berita\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box sx={{\n                maxWidth: '1200px',\n                mx: 'auto',\n                px: 2,\n                py: 4,\n                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav\n            }}>\n                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>\n                    {/* Main Article */}\n                    <Box>\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://picsum.photos/800/400?random=2';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span\n                                        className=\"text-white px-3 py-1 rounded-full text-sm font-medium\"\n                                        style={{ backgroundColor: themeSettings.primary_color }}\n                                    >\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-calendar-alt mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-clock mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i\n                                            className=\"fas fa-eye mr-2\"\n                                            style={{ color: themeSettings.primary_color }}\n                                        ></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.primary_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.secondary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                        >\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button\n                                            className=\"text-white px-4 py-2 rounded-lg transition-colors\"\n                                            style={{ backgroundColor: themeSettings.accent_color }}\n                                            onMouseEnter={(e) => e.target.style.backgroundColor = themeSettings.primary_color}\n                                            onMouseLeave={(e) => e.target.style.backgroundColor = themeSettings.accent_color}\n                                        >\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </Box>\n\n                    {/* Sidebar */}\n                    <Box>\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i\n                                        className=\"fas fa-newspaper mr-2\"\n                                        style={{ color: themeSettings.primary_color }}\n                                    ></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>\n                            <Box\n                                component=\"button\"\n                                onClick={handleBackToHome}\n                                sx={{\n                                    width: '100%',\n                                    bgcolor: 'primary.main',\n                                    color: 'white',\n                                    py: 1.5,\n                                    px: 2,\n                                    borderRadius: 2,\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontWeight: 500,\n                                    transition: 'background-color 0.2s',\n                                    '&:hover': {\n                                        bgcolor: 'primary.dark'\n                                    }\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: '8px' }}></i>\n                                Kembali ke Beranda\n                            </Box>\n                        </Box>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Custom Bottom Navigation - Mobile Only */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: { xs: 'block', md: 'none' }, // Hide on desktop\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </Box>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB;EACA,MAAM;IAAEC;EAAG,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAC1B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,KAAK,GAAGV,QAAQ,CAAC,CAAC;EACxB,MAAMW,SAAS,GAAGZ,aAAa,CAACW,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC;IAC/CsC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC;IAAE2C,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAoB,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZ,IAAIqB,EAAE,EAAE;MACJyB,eAAe,CAACzB,EAAE,CAAC;IACvB;IACA;IACA0B,eAAe,CAAC,CAAC;IACjB;IACAC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC3B,EAAE,CAAC,CAAC;;EAER;EACA,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MAEzH,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC;MAExC,IAAIG,IAAI;MACR,IAAI;QACAA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOM,CAAC,EAAE;QACRL,OAAO,CAACtB,KAAK,CAAC,mBAAmB,EAAE2B,CAAC,CAAC;QACrCL,OAAO,CAACtB,KAAK,CAAC,gBAAgB,EAAEqB,IAAI,CAAC;QACrC,MAAM,IAAIF,KAAK,CAAC,uBAAuB,CAAC;MAC5C;MAEA,IAAIK,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3BrB,gBAAgB,CAAC;UACbC,aAAa,EAAEoB,IAAI,CAACA,IAAI,CAACK,aAAa,IAAI,SAAS;UACnDxB,eAAe,EAAEmB,IAAI,CAACA,IAAI,CAACM,aAAa,IAAI,SAAS;UACrDxB,YAAY,EAAEkB,IAAI,CAACA,IAAI,CAACO,oBAAoB,IAAI;QACpD,CAAC,CAAC;QACFT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,IAAI,CAACA,IAAI,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,CAAC;MACzH,MAAMO,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B,MAAMS,QAAQ,GAAGT,IAAI,CAACA,IAAI;QAC1BhB,SAAS,CAAC;UACNC,IAAI,EAAEwB,QAAQ,CAACC,SAAS,IAAID,QAAQ,CAACE,cAAc,IAAI,EAAE;UACzDzB,KAAK,EAAEuB,QAAQ,CAACG,YAAY,IAAI;QACpC,CAAC,CAAC;QACFd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;UAClCd,IAAI,EAAEwB,QAAQ,CAACC,SAAS,GAAG,eAAe,GAAG,cAAc;UAC3DxB,KAAK,EAAEuB,QAAQ,CAACG;QACpB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACZ;MACAQ,SAAS,CAAC;QACNC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMG,eAAe,GAAG,MAAOwB,MAAM,IAAK;IACtC,IAAI;MACAtC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGoB,MAAM,EAAE,CAAC;MAEtI,IAAI,CAACrB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIJ,IAAI,CAACA,IAAI,EAAE;QAC3B7B,OAAO,CAAC6B,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACc,WAAW,EAAE;UACvBC,gBAAgB,CAACf,IAAI,CAACA,IAAI,CAACc,WAAW,EAAED,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHpC,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwC,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMzB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGuB,UAAU,UAAU,CAAC;MAElJ,IAAI,CAACxB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMI,IAAI,GAAG,MAAMR,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIR,IAAI,CAACI,OAAO,IAAIc,KAAK,CAACC,OAAO,CAACnB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMoB,QAAQ,GAAGpB,IAAI,CAACA,IAAI,CACrBqB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC1D,EAAE,KAAK2D,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChBnD,cAAc,CAAC+C,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACH/C,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMoD,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,2CAA2C;IAElE5B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2B,SAAS,CAAC;;IAE/D;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;MACrC7B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C,OAAO2B,SAAS;IACpB;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9B7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2B,SAAS,CAAC;MACxD,OAAOA,SAAS;IACpB;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,+BAA+B,CAAC,EAAE;MACvD,MAAMC,QAAQ,GAAG,mBAAmBF,SAAS,EAAE;MAC/C5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE6B,QAAQ,CAAC;MAC1D,OAAOA,QAAQ;IACnB;;IAEA;IACA,IAAIC,QAAQ,GAAG,EAAE;IAEjB,IAAIH,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;MAC9CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;IAC5D,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;MAC1CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACjD,CAAC,MAAM,IAAIJ,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;MAC7CE,QAAQ,GAAGH,SAAS,CAACI,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACpD,CAAC,MAAM,IAAI,CAACJ,SAAS,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACAF,QAAQ,GAAGH,SAAS;IACxB,CAAC,MAAM;MACH;MACAG,QAAQ,GAAGH,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;IACzC;;IAEA;IACA,MAAML,QAAQ,GAAG,gDAAgDC,QAAQ,EAAE;IAC3E/B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,QAAQ,CAAC;IAClD,OAAOA,QAAQ;EACnB,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM9C,cAAc,CAAC,IAAI,CAAC;EAChD,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM/C,cAAc,CAAC,KAAK,CAAC;EAEtD,MAAMgD,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIpC,MAAM,IAAK;IACvChD,QAAQ,CAAC,cAAcgD,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BrF,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIS,OAAO,EAAE;IACT,oBACIb,OAAA;MAAK0F,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3F,OAAA;QAAK0F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3F,OAAA;UACI0F,SAAS,EAAC,6DAA6D;UACvEE,KAAK,EAAE;YAAEC,iBAAiB,EAAE5E,aAAa,CAACE;UAAc;QAAE;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACPjG,OAAA;UAAG0F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIlF,KAAK,EAAE;IACP,oBACIf,OAAA;MAAK0F,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3F,OAAA;QAAK0F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3F,OAAA;UAAK0F,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpF3F,OAAA;YAAG0F,SAAS,EAAC;UAAkC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnDlF,KAAK;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNjG,OAAA;UACIkG,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,mDAAmD;UAC7DE,KAAK,EAAE;YACHO,eAAe,EAAElF,aAAa,CAACE,aAAa;YAC5C,QAAQ,EAAE;cAAEgF,eAAe,EAAElF,aAAa,CAACG;YAAgB;UAC/D,CAAE;UACFgF,YAAY,EAAG1D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACG,eAAgB;UACpFkF,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACE,aAAc;UAAAwE,QAAA,gBAElF3F,OAAA;YAAG0F,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAACxF,IAAI,EAAE;IACP,oBACIT,OAAA;MAAK0F,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3F,OAAA;QAAK0F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3F,OAAA;UAAG0F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDjG,OAAA;UACIkG,OAAO,EAAET,gBAAiB;UAC1BC,SAAS,EAAC,wDAAwD;UAClEE,KAAK,EAAE;YACHO,eAAe,EAAElF,aAAa,CAACE;UACnC,CAAE;UACFiF,YAAY,EAAG1D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACG,eAAgB;UACpFkF,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACE,aAAc;UAAAwE,QAAA,gBAElF3F,OAAA;YAAG0F,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIjG,OAAA,CAACf,GAAG;IAACsH,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAhB,QAAA,gBAEpF3F,OAAA,CAACb,MAAM;MAACyH,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACP,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAtB,QAAA,eACnI3F,OAAA,CAACZ,OAAO;QAACmH,EAAE,EAAE;UAAEC,SAAS,EAAE;YAAEU,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAAEC,EAAE,EAAE;YAAEF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAAxB,QAAA,gBACjE3F,OAAA,CAACf,GAAG;UAACsH,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBAC5D3F,OAAA,CAACX,MAAM;YACHmI,GAAG,EAAElG,MAAM,CAACE,IAAI,GAAGwC,WAAW,CAAC1C,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;YAC7DiG,GAAG,EAAC,MAAM;YACVlB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEgB,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAGlF,CAAC,IAAK;cAAEA,CAAC,CAAC2D,MAAM,CAACmB,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACFjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEjB,KAAK,EAAE,cAAc;cAAEkB,QAAQ,EAAE;gBAAEb,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAAE,CAAE;YAAAxB,QAAA,EACjGrE,MAAM,CAACG;UAAK;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACNjG,OAAA,CAACV,UAAU;UACP0I,IAAI,EAAC,KAAK;UACVnB,KAAK,EAAC,SAAS;UACfX,OAAO,EAAEA,CAAA,KAAM9F,QAAQ,CAAC,GAAG,CAAE;UAC7BmG,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UACdlG,KAAK,EAAC,oBAAoB;UAAAkE,QAAA,eAE1B3F,OAAA,CAACP,aAAa;YAACsI,QAAQ,EAAC;UAAO;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACbjG,OAAA,CAACV,UAAU;UACP0I,IAAI,EAAC,KAAK;UACVnB,KAAK,EAAC,SAAS;UACfX,OAAO,EAAEA,CAAA,KAAM9F,QAAQ,CAAC,QAAQ,CAAE;UAClCmG,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UACdlG,KAAK,EAAC,OAAO;UAAAkE,QAAA,eAEb3F,OAAA;YAAG0F,SAAS,EAAC,oBAAoB;YAACE,KAAK,EAAE;cAAEmC,QAAQ,EAAE;YAAG;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACbjG,OAAA,CAACV,UAAU;UACP0I,IAAI,EAAC,KAAK;UACVnB,KAAK,EAAC,SAAS;UACfX,OAAO,EAAEzB,aAAc;UACvB8B,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UACdxG,KAAK,EAAC,MAAM;UAAAkE,QAAA,eAEZ3F,OAAA,CAACT,QAAQ;YAACwI,QAAQ,EAAC;UAAO;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTjG,OAAA,CAACJ,MAAM;MACHsI,MAAM,EAAC,OAAO;MACdC,IAAI,EAAEzG,WAAY;MAClB0G,OAAO,EAAE1D,kBAAmB;MAC5B2D,UAAU,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAE;MAClC/B,EAAE,EAAE;QAAEU,MAAM,EAAE;MAAK,CAAE;MAAAtB,QAAA,eAErB3F,OAAA,CAACf,GAAG;QAACsH,EAAE,EAAE;UAAEG,KAAK,EAAE,GAAG;UAAE6B,CAAC,EAAE,CAAC;UAAE3B,QAAQ,EAAE;QAAW,CAAE;QAAAjB,QAAA,gBAChD3F,OAAA,CAACV,UAAU;UACP4G,OAAO,EAAExB,kBAAmB;UAC5B6B,EAAE,EAAE;YAAEK,QAAQ,EAAE,UAAU;YAAE4B,GAAG,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAExB,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAtB,QAAA,eAElB3F,OAAA,CAACR,SAAS;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEbjG,OAAA,CAACf,GAAG;UAACsH,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACtD3F,OAAA,CAACX,MAAM;YACHmI,GAAG,EAAElG,MAAM,CAACE,IAAI,GAAGwC,WAAW,CAAC1C,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;YAC7DiG,GAAG,EAAC,MAAM;YACVlB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEgB,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAGlF,CAAC,IAAK;cAAEA,CAAC,CAAC2D,MAAM,CAACmB,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACFjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEjB,KAAK,EAAE,cAAc;cAAEkB,QAAQ,EAAE;YAAG,CAAE;YAAApC,QAAA,EACjFrE,MAAM,CAACG;UAAK;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACNjG,OAAA,CAAC2I,OAAO;UAACpC,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BjG,OAAA,CAACf,GAAG;UAACsH,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACf3F,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,WAAW;YAACtB,EAAE,EAAE;cAChCuB,UAAU,EAAE,GAAG;cACfjB,KAAK,EAAE,gBAAgB;cACvB6B,EAAE,EAAE,GAAG;cACPX,QAAQ,EAAE,EAAE;cACZa,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE;YACnB,CAAE;YAAAlD,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjG,OAAA,CAACF,KAAK;YAACgJ,OAAO,EAAE,GAAI;YAAAnD,QAAA,gBAChB3F,OAAA,CAACH,MAAM;cACHgI,OAAO,EAAC,MAAM;cACdkB,SAAS;cACTxC,EAAE,EAAE;gBACAyC,cAAc,EAAE,YAAY;gBAC5BJ,aAAa,EAAE,MAAM;gBACrB/B,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACPV,eAAe,EAAE;gBACrB;cACJ,CAAE;cACFD,OAAO,EAAEA,CAAA,KAAM;gBACXxB,kBAAkB,CAAC,CAAC;gBACpBtE,QAAQ,CAAC,GAAG,CAAC;cACjB,CAAE;cAAAuF,QAAA,gBAEF3F,OAAA;gBAAG0F,SAAS,EAAC,aAAa;gBAACE,KAAK,EAAE;kBAAEqD,WAAW,EAAE,CAAC;kBAAElB,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAE5E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjG,OAAA,CAACH,MAAM;cACHgI,OAAO,EAAC,MAAM;cACdkB,SAAS;cACTxC,EAAE,EAAE;gBACAyC,cAAc,EAAE,YAAY;gBAC5BJ,aAAa,EAAE,MAAM;gBACrB/B,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACPV,eAAe,EAAE;gBACrB;cACJ,CAAE;cACFD,OAAO,EAAEA,CAAA,KAAM;gBACXxB,kBAAkB,CAAC,CAAC;gBACpBtE,QAAQ,CAAC,QAAQ,CAAC;cACtB,CAAE;cAAAuF,QAAA,gBAEF3F,OAAA;gBAAG0F,SAAS,EAAC,iBAAiB;gBAACE,KAAK,EAAE;kBAAEqD,WAAW,EAAE,CAAC;kBAAElB,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAEhF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjG,OAAA,CAACH,MAAM;cACHgI,OAAO,EAAC,MAAM;cACdkB,SAAS;cACTxC,EAAE,EAAE;gBACAyC,cAAc,EAAE,YAAY;gBAC5BJ,aAAa,EAAE,MAAM;gBACrB/B,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACPV,eAAe,EAAE;gBACrB;cACJ,CAAE;cACFD,OAAO,EAAEA,CAAA,KAAM;gBACXxB,kBAAkB,CAAC,CAAC;gBACpBtE,QAAQ,CAAC,QAAQ,CAAC;cACtB,CAAE;cAAAuF,QAAA,gBAEF3F,OAAA;gBAAG0F,SAAS,EAAC,oBAAoB;gBAACE,KAAK,EAAE;kBAAEqD,WAAW,EAAE,CAAC;kBAAElB,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAEnF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAENjG,OAAA,CAAC2I,OAAO;UAACpC,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BjG,OAAA,CAACf,GAAG;UAAA0G,QAAA,gBACA3F,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,WAAW;YAACtB,EAAE,EAAE;cAChCuB,UAAU,EAAE,GAAG;cACfjB,KAAK,EAAE,gBAAgB;cACvB6B,EAAE,EAAE,GAAG;cACPX,QAAQ,EAAE,EAAE;cACZa,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE;YACnB,CAAE;YAAAlD,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjG,OAAA,CAACF,KAAK;YAACgJ,OAAO,EAAE,CAAE;YAAAnD,QAAA,eACd3F,OAAA,CAACH,MAAM;cACHgI,OAAO,EAAC,UAAU;cAClBhB,KAAK,EAAC,SAAS;cACfkC,SAAS;cACTxC,EAAE,EAAE;gBACAqC,aAAa,EAAE,MAAM;gBACrB5B,WAAW,EAAE,cAAc;gBAC3BH,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACPG,WAAW,EAAE,cAAc;kBAC3Bb,eAAe,EAAE,cAAc;kBAC/BU,KAAK,EAAE;gBACX;cACJ,CAAE;cACFX,OAAO,EAAEA,CAAA,KAAM;gBACXxB,kBAAkB,CAAC,CAAC;gBACpBtE,QAAQ,CAAC,GAAG,CAAC;cACjB,CAAE;cAAAuF,QAAA,gBAEF3F,OAAA;gBAAG0F,SAAS,EAAC,mBAAmB;gBAACE,KAAK,EAAE;kBAAEqD,WAAW,EAAE,CAAC;kBAAElB,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAElF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGTjG,OAAA,CAACf,GAAG;MAACsH,EAAE,EAAE;QAAEE,OAAO,EAAE,OAAO;QAAEyC,SAAS,EAAE,CAAC;QAAEnC,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEmC,EAAE,EAAE;UAAEjC,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO;MAAE,CAAE;MAAAxB,QAAA,eAClH3F,OAAA,CAACf,GAAG;QAACsH,EAAE,EAAE;UAAE6C,QAAQ,EAAE,QAAQ;UAAEC,EAAE,EAAE,MAAM;UAAEjC,EAAE,EAAE,CAAC;UAAEkC,EAAE,EAAE;QAAE,CAAE;QAAA3D,QAAA,eACtD3F,OAAA,CAACf,GAAG;UAACsH,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEiC,GAAG,EAAE,CAAC;YAAExB,QAAQ,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAiB,CAAE;UAAAlB,QAAA,gBAClG3F,OAAA,CAACf,GAAG;YACAuK,SAAS,EAAC,QAAQ;YAClBtD,OAAO,EAAET,gBAAiB;YAC1Bc,EAAE,EAAE;cACAc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBT,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBAAEA,KAAK,EAAE;cAAe,CAAC;cACpC4C,UAAU,EAAE,YAAY;cACxBC,MAAM,EAAE,MAAM;cACdC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE;YACZ,CAAE;YAAAjE,QAAA,gBAEF3F,OAAA;cAAG0F,SAAS,EAAC,aAAa;cAACE,KAAK,EAAE;gBAAEqD,WAAW,EAAE;cAAM;YAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAElE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjG,OAAA;YAAG0F,SAAS,EAAC,sBAAsB;YAACE,KAAK,EAAE;cAAEmC,QAAQ,EAAE;YAAO;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEjB,KAAK,EAAE;YAAe,CAAE;YAAAlB,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjG,OAAA,CAACf,GAAG;MAACsH,EAAE,EAAE;QACL6C,QAAQ,EAAE,QAAQ;QAClBC,EAAE,EAAE,MAAM;QACVjC,EAAE,EAAE,CAAC;QACLkC,EAAE,EAAE,CAAC;QACLO,EAAE,EAAE;UAAE3C,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,CAAC;MAC1B,CAAE;MAAAxB,QAAA,eACE3F,OAAA,CAACf,GAAG;QAACsH,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEyC,mBAAmB,EAAE;YAAE5C,EAAE,EAAE,KAAK;YAAE6C,EAAE,EAAE;UAAU,CAAC;UAAER,GAAG,EAAE;QAAE,CAAE;QAAA5D,QAAA,gBAEpF3F,OAAA,CAACf,GAAG;UAAA0G,QAAA,eACA3F,OAAA;YAAS0F,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9D3F,OAAA;cAAK0F,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB3F,OAAA;gBACIwH,GAAG,EAAExD,WAAW,CAACvD,IAAI,CAACuJ,KAAK,CAAE;gBAC7BvC,GAAG,EAAEhH,IAAI,CAACgB,KAAM;gBAChBiE,SAAS,EAAC,kCAAkC;gBAC5CkC,OAAO,EAAGlF,CAAC,IAAK;kBACZA,CAAC,CAAC2D,MAAM,CAACmB,GAAG,GAAG,wCAAwC;gBAC3D;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACFjG,OAAA;gBAAK0F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClC3F,OAAA;kBACI0F,SAAS,EAAC,uDAAuD;kBACjEE,KAAK,EAAE;oBAAEO,eAAe,EAAElF,aAAa,CAACE;kBAAc,CAAE;kBAAAwE,QAAA,EAEvDlF,IAAI,CAACwJ,aAAa,IAAI;gBAAQ;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNjG,OAAA;cAAK0F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB3F,OAAA;gBAAI0F,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1ElF,IAAI,CAACgB;cAAK;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGLjG,OAAA;gBAAK0F,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvF3F,OAAA;kBAAK0F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3F,OAAA;oBACI0F,SAAS,EAAC,0BAA0B;oBACpCE,KAAK,EAAE;sBAAEiB,KAAK,EAAE5F,aAAa,CAACE;oBAAc;kBAAE;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJtB,UAAU,CAAClE,IAAI,CAACyJ,UAAU,IAAIzJ,IAAI,CAACoE,IAAI,CAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNjG,OAAA;kBAAK0F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3F,OAAA;oBACI0F,SAAS,EAAC,mBAAmB;oBAC7BE,KAAK,EAAE;sBAAEiB,KAAK,EAAE5F,aAAa,CAACE;oBAAc;kBAAE;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJb,UAAU,CAAC3E,IAAI,CAACyJ,UAAU,IAAIzJ,IAAI,CAACoE,IAAI,CAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNjG,OAAA;kBAAK0F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3F,OAAA;oBACI0F,SAAS,EAAC,iBAAiB;oBAC3BE,KAAK,EAAE;sBAAEiB,KAAK,EAAE5F,aAAa,CAACE;oBAAc;kBAAE;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,EACJxF,IAAI,CAAC0J,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNjG,OAAA;gBAAK0F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtC3F,OAAA;kBACI0F,SAAS,EAAC,mDAAmD;kBAC7D0E,uBAAuB,EAAE;oBACrBC,MAAM,EAAE5J,IAAI,CAAC6J,OAAO,CAACjG,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNjG,OAAA;gBAAK0F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/B3F,OAAA;kBAAI0F,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EjG,OAAA;kBAAK0F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3F,OAAA;oBACI0F,SAAS,EAAC,mDAAmD;oBAC7DE,KAAK,EAAE;sBAAEO,eAAe,EAAElF,aAAa,CAACE;oBAAc,CAAE;oBACxDiF,YAAY,EAAG1D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACG,eAAgB;oBACpFkF,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACE,aAAc;oBAAAwE,QAAA,gBAElF3F,OAAA;sBAAG0F,SAAS,EAAC;oBAAwB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTjG,OAAA;oBACI0F,SAAS,EAAC,mDAAmD;oBAC7DE,KAAK,EAAE;sBAAEO,eAAe,EAAElF,aAAa,CAACI;oBAAa,CAAE;oBACvD+E,YAAY,EAAG1D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACE,aAAc;oBAClFmF,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC2D,MAAM,CAACT,KAAK,CAACO,eAAe,GAAGlF,aAAa,CAACI,YAAa;oBAAAsE,QAAA,gBAEjF3F,OAAA;sBAAG0F,SAAS,EAAC;oBAAqB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTjG,OAAA;oBAAQ0F,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjG3F,OAAA;sBAAG0F,SAAS,EAAC;oBAAsB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNjG,OAAA,CAACf,GAAG;UAAA0G,QAAA,GAEChF,WAAW,CAAC4J,MAAM,GAAG,CAAC,iBACnBvK,OAAA;YAAK0F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnD3F,OAAA;cAAI0F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpD3F,OAAA;gBACI0F,SAAS,EAAC,uBAAuB;gBACjCE,KAAK,EAAE;kBAAEiB,KAAK,EAAE5F,aAAa,CAACE;gBAAc;cAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,kBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjG,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrBhF,WAAW,CAAC6J,GAAG,CAAE3G,IAAI,iBAClB7D,OAAA;gBAEIkG,OAAO,EAAEA,CAAA,KAAMV,sBAAsB,CAAC3B,IAAI,CAAC1D,EAAE,CAAE;gBAC/CuF,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjF3F,OAAA;kBACIwH,GAAG,EAAExD,WAAW,CAACH,IAAI,CAACmG,KAAK,CAAE;kBAC7BvC,GAAG,EAAE5D,IAAI,CAACpC,KAAM;kBAChBiE,SAAS,EAAC,sDAAsD;kBAChEkC,OAAO,EAAGlF,CAAC,IAAK;oBACZA,CAAC,CAAC2D,MAAM,CAACmB,GAAG,GAAG,wCAAwC;kBAC3D;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFjG,OAAA;kBAAK0F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3F,OAAA;oBAAI0F,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9D9B,IAAI,CAACpC;kBAAK;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLjG,OAAA;oBAAG0F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACd,IAAI,CAACqG,UAAU,IAAIrG,IAAI,CAACgB,IAAI;kBAAC;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBDpC,IAAI,CAAC1D,EAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGDjG,OAAA,CAACf,GAAG;YAACsH,EAAE,EAAE;cAAEE,OAAO,EAAE,OAAO;cAAEgE,YAAY,EAAE,CAAC;cAAEvB,SAAS,EAAE,CAAC;cAAEX,CAAC,EAAE;YAAE,CAAE;YAAA5C,QAAA,eAC/D3F,OAAA,CAACf,GAAG;cACAuK,SAAS,EAAC,QAAQ;cAClBtD,OAAO,EAAET,gBAAiB;cAC1Bc,EAAE,EAAE;gBACAG,KAAK,EAAE,MAAM;gBACbD,OAAO,EAAE,cAAc;gBACvBI,KAAK,EAAE,OAAO;gBACdyC,EAAE,EAAE,GAAG;gBACPlC,EAAE,EAAE,CAAC;gBACLqD,YAAY,EAAE,CAAC;gBACff,MAAM,EAAE,MAAM;gBACdE,MAAM,EAAE,SAAS;gBACjB9B,UAAU,EAAE,GAAG;gBACf2B,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACPhD,OAAO,EAAE;gBACb;cACJ,CAAE;cAAAd,QAAA,gBAEF3F,OAAA;gBAAG0F,SAAS,EAAC,mBAAmB;gBAACE,KAAK,EAAE;kBAAEqD,WAAW,EAAE;gBAAM;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAExE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjG,OAAA,CAACf,GAAG;MAACsH,EAAE,EAAE;QACLK,QAAQ,EAAE,OAAO;QACjB8D,IAAI,EAAE,CAAC;QACPjC,KAAK,EAAE,CAAC;QACRkC,MAAM,EAAE,CAAC;QACT1D,MAAM,EAAE,IAAI;QACZI,OAAO,EAAE;UAAEH,EAAE,EAAE,OAAO;UAAEC,EAAE,EAAE;QAAO,CAAC;QAAE;QACtChB,eAAe,EAAE,OAAO;QACxByE,SAAS,EAAE,mBAAmB;QAC9B1B,SAAS,EAAE;MACf,CAAE;MAAAvD,QAAA,eACE3F,OAAA,CAACf,GAAG;QAACsH,EAAE,EAAE;UACLc,OAAO,EAAE,MAAM;UACf2B,cAAc,EAAE,cAAc;UAC9B1B,UAAU,EAAE,QAAQ;UACpBI,MAAM,EAAE,EAAE;UACVN,EAAE,EAAE;QACR,CAAE;QAAAzB,QAAA,gBACE3F,OAAA,CAACf,GAAG;UACAiH,OAAO,EAAEA,CAAA,KAAM9F,QAAQ,CAAC,GAAG,CAAE;UAC7BsF,SAAS,EAAC,wBAAwB;UAClCE,KAAK,EAAE;YAAEgE,MAAM,EAAE;UAAU,CAAE;UAAAjE,QAAA,gBAE7B3F,OAAA;YAAG0F,SAAS,EAAC;UAA2C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAe,CAAE;YAAAlB,QAAA,EAAC;UAE1F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENjG,OAAA,CAACf,GAAG;UACAiH,OAAO,EAAEA,CAAA,KAAM9F,QAAQ,CAAC,GAAG,CAAE;UAC7BsF,SAAS,EAAC,iBAAiB;UAC3BE,KAAK,EAAE;YAAEgE,MAAM,EAAE;UAAU,CAAE;UAAAjE,QAAA,gBAE7B3F,OAAA;YAAG0F,SAAS,EAAC;UAA6C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAlB,QAAA,EAAC;UAE5F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENjG,OAAA,CAACf,GAAG;UACAiH,OAAO,EAAEA,CAAA,KAAM9F,QAAQ,CAAC,QAAQ,CAAE;UAClCsF,SAAS,EAAC,iBAAiB;UAC3BE,KAAK,EAAE;YAAEgE,MAAM,EAAE;UAAU,CAAE;UAAAjE,QAAA,gBAE7B3F,OAAA;YAAG0F,SAAS,EAAC;UAA+C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEjG,OAAA,CAACd,UAAU;YAAC2I,OAAO,EAAC,SAAS;YAACnC,SAAS,EAAC,kBAAkB;YAACa,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAlB,QAAA,EAAC;UAE5F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC/F,EAAA,CA1tBID,QAAQ;EAAA,QAEKlB,SAAS,EACPC,WAAW,EACdW,QAAQ,EACJD,aAAa;AAAA;AAAAmL,EAAA,GAL7B5K,QAAQ;AA4tBd,eAAeA,QAAQ;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}