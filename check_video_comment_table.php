<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    // Check if video_comment table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'video_comment'");
    if ($stmt->rowCount() > 0) {
        echo "video_comment table exists\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE video_comment");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $col) {
            echo $col['Field'] . " - " . $col['Type'] . "\n";
        }
        
        // Count comments
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM video_comment");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "\nTotal comments: " . $count['count'] . "\n";
        
    } else {
        echo "video_comment table does not exist\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
