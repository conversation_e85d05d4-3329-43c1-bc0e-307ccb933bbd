<?php
$newsId = $_GET['id'] ?? 0;
if (!$newsId) {
    header('Location: ?page=news');
    exit;
}
?>

<!-- Edit News Form -->
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Edit Berita</h1>
                <p class="text-gray-600 mt-1" id="news-breadcrumb">Memuat data berita...</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="?page=news" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Kembali
                </a>
                <button onclick="updateNews()" id="update-btn" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Update Berita
                </button>
            </div>
        </div>
    </div>

    <form id="edit-news-form" class="space-y-6" enctype="multipart/form-data">
        <input type="hidden" id="news-id" value="<?php echo htmlspecialchars($newsId); ?>">
        
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Dasar</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Judul Berita <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Masukkan judul berita yang menarik...">
                </div>
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Kategori <span class="text-red-500">*</span>
                    </label>
                    <select id="category" name="category_id" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Pilih Kategori</option>
                    </select>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <select id="status" name="status" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="draft">Draft</option>
                        <option value="published">Published</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Current Image Display -->
        <div id="current-image-section" class="bg-white rounded-xl shadow-sm p-6 hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Gambar Saat Ini</h3>
            <div id="current-image-display" class="mb-4"></div>
        </div>

        <!-- Image Upload -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Gambar Berita</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Upload Gambar Baru</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="image" name="image" accept="image/*" class="hidden" onchange="previewImage(this)">
                        <div id="upload-area" onclick="document.getElementById('image').click()" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload gambar baru</p>
                            <p class="text-sm text-gray-500 mt-1">Format: JPG, PNG, GIF. Max: 5MB</p>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Preview Gambar Baru</label>
                    <div id="image-preview" class="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                        <i class="fas fa-image text-4xl text-gray-400"></i>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <label for="image_alt" class="block text-sm font-medium text-gray-700 mb-2">Alt Text Gambar</label>
                <input type="text" id="image_alt" name="image_alt"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                       placeholder="Deskripsi gambar untuk aksesibilitas...">
            </div>
        </div>

        <!-- Content -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Konten Berita</h3>
            <div class="space-y-4">
                <div>
                    <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">Ringkasan/Excerpt</label>
                    <textarea id="excerpt" name="excerpt" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Ringkasan singkat berita (opsional, akan dibuat otomatis jika kosong)..."></textarea>
                </div>
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        Konten Lengkap <span class="text-red-500">*</span>
                    </label>
                    <textarea id="content" name="content" rows="12" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Tulis konten berita lengkap di sini..."></textarea>
                </div>
            </div>
        </div>

        <!-- SEO & Meta -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">SEO & Meta Information</h3>
            <div class="space-y-4">
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                    <input type="text" id="meta_title" name="meta_title"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Judul untuk SEO (opsional, akan menggunakan judul berita jika kosong)...">
                </div>
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <textarea id="meta_description" name="meta_description" rows="2"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Deskripsi untuk mesin pencari..."></textarea>
                </div>
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input type="text" id="tags" name="tags"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Tag1, Tag2, Tag3... (pisahkan dengan koma)">
                </div>
            </div>
        </div>

        <!-- Additional Options -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Opsi Tambahan</h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="featured" name="featured" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-sm text-gray-700">Berita Unggulan</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Tampilkan di halaman utama sebagai berita unggulan</p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex flex-col sm:flex-row gap-3 justify-end">
                <a href="?page=news" class="px-6 py-3 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200 text-center">
                    <i class="fas fa-times mr-2"></i>
                    Batal
                </a>
                <button type="button" onclick="updateAsDraft()" class="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Simpan sebagai Draft
                </button>
                <button type="button" onclick="updateAndPublish()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Update & Publish
                </button>
            </div>
        </div>
    </form>
</div>

<script>
const newsId = <?php echo json_encode($newsId); ?>;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadNewsData();
});

// Load categories
async function loadCategories() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_categories`);
        const select = document.getElementById('category');
        
        if (data.success && data.data) {
            data.data.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        showNotification('Error memuat kategori', 'error');
    }
}

// Load news data
async function loadNewsData() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_news_by_id&id=${newsId}`);
        
        if (data.success && data.data) {
            const news = data.data;
            console.log('News data loaded:', news);
            console.log('Image path from database:', news.image);

            // Update breadcrumb
            document.getElementById('news-breadcrumb').textContent = `Edit: ${news.title}`;
            
            // Fill form fields
            document.getElementById('title').value = news.title || '';
            document.getElementById('category').value = news.category_id || '';
            document.getElementById('status').value = news.status || 'draft';
            document.getElementById('image_alt').value = news.image_alt || '';
            document.getElementById('excerpt').value = news.excerpt || '';
            document.getElementById('content').value = news.content || '';
            document.getElementById('meta_title').value = news.meta_title || '';
            document.getElementById('meta_description').value = news.meta_description || '';
            document.getElementById('tags').value = news.tags || '';
            document.getElementById('featured').checked = news.featured || false;
            
            // Show current image if exists
            if (news.image) {
                const currentImageSection = document.getElementById('current-image-section');
                const currentImageDisplay = document.getElementById('current-image-display');
                
                currentImageSection.classList.remove('hidden');
                const imageUrl = getCorrectImageUrl(news.image, news.image_base64);
                console.log('Original image path:', news.image);
                console.log('Processed image URL:', imageUrl);

                currentImageDisplay.innerHTML = `
                    <div class="flex items-center space-x-4">
                        <div class="w-32 h-32 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            ${imageUrl ?
                                `<img src="${imageUrl}" alt="${news.title}" class="w-full h-full object-cover" onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full flex items-center justify-center text-gray-400\\'><i class=\\'fas fa-image\\'></i><br><small>Gambar tidak dapat dimuat</small></div>'">` :
                                `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                    <div class="text-center">
                                        <i class="fas fa-image text-2xl"></i>
                                        <br><small>Tidak ada gambar</small>
                                    </div>
                                </div>`
                            }
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Gambar saat ini</p>
                            <p class="text-xs text-gray-500 mt-1">Upload gambar baru untuk mengganti</p>
                        </div>
                    </div>
                `;
            }
        } else {
            showNotification('Berita tidak ditemukan', 'error');
            setTimeout(() => {
                window.location.href = '?page=news';
            }, 2000);
        }
    } catch (error) {
        console.error('Error loading news data:', error);
        showNotification('Error memuat data berita', 'error');
    }
}

// Image preview
function previewImage(input) {
    const preview = document.getElementById('image-preview');
    
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showNotification('Ukuran file terlalu besar. Maksimal 5MB', 'error');
            input.value = '';
            preview.innerHTML = '<i class="fas fa-image text-4xl text-gray-400"></i>';
            return;
        }
        
        // Validate file type - Support all common image formats
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'image/bmp', 'image/tiff', 'image/tif', 'image/svg+xml', 'image/ico',
            'image/x-icon', 'image/vnd.microsoft.icon'
        ];

        // Also check file extension as backup validation
        const fileName = file.name.toLowerCase();
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif', '.svg', '.ico'];
        const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

        if (!allowedTypes.includes(file.type) && !hasValidExtension) {
            showNotification('Format file tidak didukung. Gunakan JPG, PNG, GIF, WebP, BMP, TIFF, SVG, atau ICO', 'error');
            input.value = '';
            preview.innerHTML = '<i class="fas fa-image text-4xl text-gray-400"></i>';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover rounded-lg">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '<i class="fas fa-image text-4xl text-gray-400"></i>';
    }
}

// Update functions
function updateNews() {
    const status = document.getElementById('status').value;
    submitForm(status);
}

function updateAsDraft() {
    document.getElementById('status').value = 'draft';
    submitForm('draft');
}

function updateAndPublish() {
    document.getElementById('status').value = 'published';
    submitForm('published');
}

// Submit form
async function submitForm(status) {
    const form = document.getElementById('edit-news-form');
    const formData = new FormData();
    
    // Get form values
    const title = document.getElementById('title').value.trim();
    const category = document.getElementById('category').value;
    const content = document.getElementById('content').value.trim();
    const excerpt = document.getElementById('excerpt').value.trim();
    const imageAlt = document.getElementById('image_alt').value.trim();
    const metaTitle = document.getElementById('meta_title').value.trim();
    const metaDescription = document.getElementById('meta_description').value.trim();
    const tags = document.getElementById('tags').value.trim();
    const featured = document.getElementById('featured').checked;
    const imageFile = document.getElementById('image').files[0];
    
    // Validation
    if (!title) {
        showNotification('Judul berita harus diisi', 'error');
        document.getElementById('title').focus();
        return;
    }
    
    if (!category) {
        showNotification('Kategori harus dipilih', 'error');
        document.getElementById('category').focus();
        return;
    }
    
    if (!content) {
        showNotification('Konten berita harus diisi', 'error');
        document.getElementById('content').focus();
        return;
    }
    
    // Prepare form data
    formData.append('action', 'update_news');
    formData.append('id', newsId);
    formData.append('title', title);
    formData.append('category_id', category);
    formData.append('status', status);
    formData.append('content', content);
    
    // Optional fields
    if (excerpt) formData.append('excerpt', excerpt);
    if (imageAlt) formData.append('image_alt', imageAlt);
    if (metaTitle) formData.append('meta_title', metaTitle);
    if (metaDescription) formData.append('meta_description', metaDescription);
    if (tags) formData.append('tags', tags);
    formData.append('featured', featured ? '1' : '0');
    
    // Add image if selected
    if (imageFile) {
        formData.append('image', imageFile);
    }
    
    // Show loading state
    const updateBtn = document.getElementById('update-btn');
    const originalText = updateBtn.innerHTML;
    updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mengupdate...';
    updateBtn.disabled = true;
    
    // Disable all buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(btn => btn.disabled = true);
    
    try {
        const result = await safeFetch(API_BASE, {
            method: 'POST',
            body: formData
        });
        
        if (result.success) {
            showNotification('Berita berhasil diupdate', 'success');
            setTimeout(() => {
                window.location.href = '?page=news';
            }, 1500);
        } else {
            showNotification(result.message || 'Gagal mengupdate berita', 'error');
        }
    } catch (error) {
        console.error('Error updating news:', error);
        showNotification('Error mengupdate berita: ' + error.message, 'error');
    } finally {
        // Restore button states
        updateBtn.innerHTML = originalText;
        buttons.forEach(btn => btn.disabled = false);
    }
}

// Helper function to get correct image URL - Handle both file path and base64
function getCorrectImageUrl(imagePath, imageBase64 = '') {
    console.log('getCorrectImageUrl called with:', imagePath);
    console.log('Has base64 data:', !!imageBase64);

    // If no image data at all
    if (!imagePath && !imageBase64) {
        console.log('No image data provided');
        return null;
    }

    // If it's already a data URL (base64), return as is
    if (imagePath && imagePath.startsWith('data:')) {
        console.log('Using data URL:', imagePath.substring(0, 50) + '...');
        return imagePath;
    }

    // If it's already a full HTTP URL, return as is
    if (imagePath && imagePath.startsWith('http')) {
        console.log('Using full URL:', imagePath);
        return imagePath;
    }

    // If we have a file path, try to use it
    if (imagePath) {
        let filename = '';

        if (imagePath.startsWith('/react-news/uploads/')) {
            filename = imagePath.replace('/react-news/uploads/', '');
        } else if (imagePath.startsWith('/uploads/')) {
            filename = imagePath.replace('/uploads/', '');
        } else if (imagePath.startsWith('/react-news/frontend/uploads/')) {
            filename = imagePath.replace('/react-news/frontend/uploads/', '');
        } else if (imagePath.startsWith('assets/news/')) {
            filename = imagePath.replace('assets/news/', '');
        } else if (!imagePath.includes('/')) {
            // Just filename
            filename = imagePath;
        } else {
            // Extract filename from any other path
            filename = imagePath.split('/').pop();
        }

        const url = `http://localhost/react-news/frontend/src/pages/admin/uploads/${filename}`;
        console.log('Using file URL:', url);
        return url;
    }

    // If no file path but we have base64, create data URL
    if (imageBase64) {
        const dataUrl = `data:image/jpeg;base64,${imageBase64}`;
        console.log('Using base64 data URL');
        return dataUrl;
    }

    return null;
}

// Helper function to get correct image URL - Same logic as getCorrectImageUrl
function getImageUrl(imagePath, imageBase64 = '') {
    return getCorrectImageUrl(imagePath, imageBase64);
}

</script>
