{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\components\\\\Saved.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Box, Typography, Card, CardContent, CardMedia, IconButton, Chip, CircularProgress, AppBar, Toolbar, Avatar, Button, Drawer, Divider, Stack } from '@mui/material';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport ShareIcon from '@mui/icons-material/Share';\nimport HomeIcon from '@mui/icons-material/Home';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport CloseIcon from '@mui/icons-material/Close';\nimport { useNavigate } from 'react-router-dom';\n\n// Helper function to get correct image URL from database\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getImageUrl = imagePath => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/300x200/?news';\n  }\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    return imagePath;\n  }\n\n  // Extract filename from any path format stored in database\n  let filename = '';\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    filename = imagePath.replace('/react-news/uploads/', '');\n  } else if (imagePath.startsWith('/uploads/')) {\n    filename = imagePath.replace('/uploads/', '');\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\n  } else if (imagePath.startsWith('admin/uploads/')) {\n    filename = imagePath.replace('admin/uploads/', '');\n  } else if (imagePath.startsWith('uploads/')) {\n    filename = imagePath.replace('uploads/', '');\n  } else if (imagePath.startsWith('assets/news/')) {\n    filename = imagePath.replace('assets/news/', '');\n  } else if (!imagePath.includes('/')) {\n    // Just filename from database\n    filename = imagePath;\n  } else {\n    // Extract filename from any other path\n    filename = imagePath.split('/').pop();\n  }\n\n  // Use root/uploads path for all images (matching database posts table)\n  return `http://localhost/react-news/uploads/${filename}`;\n};\nconst Saved = () => {\n  _s();\n  const [savedNews, setSavedNews] = useState([]);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: ''\n  });\n  const [bottomValue, setBottomValue] = useState(2); // 2 = Simpan\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Load website settings from database (same as LandingPage)\n  useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan').then(res => res.json()).then(response => {\n      if (response.success && response.data) {\n        const data = response.data;\n        console.log('Website settings loaded:', data);\n\n        // Logo is now stored as base64 data in database\n        let logoPath = data.logo_file_path && data.logo_file_path.startsWith('data:') ? data.logo_file_path : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';\n        setKostum({\n          logo: logoPath,\n          title: data.nama_website || 'React News Portal',\n          description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'\n        });\n\n        // Update document title\n        document.title = `Berita Tersimpan - ${data.nama_website || 'React News Portal'}`;\n      } else {\n        throw new Error('Invalid API response');\n      }\n    }).catch(err => {\n      console.log('Database settings not available, using defaults:', err);\n      setKostum({\n        logo: '/logo192.png',\n        title: 'React News Portal',\n        description: 'Portal berita terkini dan terpercaya'\n      });\n      document.title = 'Berita Tersimpan - React News Portal';\n    });\n  }, []);\n\n  // Load saved news from database\n  useEffect(() => {\n    setLoading(true);\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news').then(res => {\n      if (!res.ok) {\n        throw new Error(`HTTP error! status: ${res.status}`);\n      }\n      return res.text(); // Get as text first to debug\n    }).then(text => {\n      console.log('Raw response:', text);\n      try {\n        return JSON.parse(text);\n      } catch (e) {\n        console.error('JSON parse error:', e);\n        console.error('Response text:', text);\n        throw new Error('Invalid JSON response');\n      }\n    }).then(data => {\n      console.log('Saved news loaded:', data);\n      if (data.success && Array.isArray(data.data)) {\n        // Map the data to match expected format\n        const mappedData = data.data.map(item => {\n          console.log('📸 Image data from database:', {\n            id: item.id,\n            title: item.title,\n            originalImage: item.image,\n            processedImage: getImageUrl(item.image)\n          });\n          return {\n            id: item.id,\n            title: item.title,\n            description: item.description || item.content,\n            content: item.content,\n            image: item.image,\n            // Raw image path from database posts table\n            category: item.category_name || 'Umum',\n            date: item.created_at || item.date,\n            author: item.author_name || 'Admin',\n            slug: item.slug,\n            views: item.views || 0,\n            likes: item.likes || 0,\n            share: item.share || 0\n          };\n        });\n        setSavedNews(mappedData);\n      } else {\n        setSavedNews([]);\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error('Error loading saved news:', err);\n      setSavedNews([]);\n      setLoading(false);\n    });\n  }, []);\n  const handleBookmark = async news => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: 'remove_saved_news',\n          post_id: news.id\n        })\n      });\n      const result = await response.json();\n      if (result.success) {\n        setSavedNews(prev => prev.filter(n => n.id !== news.id));\n        window.alert('Berita dihapus dari Simpan');\n      } else {\n        window.alert('Gagal menghapus berita dari simpan: ' + result.message);\n      }\n    } catch (error) {\n      console.error('Error removing bookmark:', error);\n      window.alert('Gagal menghapus berita dari simpan');\n    }\n  };\n  const handleNewsClick = async newsId => {\n    // Increment views when clicking news card\n    try {\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleShare = async news => {\n    try {\n      // Update share count in database\n      fetch(`http://localhost:5000/api/posts/${news.id}/share`, {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title.toLowerCase().replace(/[^a-z0-9\\s-]/g, '').replace(/\\s+/g, '-').replace(/-+/g, '-').trim();\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n      window.alert('Link berita berhasil disalin ke clipboard!');\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      window.alert('Gagal menyalin link berita');\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'blue.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 80,\n          px: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 28\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => setSidebarOpen(true),\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: () => setSidebarOpen(false),\n      sx: {\n        zIndex: 1400\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 300,\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setSidebarOpen(false),\n          sx: {\n            position: 'absolute',\n            right: 8,\n            top: 8\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 26\n            }, this),\n            onClick: () => {\n              navigate('/');\n              setSidebarOpen(false);\n            },\n            children: \"Beranda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 26\n            }, this),\n            children: \"Berita Tersimpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(AdminPanelSettingsIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 26\n            }, this),\n            onClick: () => {\n              window.open('/dashboard', '_blank');\n              setSidebarOpen(false);\n            },\n            children: \"Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 12,\n        px: 3,\n        pb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: '100%',\n          background: '#e1f5fe',\n          color: '#007bff',\n          textAlign: 'center',\n          fontWeight: 600,\n          fontSize: 16,\n          padding: '12px 0',\n          marginBottom: 3,\n          borderRadius: 2,\n          borderBottom: '1.5px solid #b3e5fc'\n        },\n        children: [\"Total Berita Disimpan: \", savedNews.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: 2,\n          paddingBottom: 10\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: 200\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this) : savedNews.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            textAlign: 'center',\n            color: 'grey.600',\n            padding: 5\n          },\n          children: \"Belum ada berita yang disimpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this) : savedNews.map((news, index) => {\n          var _news$description;\n          return /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              marginBottom: 2,\n              boxShadow: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: {\n                  xs: 'column',\n                  sm: 'row'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                onClick: () => handleNewsClick(news.id),\n                sx: {\n                  width: {\n                    xs: '100%',\n                    sm: 200\n                  },\n                  height: {\n                    xs: 200,\n                    sm: 150\n                  },\n                  objectFit: 'cover',\n                  cursor: 'pointer'\n                },\n                image: getImageUrl(news.image),\n                alt: news.title,\n                onError: e => {\n                  e.target.src = 'https://source.unsplash.com/300x200/?news';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                onClick: () => handleNewsClick(news.id),\n                sx: {\n                  flex: 1,\n                  padding: 2,\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h3\",\n                  sx: {\n                    marginBottom: 1,\n                    fontWeight: 600\n                  },\n                  children: news.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    marginBottom: 1,\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: news.category,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: 'grey.600',\n                      alignSelf: 'center'\n                    },\n                    children: [formatDate(news.date), \" \\u2022 \", news.views || 0, \" views\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'grey.700',\n                    marginBottom: 2\n                  },\n                  children: [(_news$description = news.description) === null || _news$description === void 0 ? void 0 : _news$description.substring(0, 150), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: e => {\n                      e.stopPropagation(); // Prevent card click\n                      handleBookmark(news);\n                    },\n                    color: \"primary\",\n                    title: \"Hapus dari simpan\",\n                    children: /*#__PURE__*/_jsxDEV(BookmarkAddedIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: e => {\n                      e.stopPropagation(); // Prevent card click\n                      handleShare(news);\n                    },\n                    color: \"primary\",\n                    title: \"Bagikan\",\n                    children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)\n          }, `saved-${news.id}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: 1200,\n          display: 'block',\n          backgroundColor: 'white',\n          borderTop: '1px solid #e0e0e0',\n          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-around',\n            alignItems: 'center',\n            height: 64,\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => window.location.href = '/',\n            className: `bottom-nav-item ${bottomValue === 0 ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `fas fa-home bottom-nav-icon ${bottomValue === 0 ? 'text-blue-600' : 'text-gray-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: bottomValue === 0 ? 'primary.main' : 'text.secondary'\n              },\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            onClick: () => setBottomValue(1),\n            className: `bottom-nav-item ${bottomValue === 1 ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `fas fa-search bottom-nav-icon ${bottomValue === 1 ? 'text-blue-600' : 'text-gray-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: bottomValue === 1 ? 'primary.main' : 'text.secondary'\n              },\n              children: \"Cari\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"bottom-nav-item active\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bookmark bottom-nav-icon text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: \"bottom-nav-label\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 600\n              },\n              children: \"Simpan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(Saved, \"EE6fL8Wc54qpjz0I/lDu7mow5wY=\", false, function () {\n  return [useNavigate];\n});\n_c = Saved;\nexport default Saved;\nvar _c;\n$RefreshReg$(_c, \"Saved\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "IconButton", "Chip", "CircularProgress", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "<PERSON><PERSON>", "Drawer", "Divider", "<PERSON><PERSON>", "BookmarkAddedIcon", "ShareIcon", "HomeIcon", "BookmarkIcon", "AdminPanelSettingsIcon", "MenuIcon", "CloseIcon", "useNavigate", "jsxDEV", "_jsxDEV", "getImageUrl", "imagePath", "startsWith", "filename", "replace", "includes", "split", "pop", "Saved", "_s", "savedNews", "setSavedNews", "kostum", "setKostum", "logo", "title", "bottomValue", "setBottomValue", "sidebarOpen", "setSidebarOpen", "loading", "setLoading", "fetch", "then", "res", "json", "response", "success", "data", "console", "log", "logoPath", "logo_file_path", "nama_website", "description", "deskripsi_website", "document", "Error", "catch", "err", "ok", "status", "text", "JSON", "parse", "e", "error", "Array", "isArray", "mappedData", "map", "item", "id", "originalImage", "image", "processedImage", "content", "category", "category_name", "date", "created_at", "author", "author_name", "slug", "views", "likes", "share", "handleBookmark", "news", "method", "headers", "body", "stringify", "action", "post_id", "result", "prev", "filter", "n", "window", "alert", "message", "handleNewsClick", "newsId", "navigate", "handleShare", "urlTitle", "toLowerCase", "trim", "link", "location", "origin", "navigator", "clipboard", "writeText", "formatDate", "dateString", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "sx", "minHeight", "bgcolor", "width", "overflow", "children", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "fontSize", "edge", "onClick", "anchor", "open", "onClose", "p", "right", "top", "mb", "spacing", "fullWidth", "textTransform", "startIcon", "mt", "pb", "background", "textAlign", "padding", "marginBottom", "borderRadius", "length", "paddingBottom", "justifyContent", "index", "_news$description", "boxShadow", "flexDirection", "xs", "sm", "component", "objectFit", "cursor", "flex", "gap", "flexWrap", "label", "size", "alignSelf", "substring", "stopPropagation", "left", "bottom", "backgroundColor", "borderTop", "href", "className", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/components/Saved.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport {\r\n  Box, Typography, Card, CardContent, CardMedia, IconButton, Chip, CircularProgress,\r\n  AppBar, Toolbar, Avatar, But<PERSON>, Drawer, Divider, Stack\r\n} from '@mui/material';\r\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\r\nimport ShareIcon from '@mui/icons-material/Share';\r\nimport HomeIcon from '@mui/icons-material/Home';\r\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\r\nimport AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';\r\nimport MenuIcon from '@mui/icons-material/Menu';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\n// Helper function to get correct image URL from database\r\nconst getImageUrl = (imagePath) => {\r\n  if (!imagePath) {\r\n    return 'https://source.unsplash.com/300x200/?news';\r\n  }\r\n\r\n  // If it's already a full URL, return as is\r\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\r\n    return imagePath;\r\n  }\r\n\r\n  // Extract filename from any path format stored in database\r\n  let filename = '';\r\n\r\n  if (imagePath.startsWith('/react-news/uploads/')) {\r\n    filename = imagePath.replace('/react-news/uploads/', '');\r\n  } else if (imagePath.startsWith('/uploads/')) {\r\n    filename = imagePath.replace('/uploads/', '');\r\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\r\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\r\n  } else if (imagePath.startsWith('admin/uploads/')) {\r\n    filename = imagePath.replace('admin/uploads/', '');\r\n  } else if (imagePath.startsWith('uploads/')) {\r\n    filename = imagePath.replace('uploads/', '');\r\n  } else if (imagePath.startsWith('assets/news/')) {\r\n    filename = imagePath.replace('assets/news/', '');\r\n  } else if (!imagePath.includes('/')) {\r\n    // Just filename from database\r\n    filename = imagePath;\r\n  } else {\r\n    // Extract filename from any other path\r\n    filename = imagePath.split('/').pop();\r\n  }\r\n\r\n  // Use root/uploads path for all images (matching database posts table)\r\n  return `http://localhost/react-news/uploads/${filename}`;\r\n};\r\n\r\nconst Saved = () => {\r\n  const [savedNews, setSavedNews] = useState([]);\r\n  const [kostum, setKostum] = useState({ logo: '', title: '' });\r\n  const [bottomValue, setBottomValue] = useState(2); // 2 = Simpan\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Load website settings from database (same as LandingPage)\r\n  useEffect(() => {\r\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_pengaturan')\r\n      .then(res => res.json())\r\n      .then(response => {\r\n        if (response.success && response.data) {\r\n          const data = response.data;\r\n          console.log('Website settings loaded:', data);\r\n\r\n          // Logo is now stored as base64 data in database\r\n          let logoPath = data.logo_file_path && data.logo_file_path.startsWith('data:')\r\n            ? data.logo_file_path\r\n            : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzNCODJGNiIvPgogICAgPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+Uk48L3RleHQ+Cjwvc3ZnPg==';\r\n\r\n          setKostum({\r\n            logo: logoPath,\r\n            title: data.nama_website || 'React News Portal',\r\n            description: data.deskripsi_website || 'Portal berita terkini dan terpercaya'\r\n          });\r\n\r\n          // Update document title\r\n          document.title = `Berita Tersimpan - ${data.nama_website || 'React News Portal'}`;\r\n        } else {\r\n          throw new Error('Invalid API response');\r\n        }\r\n      })\r\n      .catch(err => {\r\n        console.log('Database settings not available, using defaults:', err);\r\n        setKostum({\r\n          logo: '/logo192.png',\r\n          title: 'React News Portal',\r\n          description: 'Portal berita terkini dan terpercaya'\r\n        });\r\n        document.title = 'Berita Tersimpan - React News Portal';\r\n      });\r\n  }, []);\r\n\r\n  // Load saved news from database\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=get_saved_news')\r\n      .then(res => {\r\n        if (!res.ok) {\r\n          throw new Error(`HTTP error! status: ${res.status}`);\r\n        }\r\n        return res.text(); // Get as text first to debug\r\n      })\r\n      .then(text => {\r\n        console.log('Raw response:', text);\r\n        try {\r\n          return JSON.parse(text);\r\n        } catch (e) {\r\n          console.error('JSON parse error:', e);\r\n          console.error('Response text:', text);\r\n          throw new Error('Invalid JSON response');\r\n        }\r\n      })\r\n      .then(data => {\r\n        console.log('Saved news loaded:', data);\r\n        if (data.success && Array.isArray(data.data)) {\r\n          // Map the data to match expected format\r\n          const mappedData = data.data.map(item => {\r\n            console.log('📸 Image data from database:', {\r\n              id: item.id,\r\n              title: item.title,\r\n              originalImage: item.image,\r\n              processedImage: getImageUrl(item.image)\r\n            });\r\n\r\n            return {\r\n              id: item.id,\r\n              title: item.title,\r\n              description: item.description || item.content,\r\n              content: item.content,\r\n              image: item.image, // Raw image path from database posts table\r\n              category: item.category_name || 'Umum',\r\n              date: item.created_at || item.date,\r\n              author: item.author_name || 'Admin',\r\n              slug: item.slug,\r\n              views: item.views || 0,\r\n              likes: item.likes || 0,\r\n              share: item.share || 0\r\n            };\r\n          });\r\n          setSavedNews(mappedData);\r\n        } else {\r\n          setSavedNews([]);\r\n        }\r\n        setLoading(false);\r\n      })\r\n      .catch(err => {\r\n        console.error('Error loading saved news:', err);\r\n        setSavedNews([]);\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  const handleBookmark = async (news) => {\r\n    try {\r\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          action: 'remove_saved_news',\r\n          post_id: news.id\r\n        })\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        setSavedNews(prev => prev.filter(n => n.id !== news.id));\r\n        window.alert('Berita dihapus dari Simpan');\r\n      } else {\r\n        window.alert('Gagal menghapus berita dari simpan: ' + result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing bookmark:', error);\r\n      window.alert('Gagal menghapus berita dari simpan');\r\n    }\r\n  };\r\n\r\n  const handleNewsClick = async (newsId) => {\r\n    // Increment views when clicking news card\r\n    try {\r\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\r\n        method: 'POST'\r\n      });\r\n    } catch (error) {\r\n      console.log('Could not increment views:', error);\r\n    }\r\n\r\n    // Navigate to news detail\r\n    navigate(`/data-news/${newsId}`);\r\n  };\r\n\r\n  const handleShare = async (news) => {\r\n    try {\r\n      // Update share count in database\r\n      fetch(`http://localhost:5000/api/posts/${news.id}/share`, {\r\n        method: 'POST'\r\n      }).catch(err => console.error('Error updating share count:', err));\r\n\r\n      // Generate URL-friendly title for the link\r\n      const urlTitle = news.title\r\n        .toLowerCase()\r\n        .replace(/[^a-z0-9\\s-]/g, '')\r\n        .replace(/\\s+/g, '-')\r\n        .replace(/-+/g, '-')\r\n        .trim();\r\n\r\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\r\n\r\n      // Copy to clipboard\r\n      await navigator.clipboard.writeText(link);\r\n      window.alert('Link berita berhasil disalin ke clipboard!');\r\n\r\n    } catch (error) {\r\n      console.error('Failed to copy link:', error);\r\n      window.alert('Gagal menyalin link berita');\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('id-ID', {\r\n      day: 'numeric',\r\n      month: 'long',\r\n      year: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const navigate = useNavigate();\r\n\r\n  return (\r\n    <Box sx={{ minHeight: '100vh', bgcolor: 'blue.50', width: '100vw', overflow: 'hidden' }}>\r\n      {/* Navbar - Simple with sidebar icon only */}\r\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\r\n        <Toolbar sx={{ minHeight: 80, px: 6 }}>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n            <Avatar\r\n              src={kostum.logo}\r\n              alt=\"Logo\"\r\n              sx={{ width: 48, height: 48, mr: 2 }}\r\n              onError={(e) => { e.target.src = '/logo192.png'; }}\r\n            />\r\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>\r\n              {kostum.title}\r\n            </Typography>\r\n          </Box>\r\n          <IconButton\r\n            edge=\"end\"\r\n            color=\"primary\"\r\n            onClick={() => setSidebarOpen(true)}\r\n            sx={{ mr: 1 }}\r\n          >\r\n            <MenuIcon fontSize=\"large\" />\r\n          </IconButton>\r\n        </Toolbar>\r\n      </AppBar>\r\n\r\n      {/* Sidebar Drawer */}\r\n      <Drawer\r\n        anchor=\"right\"\r\n        open={sidebarOpen}\r\n        onClose={() => setSidebarOpen(false)}\r\n        sx={{ zIndex: 1400 }}\r\n      >\r\n        <Box sx={{ width: 300, p: 3 }}>\r\n          <IconButton\r\n            onClick={() => setSidebarOpen(false)}\r\n            sx={{ position: 'absolute', right: 8, top: 8 }}\r\n            aria-label=\"Tutup\"\r\n          >\r\n            <CloseIcon />\r\n          </IconButton>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\r\n            <Avatar\r\n              src={kostum.logo}\r\n              alt=\"Logo\"\r\n              sx={{ width: 32, height: 32, mr: 1 }}\r\n              onError={(e) => { e.target.src = '/logo192.png'; }}\r\n            />\r\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>\r\n              {kostum.title}\r\n            </Typography>\r\n          </Box>\r\n          <Divider sx={{ mb: 2 }} />\r\n          <Stack spacing={2}>\r\n            <Button\r\n              variant=\"outlined\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<HomeIcon />}\r\n              onClick={() => {\r\n                navigate('/');\r\n                setSidebarOpen(false);\r\n              }}\r\n            >\r\n              Beranda\r\n            </Button>\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<BookmarkIcon />}\r\n            >\r\n              Berita Tersimpan\r\n            </Button>\r\n            <Button\r\n              variant=\"outlined\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{ textTransform: 'none' }}\r\n              startIcon={<AdminPanelSettingsIcon />}\r\n              onClick={() => {\r\n                window.open('/dashboard', '_blank');\r\n                setSidebarOpen(false);\r\n              }}\r\n            >\r\n              Admin Dashboard\r\n            </Button>\r\n          </Stack>\r\n        </Box>\r\n      </Drawer>\r\n      {/* Content Area with proper spacing from navbar */}\r\n      <Box sx={{ mt: 12, px: 3, pb: 3 }}>\r\n        {/* Total Saved Alert/Table */}\r\n        <Box sx={{\r\n          width: '100%',\r\n          background: '#e1f5fe',\r\n          color: '#007bff',\r\n          textAlign: 'center',\r\n          fontWeight: 600,\r\n          fontSize: 16,\r\n          padding: '12px 0',\r\n          marginBottom: 3,\r\n          borderRadius: 2,\r\n          borderBottom: '1.5px solid #b3e5fc'\r\n        }}>\r\n          Total Berita Disimpan: {savedNews.length}\r\n        </Box>\r\n\r\n      <Box sx={{ padding: 2, paddingBottom: 10 }}>\r\n        {loading ? (\r\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>\r\n            <CircularProgress />\r\n          </Box>\r\n        ) : savedNews.length === 0 ? (\r\n          <Typography variant=\"h6\" sx={{ textAlign: 'center', color: 'grey.600', padding: 5 }}>\r\n            Belum ada berita yang disimpan\r\n          </Typography>\r\n        ) : (\r\n          savedNews.map((news, index) => (\r\n            <Card key={`saved-${news.id}-${index}`} sx={{ marginBottom: 2, boxShadow: 2 }}>\r\n              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' } }}>\r\n                {/* Clickable Image Area */}\r\n                <CardMedia\r\n                  component=\"img\"\r\n                  onClick={() => handleNewsClick(news.id)}\r\n                  sx={{\r\n                    width: { xs: '100%', sm: 200 },\r\n                    height: { xs: 200, sm: 150 },\r\n                    objectFit: 'cover',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  image={getImageUrl(news.image)}\r\n                  alt={news.title}\r\n                  onError={(e) => { e.target.src = 'https://source.unsplash.com/300x200/?news'; }}\r\n                />\r\n                {/* Clickable Content Area */}\r\n                <CardContent\r\n                  onClick={() => handleNewsClick(news.id)}\r\n                  sx={{\r\n                    flex: 1,\r\n                    padding: 2,\r\n                    cursor: 'pointer'\r\n                  }}\r\n                >\r\n                  <Typography variant=\"h6\" component=\"h3\" sx={{ marginBottom: 1, fontWeight: 600 }}>\r\n                    {news.title}\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', gap: 1, marginBottom: 1, flexWrap: 'wrap' }}>\r\n                    <Chip\r\n                      label={news.category}\r\n                      size=\"small\"\r\n                      color=\"primary\"\r\n                      variant=\"outlined\"\r\n                    />\r\n                    <Typography variant=\"caption\" sx={{ color: 'grey.600', alignSelf: 'center' }}>\r\n                      {formatDate(news.date)} • {news.views || 0} views\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  <Typography variant=\"body2\" sx={{ color: 'grey.700', marginBottom: 2 }}>\r\n                    {news.description?.substring(0, 150)}...\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', gap: 1 }}>\r\n                    <IconButton\r\n                      onClick={(e) => {\r\n                        e.stopPropagation(); // Prevent card click\r\n                        handleBookmark(news);\r\n                      }}\r\n                      color=\"primary\"\r\n                      title=\"Hapus dari simpan\"\r\n                    >\r\n                      <BookmarkAddedIcon />\r\n                    </IconButton>\r\n                    <IconButton\r\n                      onClick={(e) => {\r\n                        e.stopPropagation(); // Prevent card click\r\n                        handleShare(news);\r\n                      }}\r\n                      color=\"primary\"\r\n                      title=\"Bagikan\"\r\n                    >\r\n                      <ShareIcon />\r\n                    </IconButton>\r\n                  </Box>\r\n                </CardContent>\r\n              </Box>\r\n            </Card>\r\n          ))\r\n        )}\r\n      </Box>\r\n      {/* Custom Bottom Navigation */}\r\n      <Box sx={{\r\n        position: 'fixed',\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        zIndex: 1200,\r\n        display: 'block',\r\n        backgroundColor: 'white',\r\n        borderTop: '1px solid #e0e0e0',\r\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\r\n      }}>\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-around',\r\n          alignItems: 'center',\r\n          height: 64,\r\n          px: 1\r\n        }}>\r\n          <Box\r\n            onClick={() => window.location.href = '/'}\r\n            className={`bottom-nav-item ${bottomValue === 0 ? 'active' : ''}`}\r\n          >\r\n            <i className={`fas fa-home bottom-nav-icon ${bottomValue === 0 ? 'text-blue-600' : 'text-gray-500'}`}></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomValue === 0 ? 'primary.main' : 'text.secondary' }}>\r\n              Home\r\n            </Typography>\r\n          </Box>\r\n\r\n          <Box\r\n            onClick={() => setBottomValue(1)}\r\n            className={`bottom-nav-item ${bottomValue === 1 ? 'active' : ''}`}\r\n          >\r\n            <i className={`fas fa-search bottom-nav-icon ${bottomValue === 1 ? 'text-blue-600' : 'text-gray-500'}`}></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomValue === 1 ? 'primary.main' : 'text.secondary' }}>\r\n              Cari\r\n            </Typography>\r\n          </Box>\r\n\r\n          <Box className=\"bottom-nav-item active\">\r\n            <i className=\"fas fa-bookmark bottom-nav-icon text-blue-600\"></i>\r\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main', fontWeight: 600 }}>\r\n              Simpan\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Saved;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,gBAAgB,EACjFC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,QAClD,eAAe;AACtB,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;EACjC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,2CAA2C;EACpD;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACvE,OAAOD,SAAS;EAClB;;EAEA;EACA,IAAIE,QAAQ,GAAG,EAAE;EAEjB,IAAIF,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;IAChDC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;EAC1D,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;IAC5CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC/C,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,mCAAmC,CAAC,EAAE;IACpEC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;EACvE,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,gBAAgB,CAAC,EAAE;IACjDC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;EACpD,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC3CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC9C,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;IAC/CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAClD,CAAC,MAAM,IAAI,CAACH,SAAS,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACnC;IACAF,QAAQ,GAAGF,SAAS;EACtB,CAAC,MAAM;IACL;IACAE,QAAQ,GAAGF,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;EACvC;;EAEA;EACA,OAAO,uCAAuCJ,QAAQ,EAAE;AAC1D,CAAC;AAED,MAAMK,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC;IAAEwC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC;EAC7D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAD,SAAS,CAAC,MAAM;IACdiD,KAAK,CAAC,2FAA2F,CAAC,CAC/FC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,QAAQ,IAAI;MAChB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC,MAAMA,IAAI,GAAGF,QAAQ,CAACE,IAAI;QAC1BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,IAAI,CAAC;;QAE7C;QACA,IAAIG,QAAQ,GAAGH,IAAI,CAACI,cAAc,IAAIJ,IAAI,CAACI,cAAc,CAAC9B,UAAU,CAAC,OAAO,CAAC,GACzE0B,IAAI,CAACI,cAAc,GACnB,oZAAoZ;QAExZnB,SAAS,CAAC;UACRC,IAAI,EAAEiB,QAAQ;UACdhB,KAAK,EAAEa,IAAI,CAACK,YAAY,IAAI,mBAAmB;UAC/CC,WAAW,EAAEN,IAAI,CAACO,iBAAiB,IAAI;QACzC,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACrB,KAAK,GAAG,sBAAsBa,IAAI,CAACK,YAAY,IAAI,mBAAmB,EAAE;MACnF,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,CAAC,CACDC,KAAK,CAACC,GAAG,IAAI;MACZV,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAES,GAAG,CAAC;MACpE1B,SAAS,CAAC;QACRC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,mBAAmB;QAC1BmB,WAAW,EAAE;MACf,CAAC,CAAC;MACFE,QAAQ,CAACrB,KAAK,GAAG,sCAAsC;IACzD,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1C,SAAS,CAAC,MAAM;IACdgD,UAAU,CAAC,IAAI,CAAC;IAChBC,KAAK,CAAC,2FAA2F,CAAC,CAC/FC,IAAI,CAACC,GAAG,IAAI;MACX,IAAI,CAACA,GAAG,CAACgB,EAAE,EAAE;QACX,MAAM,IAAIH,KAAK,CAAC,uBAAuBb,GAAG,CAACiB,MAAM,EAAE,CAAC;MACtD;MACA,OAAOjB,GAAG,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CACDnB,IAAI,CAACmB,IAAI,IAAI;MACZb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEY,IAAI,CAAC;MAClC,IAAI;QACF,OAAOC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVhB,OAAO,CAACiB,KAAK,CAAC,mBAAmB,EAAED,CAAC,CAAC;QACrChB,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;QACrC,MAAM,IAAIL,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,CACDd,IAAI,CAACK,IAAI,IAAI;MACZC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;MACvC,IAAIA,IAAI,CAACD,OAAO,IAAIoB,KAAK,CAACC,OAAO,CAACpB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAMqB,UAAU,GAAGrB,IAAI,CAACA,IAAI,CAACsB,GAAG,CAACC,IAAI,IAAI;UACvCtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;YAC1CsB,EAAE,EAAED,IAAI,CAACC,EAAE;YACXrC,KAAK,EAAEoC,IAAI,CAACpC,KAAK;YACjBsC,aAAa,EAAEF,IAAI,CAACG,KAAK;YACzBC,cAAc,EAAEvD,WAAW,CAACmD,IAAI,CAACG,KAAK;UACxC,CAAC,CAAC;UAEF,OAAO;YACLF,EAAE,EAAED,IAAI,CAACC,EAAE;YACXrC,KAAK,EAAEoC,IAAI,CAACpC,KAAK;YACjBmB,WAAW,EAAEiB,IAAI,CAACjB,WAAW,IAAIiB,IAAI,CAACK,OAAO;YAC7CA,OAAO,EAAEL,IAAI,CAACK,OAAO;YACrBF,KAAK,EAAEH,IAAI,CAACG,KAAK;YAAE;YACnBG,QAAQ,EAAEN,IAAI,CAACO,aAAa,IAAI,MAAM;YACtCC,IAAI,EAAER,IAAI,CAACS,UAAU,IAAIT,IAAI,CAACQ,IAAI;YAClCE,MAAM,EAAEV,IAAI,CAACW,WAAW,IAAI,OAAO;YACnCC,IAAI,EAAEZ,IAAI,CAACY,IAAI;YACfC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,CAAC;YACtBC,KAAK,EAAEd,IAAI,CAACc,KAAK,IAAI,CAAC;YACtBC,KAAK,EAAEf,IAAI,CAACe,KAAK,IAAI;UACvB,CAAC;QACH,CAAC,CAAC;QACFvD,YAAY,CAACsC,UAAU,CAAC;MAC1B,CAAC,MAAM;QACLtC,YAAY,CAAC,EAAE,CAAC;MAClB;MACAU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDiB,KAAK,CAACC,GAAG,IAAI;MACZV,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEP,GAAG,CAAC;MAC/C5B,YAAY,CAAC,EAAE,CAAC;MAChBU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8C,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMJ,KAAK,CAAC,8DAA8D,EAAE;QAC3F+C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE5B,IAAI,CAAC6B,SAAS,CAAC;UACnBC,MAAM,EAAE,mBAAmB;UAC3BC,OAAO,EAAEN,IAAI,CAAChB;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMuB,MAAM,GAAG,MAAMjD,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpC,IAAIkD,MAAM,CAAChD,OAAO,EAAE;QAClBhB,YAAY,CAACiE,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKgB,IAAI,CAAChB,EAAE,CAAC,CAAC;QACxD2B,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAC;MAC5C,CAAC,MAAM;QACLD,MAAM,CAACC,KAAK,CAAC,sCAAsC,GAAGL,MAAM,CAACM,OAAO,CAAC;MACvE;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDiC,MAAM,CAACC,KAAK,CAAC,oCAAoC,CAAC;IACpD;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC;IACA,IAAI;MACF,MAAM7D,KAAK,CAAC,0FAA0F6D,MAAM,EAAE,EAAE;QAC9Gd,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdjB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgB,KAAK,CAAC;IAClD;;IAEA;IACAsC,QAAQ,CAAC,cAAcD,MAAM,EAAE,CAAC;EAClC,CAAC;EAED,MAAME,WAAW,GAAG,MAAOjB,IAAI,IAAK;IAClC,IAAI;MACF;MACA9C,KAAK,CAAC,mCAAmC8C,IAAI,CAAChB,EAAE,QAAQ,EAAE;QACxDiB,MAAM,EAAE;MACV,CAAC,CAAC,CAAC/B,KAAK,CAACC,GAAG,IAAIV,OAAO,CAACiB,KAAK,CAAC,6BAA6B,EAAEP,GAAG,CAAC,CAAC;;MAElE;MACA,MAAM+C,QAAQ,GAAGlB,IAAI,CAACrD,KAAK,CACxBwE,WAAW,CAAC,CAAC,CACbnF,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBoF,IAAI,CAAC,CAAC;MAET,MAAMC,IAAI,GAAG,GAAGV,MAAM,CAACW,QAAQ,CAACC,MAAM,YAAYvB,IAAI,CAAChB,EAAE,UAAUkC,QAAQ,aAAalB,IAAI,CAACX,QAAQ,EAAE;;MAEvG;MACA,MAAMmC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,IAAI,CAAC;MACzCV,MAAM,CAACC,KAAK,CAAC,4CAA4C,CAAC;IAE5D,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CiC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAC;IAC5C;EACF,CAAC;EAED,MAAMe,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMrC,IAAI,GAAG,IAAIsC,IAAI,CAACD,UAAU,CAAC;IACjC,OAAOrC,IAAI,CAACuC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMnB,QAAQ,GAAGvF,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA,CAACxB,GAAG;IAACiI,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEtF9G,OAAA,CAAChB,MAAM;MAAC+H,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACR,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEO,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAN,QAAA,eACrI9G,OAAA,CAACf,OAAO;QAACwH,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACpC9G,OAAA,CAACxB,GAAG;UAACiI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9D9G,OAAA,CAACd,MAAM;YACLuI,GAAG,EAAE5G,MAAM,CAACE,IAAK;YACjB2G,GAAG,EAAC,MAAM;YACVjB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEe,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAG/E,CAAC,IAAK;cAAEA,CAAC,CAACgF,MAAM,CAACL,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFlI,OAAA,CAACvB,UAAU;YAAC0J,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEpB,KAAK,EAAE,cAAc;cAAEqB,QAAQ,EAAE;YAAG,CAAE;YAAAvB,QAAA,EACnFjG,MAAM,CAACG;UAAK;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNlI,OAAA,CAACnB,UAAU;UACTyJ,IAAI,EAAC,KAAK;UACVtB,KAAK,EAAC,SAAS;UACfuB,OAAO,EAAEA,CAAA,KAAMnH,cAAc,CAAC,IAAI,CAAE;UACpCqF,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eAEd9G,OAAA,CAACJ,QAAQ;YAACyI,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGTlI,OAAA,CAACZ,MAAM;MACLoJ,MAAM,EAAC,OAAO;MACdC,IAAI,EAAEtH,WAAY;MAClBuH,OAAO,EAAEA,CAAA,KAAMtH,cAAc,CAAC,KAAK,CAAE;MACrCqF,EAAE,EAAE;QAAEW,MAAM,EAAE;MAAK,CAAE;MAAAN,QAAA,eAErB9G,OAAA,CAACxB,GAAG;QAACiI,EAAE,EAAE;UAAEG,KAAK,EAAE,GAAG;UAAE+B,CAAC,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBAC5B9G,OAAA,CAACnB,UAAU;UACT0J,OAAO,EAAEA,CAAA,KAAMnH,cAAc,CAAC,KAAK,CAAE;UACrCqF,EAAE,EAAE;YAAEM,QAAQ,EAAE,UAAU;YAAE6B,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAE;UAC/C,cAAW,OAAO;UAAA/B,QAAA,eAElB9G,OAAA,CAACH,SAAS;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACblI,OAAA,CAACxB,GAAG;UAACiI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEuB,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBACxD9G,OAAA,CAACd,MAAM;YACLuI,GAAG,EAAE5G,MAAM,CAACE,IAAK;YACjB2G,GAAG,EAAC,MAAM;YACVjB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEe,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAG/E,CAAC,IAAK;cAAEA,CAAC,CAACgF,MAAM,CAACL,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFlI,OAAA,CAACvB,UAAU;YAAC0J,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEpB,KAAK,EAAE,cAAc;cAAEqB,QAAQ,EAAE;YAAG,CAAE;YAAAvB,QAAA,EACnFjG,MAAM,CAACG;UAAK;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNlI,OAAA,CAACX,OAAO;UAACoH,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BlI,OAAA,CAACV,KAAK;UAACyJ,OAAO,EAAE,CAAE;UAAAjC,QAAA,gBAChB9G,OAAA,CAACb,MAAM;YACLgJ,OAAO,EAAC,UAAU;YAClBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAElJ,OAAA,CAACP,QAAQ;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBK,OAAO,EAAEA,CAAA,KAAM;cACblD,QAAQ,CAAC,GAAG,CAAC;cACbjE,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YAAA0F,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlI,OAAA,CAACb,MAAM;YACLgJ,OAAO,EAAC,WAAW;YACnBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAElJ,OAAA,CAACN,YAAY;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAApB,QAAA,EAC7B;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlI,OAAA,CAACb,MAAM;YACLgJ,OAAO,EAAC,UAAU;YAClBnB,KAAK,EAAC,SAAS;YACfgC,SAAS;YACTvC,EAAE,EAAE;cAAEwC,aAAa,EAAE;YAAO,CAAE;YAC9BC,SAAS,eAAElJ,OAAA,CAACL,sBAAsB;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtCK,OAAO,EAAEA,CAAA,KAAM;cACbvD,MAAM,CAACyD,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC;cACnCrH,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YAAA0F,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETlI,OAAA,CAACxB,GAAG;MAACiI,EAAE,EAAE;QAAE0C,EAAE,EAAE,EAAE;QAAE9B,EAAE,EAAE,CAAC;QAAE+B,EAAE,EAAE;MAAE,CAAE;MAAAtC,QAAA,gBAEhC9G,OAAA,CAACxB,GAAG;QAACiI,EAAE,EAAE;UACPG,KAAK,EAAE,MAAM;UACbyC,UAAU,EAAE,SAAS;UACrBrC,KAAK,EAAE,SAAS;UAChBsC,SAAS,EAAE,QAAQ;UACnBlB,UAAU,EAAE,GAAG;UACfC,QAAQ,EAAE,EAAE;UACZkB,OAAO,EAAE,QAAQ;UACjBC,YAAY,EAAE,CAAC;UACfC,YAAY,EAAE,CAAC;UACfvC,YAAY,EAAE;QAChB,CAAE;QAAAJ,QAAA,GAAC,yBACsB,EAACnG,SAAS,CAAC+I,MAAM;MAAA;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eAERlI,OAAA,CAACxB,GAAG;QAACiI,EAAE,EAAE;UAAE8C,OAAO,EAAE,CAAC;UAAEI,aAAa,EAAE;QAAG,CAAE;QAAA7C,QAAA,EACxCzF,OAAO,gBACNrB,OAAA,CAACxB,GAAG;UAACiI,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEsC,cAAc,EAAE,QAAQ;YAAErC,UAAU,EAAE,QAAQ;YAAEb,SAAS,EAAE;UAAI,CAAE;UAAAI,QAAA,eAC3F9G,OAAA,CAACjB,gBAAgB;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJvH,SAAS,CAAC+I,MAAM,KAAK,CAAC,gBACxB1J,OAAA,CAACvB,UAAU;UAAC0J,OAAO,EAAC,IAAI;UAAC1B,EAAE,EAAE;YAAE6C,SAAS,EAAE,QAAQ;YAAEtC,KAAK,EAAE,UAAU;YAAEuC,OAAO,EAAE;UAAE,CAAE;UAAAzC,QAAA,EAAC;QAErF;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GAEbvH,SAAS,CAACwC,GAAG,CAAC,CAACkB,IAAI,EAAEwF,KAAK;UAAA,IAAAC,iBAAA;UAAA,oBACxB9J,OAAA,CAACtB,IAAI;YAAmC+H,EAAE,EAAE;cAAE+C,YAAY,EAAE,CAAC;cAAEO,SAAS,EAAE;YAAE,CAAE;YAAAjD,QAAA,eAC5E9G,OAAA,CAACxB,GAAG;cAACiI,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAE0C,aAAa,EAAE;kBAAEC,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAM;cAAE,CAAE;cAAApD,QAAA,gBAEvE9G,OAAA,CAACpB,SAAS;gBACRuL,SAAS,EAAC,KAAK;gBACf5B,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAACd,IAAI,CAAChB,EAAE,CAAE;gBACxCoD,EAAE,EAAE;kBACFG,KAAK,EAAE;oBAAEqD,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE;kBAAI,CAAC;kBAC9BvC,MAAM,EAAE;oBAAEsC,EAAE,EAAE,GAAG;oBAAEC,EAAE,EAAE;kBAAI,CAAC;kBAC5BE,SAAS,EAAE,OAAO;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACF9G,KAAK,EAAEtD,WAAW,CAACoE,IAAI,CAACd,KAAK,CAAE;gBAC/BmE,GAAG,EAAErD,IAAI,CAACrD,KAAM;gBAChB6G,OAAO,EAAG/E,CAAC,IAAK;kBAAEA,CAAC,CAACgF,MAAM,CAACL,GAAG,GAAG,2CAA2C;gBAAE;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eAEFlI,OAAA,CAACrB,WAAW;gBACV4J,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAACd,IAAI,CAAChB,EAAE,CAAE;gBACxCoD,EAAE,EAAE;kBACF6D,IAAI,EAAE,CAAC;kBACPf,OAAO,EAAE,CAAC;kBACVc,MAAM,EAAE;gBACV,CAAE;gBAAAvD,QAAA,gBAEF9G,OAAA,CAACvB,UAAU;kBAAC0J,OAAO,EAAC,IAAI;kBAACgC,SAAS,EAAC,IAAI;kBAAC1D,EAAE,EAAE;oBAAE+C,YAAY,EAAE,CAAC;oBAAEpB,UAAU,EAAE;kBAAI,CAAE;kBAAAtB,QAAA,EAC9EzC,IAAI,CAACrD;gBAAK;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEblI,OAAA,CAACxB,GAAG;kBAACiI,EAAE,EAAE;oBAAEa,OAAO,EAAE,MAAM;oBAAEiD,GAAG,EAAE,CAAC;oBAAEf,YAAY,EAAE,CAAC;oBAAEgB,QAAQ,EAAE;kBAAO,CAAE;kBAAA1D,QAAA,gBACtE9G,OAAA,CAAClB,IAAI;oBACH2L,KAAK,EAAEpG,IAAI,CAACX,QAAS;oBACrBgH,IAAI,EAAC,OAAO;oBACZ1D,KAAK,EAAC,SAAS;oBACfmB,OAAO,EAAC;kBAAU;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFlI,OAAA,CAACvB,UAAU;oBAAC0J,OAAO,EAAC,SAAS;oBAAC1B,EAAE,EAAE;sBAAEO,KAAK,EAAE,UAAU;sBAAE2D,SAAS,EAAE;oBAAS,CAAE;oBAAA7D,QAAA,GAC1Ed,UAAU,CAAC3B,IAAI,CAACT,IAAI,CAAC,EAAC,UAAG,EAACS,IAAI,CAACJ,KAAK,IAAI,CAAC,EAAC,QAC7C;kBAAA;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENlI,OAAA,CAACvB,UAAU;kBAAC0J,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAEO,KAAK,EAAE,UAAU;oBAAEwC,YAAY,EAAE;kBAAE,CAAE;kBAAA1C,QAAA,IAAAgD,iBAAA,GACpEzF,IAAI,CAAClC,WAAW,cAAA2H,iBAAA,uBAAhBA,iBAAA,CAAkBc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACvC;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEblI,OAAA,CAACxB,GAAG;kBAACiI,EAAE,EAAE;oBAAEa,OAAO,EAAE,MAAM;oBAAEiD,GAAG,EAAE;kBAAE,CAAE;kBAAAzD,QAAA,gBACnC9G,OAAA,CAACnB,UAAU;oBACT0J,OAAO,EAAGzF,CAAC,IAAK;sBACdA,CAAC,CAAC+H,eAAe,CAAC,CAAC,CAAC,CAAC;sBACrBzG,cAAc,CAACC,IAAI,CAAC;oBACtB,CAAE;oBACF2C,KAAK,EAAC,SAAS;oBACfhG,KAAK,EAAC,mBAAmB;oBAAA8F,QAAA,eAEzB9G,OAAA,CAACT,iBAAiB;sBAAAwI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACblI,OAAA,CAACnB,UAAU;oBACT0J,OAAO,EAAGzF,CAAC,IAAK;sBACdA,CAAC,CAAC+H,eAAe,CAAC,CAAC,CAAC,CAAC;sBACrBvF,WAAW,CAACjB,IAAI,CAAC;oBACnB,CAAE;oBACF2C,KAAK,EAAC,SAAS;oBACfhG,KAAK,EAAC,SAAS;oBAAA8F,QAAA,eAEf9G,OAAA,CAACR,SAAS;sBAAAuI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC,GApEG,SAAS7D,IAAI,CAAChB,EAAE,IAAIwG,KAAK,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqEhC,CAAC;QAAA,CACR;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlI,OAAA,CAACxB,GAAG;QAACiI,EAAE,EAAE;UACPM,QAAQ,EAAE,OAAO;UACjB+D,IAAI,EAAE,CAAC;UACPlC,KAAK,EAAE,CAAC;UACRmC,MAAM,EAAE,CAAC;UACT3D,MAAM,EAAE,IAAI;UACZE,OAAO,EAAE,OAAO;UAChB0D,eAAe,EAAE,OAAO;UACxBC,SAAS,EAAE,mBAAmB;UAC9BlB,SAAS,EAAE;QACb,CAAE;QAAAjD,QAAA,eACA9G,OAAA,CAACxB,GAAG;UAACiI,EAAE,EAAE;YACPa,OAAO,EAAE,MAAM;YACfsC,cAAc,EAAE,cAAc;YAC9BrC,UAAU,EAAE,QAAQ;YACpBI,MAAM,EAAE,EAAE;YACVN,EAAE,EAAE;UACN,CAAE;UAAAP,QAAA,gBACA9G,OAAA,CAACxB,GAAG;YACF+J,OAAO,EAAEA,CAAA,KAAMvD,MAAM,CAACW,QAAQ,CAACuF,IAAI,GAAG,GAAI;YAC1CC,SAAS,EAAE,mBAAmBlK,WAAW,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA6F,QAAA,gBAElE9G,OAAA;cAAGmL,SAAS,EAAE,+BAA+BlK,WAAW,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;YAAG;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1GlI,OAAA,CAACvB,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE/F,WAAW,KAAK,CAAC,GAAG,cAAc,GAAG;cAAiB,CAAE;cAAA6F,QAAA,EAAC;YAEjI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENlI,OAAA,CAACxB,GAAG;YACF+J,OAAO,EAAEA,CAAA,KAAMrH,cAAc,CAAC,CAAC,CAAE;YACjCiK,SAAS,EAAE,mBAAmBlK,WAAW,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA6F,QAAA,gBAElE9G,OAAA;cAAGmL,SAAS,EAAE,iCAAiClK,WAAW,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;YAAG;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GlI,OAAA,CAACvB,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE/F,WAAW,KAAK,CAAC,GAAG,cAAc,GAAG;cAAiB,CAAE;cAAA6F,QAAA,EAAC;YAEjI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENlI,OAAA,CAACxB,GAAG;YAAC2M,SAAS,EAAC,wBAAwB;YAAArE,QAAA,gBACrC9G,OAAA;cAAGmL,SAAS,EAAC;YAA+C;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjElI,OAAA,CAACvB,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACgD,SAAS,EAAC,kBAAkB;cAAC1E,EAAE,EAAE;gBAAEO,KAAK,EAAE,cAAc;gBAAEoB,UAAU,EAAE;cAAI,CAAE;cAAAtB,QAAA,EAAC;YAE3G;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxH,EAAA,CA9aID,KAAK;EAAA,QAuLQX,WAAW;AAAA;AAAAsL,EAAA,GAvLxB3K,KAAK;AAgbX,eAAeA,KAAK;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}