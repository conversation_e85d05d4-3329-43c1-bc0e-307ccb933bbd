<?php
// Get users from database with JWT tokens
function getUsers() {
    try {
        $conn = getConnection();

        // Try to get users with JWT tokens from users table
        try {
            $stmt = $conn->prepare("
                SELECT id, name, email, role, status, created_at,
                       jwt_token,
                       jwt_expires_at as token_expires,
                       CASE
                           WHEN jwt_expires_at > NOW() THEN 'active'
                           WHEN jwt_expires_at IS NOT NULL THEN 'expired'
                           ELSE 'none'
                       END as token_status,
                       CASE
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'Hari ini'
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'Minggu ini'
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'Bulan ini'
                           ELSE 'Lebih lama'
                       END as registration_period
                FROM users
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $tokenError) {
            // If jwt_token columns don't exist, get users without tokens
            error_log("JWT token columns error (this is OK if columns don't exist): " . $tokenError->getMessage());

            $stmt = $conn->prepare("
                SELECT id, name, email, role, status, created_at,
                       NULL as jwt_token,
                       NULL as token_expires,
                       'none' as token_status,
                       CASE
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'Hari ini'
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'Minggu ini'
                           WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'Bulan ini'
                           ELSE 'Lebih lama'
                       END as registration_period
                FROM users
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        error_log("Error getting users: " . $e->getMessage());
        return [];
    }
}

// Get user statistics
function getUserStats() {
    try {
        $conn = getConnection();
        $stats = [];

        // Total users
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['total'] = $result ? $result['total'] : 0;

        // Users today
        $stmt = $conn->prepare("SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['today'] = $result ? $result['today'] : 0;

        // Users this week
        $stmt = $conn->prepare("SELECT COUNT(*) as week FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['week'] = $result ? $result['week'] : 0;

        // Users this month
        $stmt = $conn->prepare("SELECT COUNT(*) as month FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['month'] = $result ? $result['month'] : 0;

        // Admin count
        $stmt = $conn->prepare("SELECT COUNT(*) as admin FROM users WHERE role = 'admin'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['admin'] = $result ? $result['admin'] : 0;

        // User count
        $stmt = $conn->prepare("SELECT COUNT(*) as user FROM users WHERE role = 'user'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['user'] = $result ? $result['user'] : 0;

        return $stats;
    } catch (PDOException $e) {
        return [
            'total' => 0,
            'today' => 0,
            'week' => 0,
            'month' => 0,
            'admin' => 0,
            'user' => 0
        ];
    }
}

$users = getUsers();
$userStats = getUserStats();


?>

<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <!-- Total Users -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Pengguna</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($userStats['total']); ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Today's Registrations -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Hari Ini</p>
                    <p class="text-2xl font-bold text-green-600"><?php echo number_format($userStats['today']); ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-plus text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- This Week -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Minggu Ini</p>
                    <p class="text-2xl font-bold text-purple-600"><?php echo number_format($userStats['week']); ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-week text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- This Month -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Bulan Ini</p>
                    <p class="text-2xl font-bold text-orange-600"><?php echo number_format($userStats['month']); ?></p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Admin Users -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Admin</p>
                    <p class="text-2xl font-bold text-red-600"><?php echo number_format($userStats['admin']); ?></p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-shield text-red-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Regular Users -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">User</p>
                    <p class="text-2xl font-bold text-indigo-600"><?php echo number_format($userStats['user']); ?></p>
                </div>
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user text-indigo-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 md:px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                <h3 class="text-lg font-semibold text-gray-900">Daftar Pengguna</h3>

                <!-- Mobile Filters -->
                <div class="flex flex-col space-y-3 md:hidden">
                    <!-- Search -->
                    <div class="relative">
                        <input
                            type="text"
                            id="searchUsers"
                            placeholder="Cari pengguna..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <!-- Role Filter -->
                        <select id="roleFilter" class="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Role</option>
                            <option value="admin">Admin</option>
                            <option value="user">User</option>
                        </select>
                        <!-- Status Filter -->
                        <select id="statusFilter" class="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                    <!-- Add User Button Mobile -->
                    <a href="?page=add-user" class="w-full mt-3 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Tambah User
                    </a>
                </div>

                <!-- Desktop Filters -->
                <div class="hidden md:flex items-center space-x-3">
                    <!-- Search -->
                    <div class="relative">
                        <input
                            type="text"
                            id="searchUsersDesktop"
                            placeholder="Cari pengguna..."
                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <!-- Role Filter -->
                    <select id="roleFilterDesktop" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Role</option>
                        <option value="admin">Admin</option>
                        <option value="user">User</option>
                    </select>
                    <!-- Status Filter -->
                    <select id="statusFilterDesktop" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                    <!-- Add User Button -->
                    <a href="?page=add-user" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Tambah User
                    </a>
                </div>
            </div>
        </div>

        <!-- Responsive Table with Horizontal Scroll -->
        <div class="overflow-x-auto" style="min-width: 100%;">
            <div style="min-width: 900px;">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary focus:ring-primary">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">Nama</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[180px]">Email</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Role</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">Token JWT</th>
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Tgl Daftar</th>
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Aksi</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="usersTableBody">
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="9" class="px-4 py-12 text-center text-gray-500">
                                    <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
                                    <p class="text-lg font-medium">Belum ada pengguna</p>
                                    <p class="text-sm">Pengguna yang mendaftar akan muncul di sini</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $index => $user): ?>
                                <tr class="hover:bg-gray-50 user-row"
                                    data-user-id="<?php echo $user['id']; ?>"
                                    data-name="<?php echo strtolower($user['name']); ?>"
                                    data-email="<?php echo strtolower($user['email']); ?>"
                                    data-role="<?php echo $user['role']; ?>"
                                    data-status="<?php echo $user['status'] ?? 'active'; ?>">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <input type="checkbox" class="user-checkbox rounded border-gray-300 text-primary focus:ring-primary" value="<?php echo $user['id']; ?>">
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $index + 1; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                                                <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900 truncate max-w-[120px]"><?php echo htmlspecialchars($user['name']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <div class="truncate max-w-[150px]"><?php echo htmlspecialchars($user['email']); ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            <?php echo $user['role'] === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'; ?>">
                                            <i class="fas <?php echo $user['role'] === 'admin' ? 'fa-user-shield' : 'fa-user'; ?> mr-1"></i>
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            <?php
                                            $status = $user['status'] ?? 'active';
                                            if ($status === 'active') echo 'bg-green-100 text-green-800';
                                            elseif ($status === 'inactive') echo 'bg-gray-100 text-gray-800';
                                            elseif ($status === 'suspended') echo 'bg-red-100 text-red-800';
                                            else echo 'bg-yellow-100 text-yellow-800';
                                            ?>">
                                            <i class="fas <?php
                                            if ($status === 'active') echo 'fa-check-circle';
                                            elseif ($status === 'inactive') echo 'fa-pause-circle';
                                            elseif ($status === 'suspended') echo 'fa-ban';
                                            else echo 'fa-question-circle';
                                            ?> mr-1"></i>
                                            <?php echo ucfirst($status); ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($user['jwt_token'] && $user['token_status'] === 'active'): ?>
                                            <div class="flex items-center space-x-1">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Aktif
                                                </span>
                                                <button onclick="copyToken('<?php echo htmlspecialchars($user['jwt_token']); ?>')"
                                                        class="text-blue-600 hover:text-blue-800 p-1 rounded"
                                                        title="Copy Token">
                                                    <i class="fas fa-copy text-xs"></i>
                                                </button>
                                            </div>
                                        <?php elseif ($user['jwt_token'] && $user['token_status'] === 'expired'): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-times-circle mr-1"></i>Kadaluarsa
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                <i class="fas fa-minus-circle mr-1"></i>Tidak Ada
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center text-sm text-gray-500">
                                        <div class="flex flex-col">
                                            <span class="text-xs"><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></span>
                                            <span class="text-xs text-gray-400"><?php echo date('H:i', strtotime($user['created_at'])); ?></span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <div class="flex items-center justify-center space-x-1">
                                            <button onclick="viewUser(<?php echo $user['id']; ?>)"
                                                    class="text-blue-600 hover:text-blue-900 p-1.5 rounded hover:bg-blue-50"
                                                    title="Lihat Detail">
                                                <i class="fas fa-eye text-sm"></i>
                                            </button>
                                            <button onclick="editUser(<?php echo $user['id']; ?>)"
                                                    class="text-green-600 hover:text-green-900 p-1.5 rounded hover:bg-green-50"
                                                    title="Edit">
                                                <i class="fas fa-edit text-sm"></i>
                                            </button>
                                            <?php if ($user['role'] !== 'admin'): ?>
                                            <button onclick="deleteUser(<?php echo $user['id']; ?>)"
                                                    class="text-red-600 hover:text-red-900 p-1.5 rounded hover:bg-red-50"
                                                    title="Hapus">
                                                <i class="fas fa-trash text-sm"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Table Footer -->
        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <div class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                <div class="text-sm text-gray-700 text-center sm:text-left">
                    Menampilkan <span class="font-medium"><?php echo count($users); ?></span> pengguna
                </div>
                <div class="flex items-center justify-center space-x-2">
                    <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50" disabled>
                        <i class="fas fa-chevron-left mr-1"></i> <span class="hidden sm:inline">Sebelumnya</span>
                    </button>
                    <span class="px-3 py-1 text-sm bg-primary text-white rounded">1</span>
                    <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50" disabled>
                        <span class="hidden sm:inline">Selanjutnya</span> <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Mobile optimizations */
@media (max-width: 768px) {
    .user-card {
        transition: all 0.2s ease;
    }

    .user-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Improve touch targets */
    .user-card button {
        min-width: 44px;
        min-height: 44px;
    }

    /* Better spacing for mobile */
    .space-y-4 > * + * {
        margin-top: 1rem;
    }

    /* Responsive text */
    .truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* Loading animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Smooth transitions */
.user-row, .user-card {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.user-row:hover, .user-card:hover {
    transform: translateY(-1px);
}

/* Better button styling */
button[title] {
    position: relative;
}

/* Responsive statistics cards */
@media (max-width: 640px) {
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-6 {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-6 {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Setup event listeners
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // Search functionality
    const mobileSearch = document.getElementById('searchUsers');
    const desktopSearch = document.getElementById('searchUsersDesktop');

    if (mobileSearch) {
        mobileSearch.addEventListener('input', filterUsers);
    }

    if (desktopSearch) {
        desktopSearch.addEventListener('input', function() {
            document.getElementById('searchUsers').value = this.value;
            filterUsers();
        });
    }

    // Filter functionality
    const mobileRoleFilter = document.getElementById('roleFilter');
    const mobileStatusFilter = document.getElementById('statusFilter');
    const desktopRoleFilter = document.getElementById('roleFilterDesktop');
    const desktopStatusFilter = document.getElementById('statusFilterDesktop');

    if (mobileRoleFilter) mobileRoleFilter.addEventListener('change', filterUsers);
    if (mobileStatusFilter) mobileStatusFilter.addEventListener('change', filterUsers);

    if (desktopRoleFilter) {
        desktopRoleFilter.addEventListener('change', function() {
            document.getElementById('roleFilter').value = this.value;
            filterUsers();
        });
    }

    if (desktopStatusFilter) {
        desktopStatusFilter.addEventListener('change', function() {
            document.getElementById('statusFilter').value = this.value;
            filterUsers();
        });
    }
}

// Role filter
document.getElementById('roleFilter').addEventListener('change', function() {
    filterUsers();
});

// Status filter
document.getElementById('statusFilter').addEventListener('change', function() {
    filterUsers();
});

// Filter function for table rows
function filterUsers() {
    const selectedRole = document.getElementById('roleFilter').value;
    const selectedStatus = document.getElementById('statusFilter').value;
    const searchTerm = document.getElementById('searchUsers').value.toLowerCase();

    // Filter table rows
    const rows = document.querySelectorAll('.user-row');
    rows.forEach(row => {
        const role = row.dataset.role;
        const status = row.dataset.status;
        const name = row.dataset.name;
        const email = row.dataset.email;

        const roleMatch = selectedRole === '' || role === selectedRole;
        const statusMatch = selectedStatus === '' || status === selectedStatus;
        const searchMatch = searchTerm === '' || name.includes(searchTerm) || email.includes(searchTerm);

        if (roleMatch && statusMatch && searchMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Sync desktop filters with mobile filters
    const desktopRoleFilter = document.getElementById('roleFilterDesktop');
    const desktopStatusFilter = document.getElementById('statusFilterDesktop');
    const desktopSearch = document.getElementById('searchUsersDesktop');

    if (desktopRoleFilter && desktopRoleFilter.value !== selectedRole) {
        desktopRoleFilter.value = selectedRole;
    }
    if (desktopStatusFilter && desktopStatusFilter.value !== selectedStatus) {
        desktopStatusFilter.value = selectedStatus;
    }
    if (desktopSearch && desktopSearch.value !== searchTerm) {
        desktopSearch.value = searchTerm;
    }
}

// Select all functionality
const selectAllCheckbox = document.getElementById('selectAll');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
}

// Copy JWT Token
function copyToken(token) {
    navigator.clipboard.writeText(token).then(function() {
        showNotification('Token JWT berhasil disalin!', 'success');
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = token;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Token JWT berhasil disalin!', 'success');
    });
}

// User actions
function viewUser(userId) {
    // Create modal for viewing user details
    fetch(`simple_api.php?action=get_user&id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserModal(data.user, 'view');
            } else {
                showNotification('Gagal memuat data pengguna', 'error');
            }
        })
        .catch(error => {
            showNotification('Terjadi kesalahan saat memuat data', 'error');
        });
}

function editUser(userId) {
    // Redirect to edit page
    window.location.href = `?page=edit-user&id=${userId}`;
}

function deleteUser(userId) {
    if (confirm('Apakah Anda yakin ingin menghapus pengguna ini? Tindakan ini tidak dapat dibatalkan.')) {
        fetch('simple_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'delete_user',
                id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Pengguna berhasil dihapus', 'success');
                // Remove row from table
                const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                if (row) {
                    row.remove();
                }
                // Refresh page after 1 second
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Gagal menghapus pengguna', 'error');
            }
        })
        .catch(error => {
            showNotification('Terjadi kesalahan saat menghapus pengguna', 'error');
        });
    }
}

// Show user modal for viewing details
function showUserModal(user, mode = 'view') {
    const modalHtml = `
        <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-semibold text-gray-900">Detail Pengguna</h3>
                        <button onclick="closeUserModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            ${user.name.substring(0, 2).toUpperCase()}
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">${user.name}</h4>
                            <p class="text-gray-600">${user.email}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                                <i class="fas ${user.role === 'admin' ? 'fa-user-shield' : 'fa-user'} mr-2"></i>
                                ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                user.status === 'active' ? 'bg-green-100 text-green-800' :
                                user.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                                'bg-red-100 text-red-800'
                            }">
                                <i class="fas ${
                                    user.status === 'active' ? 'fa-check-circle' :
                                    user.status === 'inactive' ? 'fa-pause-circle' :
                                    'fa-ban'
                                } mr-2"></i>
                                ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Daftar</label>
                            <p class="text-gray-900">${new Date(user.created_at).toLocaleDateString('id-ID', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ID Pengguna</label>
                            <p class="text-gray-900 font-mono">#${user.id}</p>
                        </div>
                    </div>

                    ${user.jwt_token ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">JWT Token</label>
                            <div class="bg-gray-50 p-3 rounded-lg border">
                                <code class="text-xs font-mono text-gray-800 break-all">${user.jwt_token}</code>
                                <button onclick="copyToken('${user.jwt_token}')" class="ml-2 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="closeUserModal()" class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                        Tutup
                    </button>
                    <button onclick="editUser(${user.id}); closeUserModal();" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeUserModal() {
    const modal = document.getElementById('userModal');
    if (modal) {
        modal.remove();
    }
}

// Add User functionality moved to separate page (add-user.php)

</script>
